# show-web

### 介绍

远方直播助手管理中台

### 一、项目功能

-   🚀 采用 React 最新生态技术找开发：React18、React-Router v6、React-Hooks
-   🚀 采用 pnpm 作为依赖管理工具
-   🚀 使用 Ant-Design、ant-Design/pro-components 作为组件库
-   🚀 使用 styled-components 作为 css in js 解决方案
-   🚀 采用 Vite4 作为项目开发、打包工具（配置了 Gzip 打包、跨域代理、打包预览工具……）
-   🚀 整个项目集成了 TypeScript
-   🚀 使用 redux 做状态管理，集成 immer、react-redux、redux-persist、redux-toolkit 开发
-   🚀 使用 TypeScript 对 Axios 整个二次封装 （全局错误拦截、常用请求封装、全局请求 Loading、取消重复请求……）
-   🚀 使用 自定义高阶组件 进行路由权限拦截（403 页面）、页面按钮权限配置
-   🚀 支持 React-Router v6 路由懒加载配置、菜单手风琴模式、无限级菜单、多标签页、面包屑导航
-   🚀 使用 Prettier 统一格式化代码，集成 Eslint、Stylelint 代码校验规范（项目规范配置）
-   🚀 使用 husky、lint-staged、commitlint、commitizen、cz-git 规范提交信息（项目规范配置）

### 二、安装使用步骤

-   **代码地址**

```text

# Gitlab https://git.ifengqun.com/fq-front/fq-business-systems/fq-shop-web

git clone https://git.ifengqun.com/fq-front/fq-business-systems/fq-shop-web.git

```

-   **Install：**

node-version > 16.14.0

vscode-extension: vscode-styled-components

```text

npm i pnpm -g

pnpm install

windows下提示系统禁止运行脚本时 在windows搜索栏搜 powerShell 以管理员身份运行

执行 set-ExecutionPolicy RemoteSigned 命令  选择是(Y)

```

-   **Run：**

```text

pnpm dev

```

-   **Build：**

```text
# 开发环境
pnpm run build:dev

# 测试环境
pnpm run build:test

# 生产环境
pnpm run build:pro
```

-   **Lint：**

```text
# eslint 检测代码
pnpm run lint:eslint

# prettier 格式化代码
pnpm run lint:prettier

# stylelint 格式化样式
lint:stylelint
```


