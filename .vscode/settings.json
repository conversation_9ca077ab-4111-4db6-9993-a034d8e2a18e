{
    // 保存自动格式化代码
    "editor.formatOnSave": true,
    // 默认格式化工具
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "stylelint.enable": true,
    // 开启stylelint自动修复
    "editor.codeActionsOnSave": {
        "source.fixAll.stylelint": "explicit",
        "source.fixAll.eslint": "explicit"
    },

    // 自动 import 语句中路径的首选样式
    "typescript.preferences.importModuleSpecifier": "shortest",

    // 配置stylelint检查的文件类型范围
    "stylelint.validate": ["css", "less", "postcss", "scss", "sass"],
    "files.eol": "\n",
    "cSpell.words": [
        "antd",
        "anticon",
        "Appstore",
        "Biao",
        "bqddxxwqmfncffacvbpkuxvwvqrhln",
        "breakline",
        "browserslist",
        "cnpm",
        "commitlint",
        "contentright",
        "doclock",
        "easymock",
        "esbuild",
        "fangda",
        "fastmock",
        "Geeker",
        "Gitee",
        "iconfont",
        "immer",
        "juejin",
        "loglevel",
        "malefemale",
        "nprogress",
        "persistor",
        "Prefixs",
        "reduxjs",
        "screenfull",
        "Sider",
        "styl",
        "stylelint",
        "stylelintignore",
        "stylelintrc",
        "suoxiao",
        "truetype",
        "zhongyingwen",
        "zhuti"
    ],
    "typescript.tsdk": "node_modules/typescript/lib",
    "[typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    }
}
