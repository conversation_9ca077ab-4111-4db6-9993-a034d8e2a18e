/**
 * @Owners zp
 * @Title vite 配置文件
 */
import react from '@vitejs/plugin-react-swc';
import { resolve } from 'path';
import { defineConfig, loadEnv, type ConfigEnv, type UserConfig } from 'vite';
import viteCompression from 'vite-plugin-compression';
import eslintPlugin from 'vite-plugin-eslint';
import { createHtmlPlugin } from 'vite-plugin-html';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

import { isDev as isDevFn, wrapperEnv } from './src/utils/uViteEnv';

// @see: https://vitejs.dev/config/
export default defineConfig(async (mode: ConfigEnv): Promise<UserConfig> => {
    const env = loadEnv(mode.mode, process.cwd());
    const viteEnv = wrapperEnv(env);
    // const isTest = isTestFn(mode.mode);
    const isDev = isDevFn(mode.mode);
    const version = process.env.npm_config_version_id || '';

    const hash = '-[hash]'
    const appVersion = isDev ? '' : version

    return {
        // base: "/",
        // alias config
        resolve: {
            alias: {
                '@': resolve(__dirname, './src'),
                '@utils': resolve(__dirname, './src/utils'),
                '@consts': resolve(__dirname, './src/consts'),
                '@helpers': resolve(__dirname, './src/helpers'),
                '@apis': resolve(__dirname, './src/apis'),
                '@services': resolve(__dirname, './src/services'),
            },
        },
        // global css
        css: {
            preprocessorOptions: {},
        },
        // server config
        server: {
            host: '0.0.0.0', // 服务器主机名，如果允许外部访问，可设置为"0.0.0.0"
            port: viteEnv.VITE_PORT,
            open: viteEnv.VITE_OPEN,
            cors: true,
            // https: false,
            // 代理跨域（mock 不需要配置，这里只是个事列）
            proxy: {
                '/mock': {
                    target: 'https://mock.mengxuegu.com/mock/650bb20e96175e3c6ac1bf8d',
                    changeOrigin: true,
                },
                '/api/live': {
                    target: 'https://newtest-gateway.ifengqun.com/',
                    changeOrigin: true,
                    rewrite: (path: string) => path.replace(/^\/api/, ''),
                },
                '/platform': {
                    target: 'https://test-mall.ifengqun.com',
                    changeOrigin: true,
                    rewrite: (path: string) => path.replace(/^\/platform/, ''),
                },
                '/api/goods': {
                    target: 'https://newtest-gateway.ifengqun.com/',
                    changeOrigin: true,
                    rewrite: (path: string) => path.replace(/^\/api/, ''),
                },
            },
        },
        // plugins
        plugins: [
            react(),
            createHtmlPlugin({
                inject: {
                    data: {
                        title: viteEnv.VITE_GLOB_APP_TITLE,
                        version: appVersion
                    },
                },
            }),
            // 使用 svg 图标
            createSvgIconsPlugin({
                iconDirs: [resolve(process.cwd(), 'src/assets/icons')],
                symbolId: 'icon-[dir]-[name]',
            }),
            // EsLint 报错信息显示在浏览器界面上
            eslintPlugin(),
            // gzip compress
            viteEnv.VITE_BUILD_GZIP &&
            viteCompression({
                verbose: true,
                disable: false,
                threshold: 10240,
                algorithm: 'gzip',
                ext: '.gz',
            }),
        ],
        esbuild: {
            pure: viteEnv.VITE_DROP_CONSOLE ? ['console.log', 'debugger'] : [],
        },
        // build configure
        build: {
            outDir: 'dist',
            // esbuild 打包更快，但是不能去除 console.log，去除 console 使用 terser 模式
            minify: 'esbuild',
            // minify: "terser",
            // terserOptions: {
            // 	compress: {
            // 		drop_console: viteEnv.VITE_DROP_CONSOLE,
            // 		drop_debugger: true
            // 	}
            // },
            rollupOptions: {
                output: {
                    // Static resource classification and packaging
                    chunkFileNames: `assets/js/[name]${hash}.js`,
                    entryFileNames: `assets/js/[name]${hash}.js`,
                    assetFileNames: `assets/[ext]/[name]${hash}.[ext]`,
                },
            },
        },
    };
});
