pipeline {
    agent {
        kubernetes {
            cloud 'kubernetes'
            label 'agent-k8s'
            defaultContainer 'jnlp'
            // yamlFile './podTemplate.yaml'
            yaml """
            apiVersion: v1
            kind: Pod
            metadata:
            name: jenkins-slave
            namespace: kube-ops
            labels:
                app: jenkins-slave
            spec:
            serviceAccount: jenkins2
            containers:
            - name: jnlp
                image: 'registry.cn-shenzhen.aliyuncs.com/imghub/jnlp-slave-node:v18'
                imagePullPolicy: 'Always'
                tty: true
                securityContext:
                privileged: true
                env:
                - name: CONTAINER_ENV_VAR
                value: jnlp
                resources:
                limits:
                    cpu: 4
                    memory: 8Gi
                requests:
                    cpu: 2
                    memory: 4Gi
                volumeMounts:
                - name: repo-kube-config
                mountPath: /root/.kube
                - name: repo-node-cache
                mountPath: /root/.npm
                - name: repo-modules-cache
                mountPath: /root/.node_modules
            - name: docker
                image: docker
                command:
                - cat
                tty: true
                env:
                - name: CONTAINER_ENV_VAR
                value: docker
                volumeMounts:
                - name: repo-docker-sock
                mountPath: /var/run/docker.sock
                - name: repo-docker-certs
                mountPath: /etc/docker/certs.d
                readOnly: false
            volumes:
            - name: repo-docker-sock
                hostPath:
                path: /var/run/docker.sock
            - name: repo-docker-certs
                hostPath:
                path: /etc/docker/certs.d
            - name: repo-kube-config
                hostPath:
                path: /root/.kube
            - name: repo-node-cache
                hostPath:
                path: /root/.npm
            - name: repo-modules-cache
                hostPath:
                path: /root/.wasp-web-modules
            """
        }
    }

    environment {
        /* project */
        environ="test"
        branch = "${params.branch}"
        project = 'wasp-web'
        repo_url = 'http://ztgitlab.meiji8888.com/meiji-front/wasp-web.git'

        /* container */
        k8s_namespace = "mj-front-${environ}"
        docker_registry = 'meiji-zt-registry-vpc.cn-shenzhen.cr.aliyuncs.com'
        chart_registry = 'meiji-zt-chart-vpc.cn-shenzhen.cr.aliyuncs.com'
        chart_repository = "${chart_registry}/zt/app"
        image_repository = "${docker_registry}/mj-front/${project}"
        // image_tag = "${environ}"
    }

    parameters {
        string (defaultValue: 'master', description: '代码分支', name: 'branch', trim: true)
    }

    options {
        timestamps()
        skipDefaultCheckout()
        disableConcurrentBuilds()
        timeout(time: 1, unit: 'HOURS')
    }

    stages {
        stage('checkout') {
            steps {
                echo 'checkout'
                sh '''
                    git --version
                    node --version
                    java -version
                    python3 --version
                    helm version
                    helm plugin list
                    miniprogram-ci --version
                    ossutil --version
                    # npm config ls -l
                    ls /root/.npm
                    # ls /root/.node_modules
                    # ls /etc/docker/certs.d
                    # node -e 'console.log(process.env)'
                '''
                /* checkout scm */
                git branch: "${branch}", credentialsId: 'gitlab', url: "${repo_url}"
            }
        }

        stage('build') {
            steps {
                script {
                    env.build_time = sh(script: 'date +%Y%m%d%H%M%S', returnStdout: true).trim()
                    env.git_version = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                    env.git_tag = "release-${project}-${git_version}#${BUILD_NUMBER}"
                    env.image_tag = "${environ}-${git_version}-${env.build_time}"

                    def (author, comment) = sh(script: 'git --no-pager log --pretty=format:%an::%s --no-merges -1', returnStdout: true).trim().split('::')
                    def poetry = readJSON(text: sh(script: "curl https://v1.jinrishici.com/all", returnStdout: true))

                    env.git_author = author
                    env.git_comment = comment
                    env.poetry_title = "${poetry.origin} -${poetry.author}"
                    env.poetry_content = "${poetry.content}"
                }

								sh """
                    pnpm install --registry=https://registry.npm.taobao.org
                    npm run build:test --version_id=${git_version}
                """

                withCredentials([[
                    $class: 'UsernamePasswordMultiBinding',
                    credentialsId: 'dockerHub',
                    usernameVariable: 'docker_username',
                    passwordVariable: 'docker_password'
                ]]) {
                    container('docker') {
                        sh 'docker -v'

                        sh """
                            docker login ${docker_registry} -u ${docker_username} -p ${docker_password}
                            docker build -t ${image_repository}:${image_tag} .
                            docker push ${image_repository}:${image_tag}
                        """
                    }
                }
            }
        }


        stage('deploy') {
            steps {
                echo 'deploy server'
                withCredentials([[
                    $class: 'UsernamePasswordMultiBinding',
                    credentialsId: 'dockerHub',
                    usernameVariable: 'HELM_REPO_USERNAME',
                    passwordVariable: 'HELM_REPO_PASSWORD'
                ]]) {
                    sh "helm repo add meiji-zt acr://${chart_repository} --username=${HELM_REPO_USERNAME} --password=${HELM_REPO_PASSWORD}"
                    sh """
                        helm upgrade --install -f ./deploy/chart/values-${environ}.yaml \
                        --set api.image.repository=${image_repository} \
                        --set api.image.tag=${image_tag} \
                        --set client.version=${git_version} \
                        --namespace=${k8s_namespace} \
                        ${project} ./deploy/chart
                    """
                }
            }
        }
    }

    post {
        success {
            dingtalk (
                robot: 'publish-notice',
                type: 'MARKDOWN',
                title: "${project} 发布成功",
                text: [
                    "# [${project} 发布成功](${BUILD_URL})",
                    '---',
                    "发布环境：${environ}  ",
                    "代码分支：${branch}  ",
                    "最近提交：@${git_author}: ${git_comment}  ",
                    "构建状态：[${JOB_NAME}#${BUILD_ID}](${BUILD_URL}) <font color=#52c41a>成功</font>  "
                ],
                atAll: false
            )
        }

        failure {
            dingtalk (
                robot: 'publish-notice',
                type: 'MARKDOWN',
                title: "${project} 发布失败",
                text: [
                    "# [${project} 发布失败](${BUILD_URL})",
                    '---',
                    "发布环境：${environ}  ",
                    "代码分支：${branch}  ",
                    "最近提交：@${git_author}: ${git_comment}  ",
                    "构建状态：[${JOB_NAME}#${BUILD_ID}](${BUILD_URL}) <font color=#ff0000>失败</font>  ",
                ],
                atAll: false
            )
        }
    }
}
