/**
 * @Owners haoyang
 * @Title eslint 规则调整与补充
 */

{
    "rules": {
        // 禁止导入模块子依赖
        // TODO turn on
        "import/no-internal-modules": "off",
        // 禁止未引用的依赖导入
        // TODO turn on
        "import/no-unassigned-import": [
            "off",
            {
                "allow": ["**/*.scss", "**/*.css", "**/*.less"]
            }
        ],
        // 禁止不合法的类型导入
        "@typescript-eslint/consistent-type-imports": [
            "error",
            {
                "fixStyle": "inline-type-imports"
            }
        ],
        // 禁止未使用的变量
        "@typescript-eslint/no-unused-vars": [
            "error",
            {
                "argsIgnorePattern": "^_",
                "varsIgnorePattern": "^_"
            }
        ],
        // TODO turn on
        // 文件标题模板
        "header/header": [
            "warn",
            "block",
            {
                "pattern": "^\\*\n \\* @Owners .+\n \\* @Title .+(\n \\* @Details(( |\n)(((?!\\* @).)|\n)+)?)?\n $"
            },
            0
        ],
        // TODO remove
        // 禁止ts禁用规则指令
        "@typescript-eslint/ban-ts-comment": "warn",
        // @see https://github.com/import-js/eslint-plugin-import/blob/main/docs/rules/no-extraneous-dependencies.md
        "import/no-extraneous-dependencies": ["off", { "includeInternal": true, "includeTypes": true }],
        // 禁止魔法数字
        "@typescript-eslint/no-magic-numbers": "off",
        // 禁止位运算
        "no-bitwise": "off"
    }
}
