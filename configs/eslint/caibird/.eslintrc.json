{
    /**
     * @Owners cmZhou
     */
    "rules": {
        // "@typescript-eslint/semi": "off",
        // "@typescript-eslint/comma-dangle": "off",
        // "@typescript-eslint/ban-types": "off",
        // "@typescript-eslint/indent": "off",
        // "@typescript-eslint/member-delimiter-style": "off",

        // "@typescript-eslint/no-require-imports": "off",
        // "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/no-unsafe-call": "off",
        "@typescript-eslint/no-unsafe-assignment": "off",
        "@typescript-eslint/restrict-template-expressions": "off",
        "@typescript-eslint/no-unsafe-return": "off",
        "@typescript-eslint/no-implicit-any-catch": "off",
        // "@typescript-eslint/lines-between-class-members": "off",
        "@typescript-eslint/prefer-optional-chain": "off",
        // "@typescript-eslint/ban-tslint-comment": "off",
        // "@typescript-eslint/no-explicit-any": "off",
        // "@typescript-eslint/sort-type-union-intersection-members": "off",
        "@typescript-eslint/consistent-indexed-object-style": "off",
        // "@typescript-eslint/no-magic-numbers": "off",
        // "@typescript-eslint/explicit-member-accessibility": "off",
        // "no-async-promise-executor": "off",
        "@typescript-eslint/promise-function-async": "off",
        // "no-bitwise": "off",
        "@typescript-eslint/consistent-type-assertions": "off",
        "@typescript-eslint/require-await": "off",
        "import/order": "off",
        // "import/no-extraneous-dependencies": "off",
        "react/jsx-no-bind": "off",
        "@typescript-eslint/no-shadow": "off",
        "@typescript-eslint/no-useless-constructor": "off",
        "@typescript-eslint/no-confusing-void-expression": "off",
        "prefer-template": "off",
        // "no-multi-spaces": "off",
        "@typescript-eslint/member-ordering": "off",
        "@typescript-eslint/no-redeclare": "off",
        // "import/no-internal-modules": "off",
        // "import/no-unassigned-import": "off",
        "@typescript-eslint/restrict-plus-operands": "off",
        "@typescript-eslint/object-curly-spacing": "off",
        "@typescript-eslint/prefer-includes": "off",
        // "spaced-comment": "off",
        "array-callback-return": "off",
        "@typescript-eslint/no-unnecessary-type-assertion": "off",
        "@typescript-eslint/no-implied-eval": "off",
        "@typescript-eslint/non-nullable-type-assertion-style": "off",
        "no-extra-boolean-cast": "off",
        "jsdoc/newline-after-description": "off",
        "@typescript-eslint/tslint/config": [
            "error",
            {
                "lintFile": "./configs/eslint/caibird/base/tslint.json"
            }
        ]
    },
    "overrides": [
        {
            "files": ["./src/**"],
            "rules": {
                "no-restricted-imports": [
                    "error",
                    {
                        "patterns": []
                    }
                ]
            }
        }
    ]
}
