{
    /**
     * @Owners cmZhou
     * @version 6.1.3
     */
    "defaultSeverity": "error",
    "extends": ["tslint:all", "tslint-react", "tslint-eslint-rules"],
    "jsRules": true,
    "rules": {
        //核心的共162个规则
        //#region TypeScript-specific

        //函数重载要排在一起
        "adjacent-overload-signatures": true,
        //禁用 @ts-ignore
        "ban-ts-ignore": true,
        //禁用类型
        "ban-types": [true, ["Object"], ["String"], ["Number"]],
        //必需申明类成员可见性。如public private
        "member-access": [true, "check-accessor", "check-parameter-property"],
        //类成员类型排序规则
        "member-ordering": [
            true,
            {
                "order": [
                    "private-static-field",
                    "private-static-method",
                    "protected-static-field",
                    "protected-static-method",
                    "public-static-field",
                    "public-static-method",

                    "private-constructor",
                    "protected-constructor",
                    "public-constructor",

                    "private-instance-field",
                    "protected-instance-field",
                    "public-instance-field",

                    "private-instance-method",
                    "protected-instance-method",
                    "public-instance-method"
                ]
            }
        ],
        //不允许使用any(很多地方要用)
        "no-any": false,
        //不允许使用空接口(空接口经常使用)
        "no-empty-interface": false,
        //禁止 for in（没必要禁用）
        "no-for-in": false,
        //不允许副作用的import
        "no-import-side-effect": false,
        //对于初始化为数字，字符串或布尔值的变量或参数，禁止显式类型声明。
        "no-inferrable-types": true,
        //禁止内部module，用namespace代替
        "no-internal-module": true,
        //不允许在变量赋值之外使用常数数值
        "no-magic-numbers": false,
        //统一使用es6的模块风格，不允许使用module和namespace(可以使用)
        "no-namespace": false,
        //不允许使用非空断言
        // "no-non-null-assertion": true,
        //不允许重新给传入的参数赋值
        "no-parameter-reassignment": true,
        //禁止reference导入
        "no-reference": false,
        //禁止无意义的类型断言。【编译时检查】
        "no-unnecessary-type-assertion": true,
        //禁用reuqire
        "no-var-requires": false,
        //禁用非箭头函数(很多时候还是需要用到常规函数)
        "only-arrow-functions": false,
        //当不需要使用索引时，只能使用for-of来循环
        "prefer-for-of": false,
        //返回promise时必需加上async。【编译时检查】
        "promise-function-async": false,
        //必需申明类型(有时候不是必需的)
        "typedef": false,
        //类型定义时的空格格式
        "typedef-whitespace": [
            true,
            {
                "call-signature": "nospace",
                "index-signature": "nospace",
                "parameter": "nospace",
                "property-declaration": "nospace",
                "variable-declaration": "nospace"
            },
            {
                "call-signature": "onespace",
                "index-signature": "onespace",
                "parameter": "onespace",
                "property-declaration": "onespace",
                "variable-declaration": "onespace"
            }
        ],
        //如果重载可以合并，则报错
        "unified-signatures": true,

        //#endregion

        //#region Functionality

        //await 只能操作promise。【编译时检查】
        "await-promise": [true, "Bluebird"],
        //禁用逗号运算符
        "ban-comma-operator": true,
        //禁用方法(暂时没有要禁用的方法)
        "ban": false,
        //if for while 需要大括号，如果在同一行则不需要。
        "curly": [true, "ignore-same-line"],
        //必需使用hasOwnProperty来过滤 for in(这个不是必需的)
        "forin": false,
        //禁止 new Function
        "function-constructor": true,
        //禁止直接导入整个模块的模块列表(用了ts-import-plugin 这里不需要了)
        "import-blacklist": false,
        //标签仅允许出现在合理的位置
        "label-position": true,
        //禁用arguments
        "no-arg": true,
        //没用awiat或返回值时不能用async(暂时没必要)
        "no-async-without-await": false,
        //禁止位运算符
        "no-bitwise": false,
        //禁止在条件语句中赋值
        "no-conditional-assignment": true,
        //禁用console方法(console很常用)
        "no-console": false,
        //不允许使用Number,String,Boolean等构造函数
        "no-construct": true,
        //不允许使用debugger
        "no-debugger": true,
        //不允许多次调用supper
        "no-duplicate-super": true,
        //不允许switch中出现相同的case
        "no-duplicate-switch-case": true,
        //同一个块内不允许重复用var申明同一个变量(var被禁用，这里不需要开启了)
        "no-duplicate-variable": false,
        //不允许使用动态delete（没必要）
        "no-dynamic-delete": false,
        //不允许空的块
        "no-empty": [true, "allow-empty-catch", "allow-empty-functions"],
        //不允许使用eval
        "no-eval": true,
        //必需处理promise异常，【编译时检查】(太严格)
        "no-floating-promises": false,
        //禁止使用for in迭代数组。【编译时检查】
        "no-for-in-array": true,
        //没有在package.json引入的模块不允许import
        "no-implicit-dependencies": false,
        //当泛型方法无法根据参数推断类型时，必需显式指定泛型的类型或设置默认类型，【编译时检查】
        "no-inferred-empty-object-type": true,
        //禁止在非字符串模板中使用${}(普通字符串中用${}时很容易知道是常规字符串，这里没必要禁用)
        "no-invalid-template-strings": false,
        //禁止在普通的函数里用this，只能在类的方法里使用(没必要)
        "no-invalid-this": false,
        //不能在接口和类中使用new
        "no-misused-new": true,
        //不允许使用null，全部用undefined代替(没必要)
        "no-null-keyword": false,
        //禁止null和undefined的联合类型(没必要)
        "no-null-undefined-union": false,
        //禁止对字面量进行类型断言，any除外
        "no-object-literal-type-assertion": false,
        //禁止直接在if语句中使用promise做为真假表达式 【编译时检查】
        "no-promise-as-boolean": [
            true,
            {
                "promise-classes": ["Bluebird"]
            }
        ],
        //禁用一些全局变量
        "no-restricted-globals": [true],
        //禁止不必要的return await
        "no-return-await": true,
        //不允许有覆盖变量申明的情况，如(a) => { let a;  }
        "no-shadowed-variable": [
            false
            // {
            //     "class": false,
            //     "enum": true,
            //     "function": true,
            //     "interface": true,
            //     "namespace": true,
            //     "typeAlias": true,
            //     "typeParameter": true
            // }
        ],
        //禁止稀疏数组
        "no-sparse-arrays": true,
        //禁止不必要的字符串属性访问
        "no-string-literal": false,
        //throw不能直接抛出字符
        "no-string-throw": true,
        //禁止导入子模块的模块列表(暂时没有模块禁止使用子模块)
        "no-submodule-imports": false,
        //当case没有break或return时报错(ts本身已经检查了)
        "no-switch-case-fall-through": false,
        //禁止对两个相同的字面量进行比较
        "no-tautology-expression": true,
        //禁止给this定义引用，可以用来解构
        "no-this-assignment": [
            true,
            {
                "allow-destructuring": true
            }
        ],
        //调用未bind的类方法时报错。【编译时检查】
        "no-unbound-method": [true, "ignore-static"],
        //不允许不必要的class，通常指只有静态成员的类
        "no-unnecessary-class": [false, "allow-empty-class", "allow-constructor-only"],
        //禁止不安全的any调用。【编译时检查】
        "no-unsafe-any": false,
        //禁止不安全的finally。比如在finally里有return,break,continue等操作
        "no-unsafe-finally": true,
        //禁止无作用的表达式
        "no-unused-expression": [true, "allow-fast-null-checks"],
        //在var定义前不允许使用变量。（由于通常都是禁用var，所以这里不需要开启）【编译时检查】
        "no-use-before-declare": false,
        //不允许使用var
        "no-var-keyword": true,
        //不能return void。【编译时检查】(太严格了)
        "no-void-expression": false,
        //能用三目表达式实现赋值时，就不要用if else (没必要)
        "prefer-conditional-expression": false,
        //不允许使用Object.assign(),要用扩展运算符
        "prefer-object-spread": false,
        //必需传radix(当前的项目和环境不需要传)
        "radix": false,
        //加法运算符只能用在number、string中。【编译时检查】
        "restrict-plus-operands": false,
        //禁止在static方法中使用this
        "static-this": true,
        //真假表达式中能用的类型。【编译时检查】
        "strict-boolean-expressions": false,
        //禁止不合理的比较(暂时不用)
        "strict-comparisons": false,
        //禁止隐式的toString【编译时检查】（暂时没必要）
        "strict-string-expressions": false,
        //禁止无效的类型判断,哪当 typeof '111' === 'string'就是无效的。【编译时检查】(这个暂时关闭)
        "strict-type-predicates": false,
        //switch必需有default(有时不需要)
        "switch-default": false,
        //必需使用===和!==
        "triple-equals": [true, "allow-null-check"],
        //typeof的字符串必需正确(ts会检查)
        "typeof-compare": false,
        //禁止空构造函数
        "unnecessary-constructor": true,
        //当传参为参数默认值时，会警告。【编译时检查】
        "use-default-type-parameter": true,
        //必需用isNaN，不通知用 xxx === NaN
        "use-isnan": true,
        //#endregion

        //#region Maintainability
        //限制if for等循环语句在一个块内的使用次数(这个没有必需限制)
        "cyclomatic-complexity": false,
        //使用废弃api时报错。【编译时检查】
        "deprecation": false, // TODO 有bug 寻找替代
        // 禁止在返回类型和泛型以外使用void类型(暂时没必要)
        "invalid-void": false,
        //文件最后必需有换行符
        "eofline": true,
        //控制缩进用空格还是制表符【这里不会检查缩进值，要用tslint-eslint的ter-indent来控制】
        "indent": [true, "spaces", 4],
        //强制统一换行符
        "linebreak-style": true,
        //控制一个文件内类的数量(没必要限制)
        "max-classes-per-file": false,
        //控制文件行数(暂时没必要限制)
        "max-file-line-count": false,
        //控制每行的长度(暂时没必要限制)
        "max-line-length": false,
        //禁用默认导出(没必要禁用)
        "no-default-export": false,
        //禁用默认导入(没必要禁用)
        "no-default-import": false,
        //禁止同文件对同一个模块导入多次
        "no-duplicate-imports": true,
        //禁止同文件使用可合并的命名空间(有时候需要分开)
        "no-mergeable-namespace": false,
        //禁用reuqire
        "no-require-imports": false,
        //对象字段赋值要正确排序(目前类型定义时无法控制keys的排序，所以没必要限制了)
        "object-literal-sort-keys": false,
        //能使用const的必需使用const
        "prefer-const": true,
        //能使用readonly的成员必需使用readonly。【编译时检查】
        "prefer-readonly": true,
        //禁止尾部逗号
        "trailing-comma": false,
        //#endregion

        //#region Style
        //强制缩进对齐
        "align": true,
        //数组定义风格统一
        "array-type": [true, "array"],
        //当箭头函数的单一参数不需要指定类型时，必需删除括号。其它情况必需加括号
        "arrow-parens": [true, "ban-single-arg-parens"],
        //箭头函数的返回值可以简写时必需简写。如 x => {return x} 必需写成 x => x;
        "arrow-return-shorthand": [true, "multiline"],
        //二元表达式的顺序必需一致。如 x + 1 不能写成 1 + x;
        "binary-expression-operand-order": true,
        //如果为单一的函数类型，不能用接口来定义
        "callable-types": true,
        //类名和接口名必需大写开头(没必要)
        "class-name": false,
        //注释格式统一
        "comment-format": [true, "check-space"],
        //限制注释类型（没必要限制）
        "comment-type": false,
        //指定必需要加JSDoc的成员(暂时没有一定要加jsdoc的成员)【编译时检查】
        "completed-docs": false,
        //指定编码格式为utf-8
        "encoding": true,
        //指定文件必需有某个文件头(暂时不需要加)
        "file-header": false,
        //文件名一致(暂时不需要)
        "file-name-casing": false,
        // 禁止++i之类的一元运算符
        "increment-decrement": [true, "allow-post"],
        //import 的变量之间要有空格
        "import-spacing": true,
        //接口必需以I开头(不需要)
        "interface-name": false,
        //尽可能的使用接口(用类型别名有时候更好管理)
        "interface-over-type-literal": false,
        //jsdoc格式
        "jsdoc-format": [true, "check-multiline-start"],
        //默认导入和导出的名称必需一致【编译时检查】
        "match-default-export-name": false,
        //return前强制空行(没必要)
        "newline-before-return": false,
        //链式调用必需为单独的行(没必要)
        "newline-per-chained-call": false,
        //new 一个对象时必需加上括号
        "new-parens": true,
        //类型断言全部用as type
        "no-angle-bracket-type-assertion": true,
        //当一个变量或表达式的类型仅为boolean时，不能和true或false进行比较【编译时检查】
        "no-boolean-literal-compare": true,
        //最多允许的空行。默认一行
        "no-consecutive-blank-lines": true,
        //不允许不规则的空白。如mac上的 alt+空格
        "no-irregular-whitespace": true,
        //不能在构造函数中直接用参数生成类成员(可以用，用起来更方便)
        "no-parameter-properties": false,
        //ts中已经有的类型标识，不能在jsdoc中注释了
        "no-redundant-jsdoc": true,
        //当用import导入时，不允许使用<reference types>
        "no-reference-import": true,
        //不允许尾随空格
        "no-trailing-whitespace": true,
        //禁止无用的回调。如 x => f(x) 要改成 f。(有时需要)
        "no-unnecessary-callback-wrapper": {
            "severity": "warn"
        },
        //禁止将let或var定义的变量初始化为undefined(没必要)
        "no-unnecessary-initializer": false,
        //当在同一个命名空间内时，禁用无效的命名空间前缀【编译时检查】
        "no-unnecessary-qualifier": true,
        //使数字的格式统一。如小数点后不能有无效的0
        "number-literal-format": true,
        //统一对象的属性的格式。如统一为当不必要时都不允许加引号
        "object-literal-key-quotes": [true, "as-needed"],
        //强制使用或禁用对象属性赋值的简写。如是否强制 a = { x: x } 写成 a = { x };
        "object-literal-shorthand": true,
        //else catch finnaly { 必需要和前面的表达式在同一行
        "one-line": [true, "check-open-brace", "check-catch", "check-else", "check-finally", "check-whitespace"],
        //一个申明语句中不能申明多个变量
        "one-variable-per-declaration": [true, "ignore-for-loop"],
        //import 语句的风格
        "ordered-imports": [
            true,
            {
                "import-sources-order": "lowercase-last",
                "named-imports-order": "lowercase-last",
                "module-source-path": "full",
                "grouped-imports": true
            }
        ],
        //当一个类方法不使用this时发出警告(有时候用不到this)
        "prefer-function-over-method": false,
        //统一函数的类型定义格式。foo: () => void 替换为 foo(): void
        "prefer-method-signature": true,
        //当if中有对一个变量进行了多次 === 判断时，必需使用switch
        "prefer-switch": [true, 3],
        //当有3个以上字符串相加时，必需使用字符串模板。
        "prefer-template": [true, "allow-single-concat"],
        //当使用while更好时必需要改为while
        "prefer-while": true,
        //控制字符串引号的样式统一，模板字符串必需要有参数插入。
        "quotemark": [true, "single", "avoid-template"],
        //当返回类型是void时不能return undefined;。当返回类型是undefined时不能return;【编译时检查】
        "return-undefined": true,
        //强制使用分号结束语句
        "semicolon": false,
        //函数变量前的空格检查
        "space-before-function-paren": [
            true,
            {
                "anonymous": "always",
                "named": "never",
                "asyncArrow": "always",
                "method": "never",
                "constructor": "never"
            }
        ],
        //括号内的空格
        "space-within-parens": [true, 0],
        //控制switch最后一个语句是否必需以break结束
        "switch-final-break": true,
        //类型定义必需使用分号结尾
        "type-literal-delimiter": false,
        //禁止无用的bind
        "unnecessary-bind": true,
        //禁用无用的else
        "unnecessary-else": [
            true,
            {
                "allow-else-if": true
            }
        ],
        //控制变量名风格(不需要)
        "variable-name": false,
        //空格检查
        "whitespace": [
            true,
            "check-branch",
            "check-decl",
            "check-operator",
            "check-module",
            "check-preblock",
            "check-separator",
            "check-rest-spread",
            "check-type",
            /* "check-typecast", */
            "check-type-operator"
            /* ,"check-postbrace" */
        ],
        //#endregion

        //#region tslint-react@5.0.0 18个规则
        //多行jsx要对齐(没必要)
        "jsx-alignment": false,
        //禁用jsx元素(暂时不需要禁用)
        "jsx-ban-elements": false,
        //禁用jsx属性(暂时不需要禁用)
        "jsx-ban-props": false,
        //在jsx中的boolean为true时是否要省略
        "jsx-boolean-value": [true, "never"],
        //控制jsx各类元素是否必需用{} (暂时没必要)
        "jsx-curly-brace-presence": false,
        //jsx大括号之间的空间是否需要
        "jsx-curly-spacing": [true, "never"],
        //jsx属性的等于号前后是否需要空格
        "jsx-equals-spacing": [true, "never"],
        //.map语句中返回的jsx必需要有key(有时候会误报)
        "jsx-key": true,
        //禁止在jsx中bind方法
        "jsx-no-bind": true,
        //禁止在jsx里用lambda来传递方法
        "jsx-no-lambda": false,
        //禁止jsx中的多行js表达式(没必要)
        "jsx-no-multiline-js": false,
        //禁止将字符串传给ref
        "jsx-no-string-ref": true,
        //禁止jsx中使用未包装的字符串(没必要限制)
        "jsx-use-translation-function": false,
        //没有子节点的jsx必需自闭合
        "jsx-self-close": true,
        //自闭合前是否要有空格
        "jsx-space-before-trailing-slash": true,
        //用" "代替&nbsp;(没必要)
        "jsx-whitespace-literal": false,
        //多行jsx必需用括号括起来(没必要)
        "jsx-wrap-multiline": false,
        //禁止不必要的fragment
        "react-no-unnecessary-fragment": true,
        //#endregion

        //#region tslint-eslint-rules@5.4.0 38个规则
        //禁止在条件表达式里出现常数
        "no-constant-condition": true,
        //在正则里不允许出现不可见字符
        "no-control-regex": true,
        //不允许出现重复的case(tslint已有)
        "no-duplicate-case": false,
        //正则正不允许使用空的中括号
        "no-empty-character-class": true,
        //禁止对catch中的e重新赋值
        "no-ex-assign": true,
        //禁止不必要的boolean转换(有时候需要转换)
        "no-extra-boolean-cast": false,
        //禁止不必要的分号(tslint已有)
        "no-extra-semi": false,
        //禁止在内部申明可提升的变量或函数(这里只限制函数，因为var已经被禁用)
        "no-inner-declarations": [true, "functions"],
        //禁止在RegExp中初始化无效的正则
        "no-invalid-regexp": true,
        //禁止不规则的空白(tslint已有)
        "ter-no-irregular-whitespace": false,
        //禁止在正则中直接输入多个空格
        "no-regex-spaces": true,
        //禁止稀疏数组(tslint已有)
        "ter-no-sparse-arrays": false,
        //禁止容易误解的多行表达式(已经强制使用分号了，这种情况不会出现了)
        "no-unexpected-multiline": false,
        //jsdoc字段名和必填项(以后开始写jsdoc再设定)
        "valid-jsdoc": false,
        //typeof的字符串必需正确(ts会检查)
        "valid-typeof": false,
        //不能有多的空格
        "no-multi-spaces": [
            true,
            {
                "exceptions": {
                    "VariableDeclaration": false,
                    "PropertyAssignment": false,
                    "BinaryExpression": false
                }
            }
        ],
        //禁用__proto__
        "ter-no-proto": true,
        //禁止在url中用javascript:
        "ter-no-script-url": true,
        //不允许与自身比较
        "ter-no-self-compare": true,
        //必需要处理有err的回调(这个没必要这么严格)
        "handle-callback-err": false,
        //数组的值和[]之间的是否需要空格
        "array-bracket-spacing": [true, "never"],
        //单行块{}之间是否有空格
        "block-spacing": [true, "always"],
        //控制else catch finnaly等是否和}在同一行(tslint已有)
        "brace-style": false,
        //禁止在用中括号访问属性时加上空格
        "ter-computed-property-spacing": [true, "never"],
        //函数调用时与()之间是否允许有空格
        "ter-func-call-spacing": [true, "never"],
        //控制缩进
        "ter-indent": false,
        //控制每行的长度(暂时没必要限制)
        "ter-max-len": false,
        //定义变量后是否必需空出一行(不需要，因为有时候变量在最后一行)
        "ter-newline-after-var": false,
        //禁止空格和制表符混用(已经限制只能用空格)
        "ter-no-mixed-spaces-and-tabs": false,
        //大括号内是否有空格
        "object-curly-spacing": [true, "always"],
        //块的首尾是否允许空行
        "ter-padded-blocks": [true, "never"],
        //对import进行排序(tslint已有)
        "sort-imports": false,
        //()内是否有空格(tslint已有)
        "space-in-parens": false,
        //禁用tab
        "ter-no-tabs": false,
        //箭头函数是否需要大括号(tslint已有)
        "ter-arrow-body-style": false,
        //箭头函数的参数是否必需用括号(tslint已有)
        "ter-arrow-parens": false,
        //箭头函数是否需要箭头前后的空格(tslint已有)
        "ter-arrow-spacing": false,
        //必需用箭头函数作为回调
        "ter-prefer-arrow-callback": true
        //#endregion
    },
    "rulesDirectory": [],
    "linterOptions": {
        "exclude": []
    }
}
