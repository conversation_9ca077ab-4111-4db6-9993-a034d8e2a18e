{
    /**
     * @Owners cmZhou
     * @version 4.7.3
     */
    "compileOnSave": false,
    //compilerOptions总共107个配置
    "compilerOptions": {
        //#region 基本配置(12)
        "charset": "utf8",
        "disableReferencedProjectLoad": false, //禁用多项目自动加载
        "disableSizeLimit": false, //禁用对工程中非TS文件的大小的限制。
        "jsx": "react-jsx", //jsx类型。
        "jsxImportSource": "react", //导入jsx依赖的库
        "module": "CommonJS", //模块系统。// TODO
        // "moduleDetection": "auto", // 设置检查模块的模式
        "moduleResolution": "Node", //模块解析规范。 // TODO
        // "moduleSuffixes": [""], // 定义模块查找的标识
        //"plugins": [], //语言服务插件
        //"rootDir": "src", //设置编译的根目录。所有待编译的文件都要在这个目录下。
        "target": "ES2020", //指定ECMAScript目标版本 "ES3"（默认）。 // TODO
        //#endregion

        //#region 控制台(10)
        "diagnostics": false, //显示诊断信息。
        "extendedDiagnostics": false, //显示详细诊断信息。
        "listEmittedFiles": false, //打印出编译后生成文件的名字。
        "listFiles": false, //编译过程中打印文件名。
        "locale": "zh-CN", //显示错误信息时使用的语言。
        "noErrorTruncation": true, //不截断错误消息。
        "preserveWatchOutput": true, //在watch模式下不自动清除控制台
        "pretty": true, //给错误和消息设置样式，使用颜色和上下文。
        "showConfig": false, //显示最终的tsconfig。
        "traceResolution": false, //生成模块解析日志信息。
        //#endregion

        //#region 映射(3)
        //"baseUrl": ".", //非相对模块的基础路径，和paths配合使用。
        "paths": {}, //模块名到基于baseUrl的路径映射的列表。
        //"rootDirs": [], //根（root）文件夹列表，表示运行时组合工程结构的内容。
        //#endregion

        //#region 筛选(15)
        "allowJs": true, //是否编译js。
        "assumeChangesOnlyAffectDirectDependencies": false, //是否仅重新编译有变化的文件和直接依赖项（可提高编译速度）
        //"disableSolutionSearching": false, //在大型复合项目中可以开启，提高编译速度
        "disableSourceOfProjectReferenceRedirect": false, //编译时禁用项目源引用跳转（可提高编译速度）
        "lib": ["DOM", "ESNext"], //编译过程中需要引入的库文件的列表。
        "maxNodeModuleJsDepth": 0, //node_modules依赖的最大搜索深度并加载JavaScript文件。仅适用于 --allowJs。
        "noLib": false, //编译时不包含默认的库文件（lib.d.ts）。
        "noResolve": false, //不把 /// <reference``>或模块导入的文件加到编译文件列表。
        "preserveSymlinks": false, //不处理符号链接。
        "resolveJsonModule": false, //允许导入.json文件。
        "skipDefaultLibCheck": false, //忽略标记了 /// <reference no-default-lib="true"/> 的.d.ts文件
        "skipLibCheck": false, //忽略所有.d.ts文件检查
        "stripInternal": false, //编译时跳过 /** @internal */ 注解的代码
        //"typeRoots": [], //要包含的类型声明文件路径列表。   
        //"types": [], //要包含的类型声明文件名列表。
        //#endregion

        //#region 错误检查(30)
        "allowSyntheticDefaultImports": true, //允许从没有设置默认导出的模块中默认导入，要同时开启esModuleInterop，否则仅能用于类型检查。
        "allowUmdGlobalAccess": false, // 允许将模块导出到 UMC 全局对象。
        "allowUnreachableCode": false, //不报告执行不到的代码错误。
        "allowUnusedLabels": false, //不报告未使用的标签错误。
        "checkJs": false, //检查js文件中的错误，和allowJs配合使用。
        "downlevelIteration": false, //es5和es3支持iterables。
        "esModuleInterop": true, //使commonjs的能使用es6的默认导出和导入。
        "exactOptionalPropertyTypes": false, // 可选字段不再默认可接受undefined类型 // TODO
        "experimentalDecorators": true, //是否启用es7注解。
        "forceConsistentCasingInFileNames": true, //禁止对同一个文件的不一致的引用。
        "isolatedModules": false, //每个文件都必需是一个模块。 // TODO
        "keyofStringsOnly": false, // keyof 只会返回string类型而不是string | number。
        "noFallthroughCasesInSwitch": true, //报告switch语句的fallthrough错误。（即，不允许switch的case语句贯穿）。
        "noImplicitAny": true, //在表达式和声明上有隐含的any类型时报错。
        "noImplicitOverride": true, // 禁止隐式重写类成员（必需显示用override声明）
        "noImplicitReturns": true, //不是函数的所有返回路径都有返回值时报错。
        "noImplicitThis": true, //当this表达式的值为any类型的时候，生成一个错误。
        "noPropertyAccessFromIndexSignature": true, // 禁止在索引类型中直接读取props
        "noStrictGenericChecks": false, //禁用在函数类型里对泛型签名进行严格检查。
        "noUncheckedIndexedAccess": false, // 开启后会强制所有索引类型为可空类型 // TODO
        "noUnusedLocals": true, //若有未使用的局部变量则抛错。
        "noUnusedParameters": true, //若有未使用的参数则抛错。
        "strict": true, //启用所有严格类型检查选项。启用 --strict相当于启用 --noImplicitAny, --noImplicitThis, --alwaysStrict， --strictNullChecks和 --strictFunctionTypes和--strictPropertyInitialization。
        "strictBindCallApply": true, // 开启严格的bind和apply检查
        "strictFunctionTypes": true, //启用函数参数的抗变检查。
        "strictNullChecks": true, //在严格的 null检查模式下， null和 undefined值不包含在任何类型里，只允许用它们自己和any来赋值（有个例外， undefined可以赋值到void）。
        "strictPropertyInitialization": true, //检查类成员是否被初始化，要求同时启用strictNullChecks。
        "suppressExcessPropertyErrors": false, //阻止对对象字面量的额外属性检查。ts中对象字面量会对额外属性进行检查，开启这个选项就会绕过这个检查。
        "suppressImplicitAnyIndexErrors": false, //阻止--noImplicitAny对缺少索引签名的索引对象报错。
        "useUnknownInCatchVariables": true, // catch(e) 中e的类型默认为unknown
        //#endregion

        //#region 注入(12)
        "alwaysStrict": true, //编译后在文件头部加上use strict。与noImplicitUseStrict冲突。
        "emitBOM": false, //在输出文件的开头加入BOM头（UTF-8 Byte Order Mark）。
        "emitDecoratorMetadata": true, //给源码里的装饰器声明加上设计类型元数据。Reflect.metadata
        "importHelpers": true, //从 tslib 导入辅助工具函数（比如 __extends， __rest等）。
        //"jsxFactory": "React.createElement", //指定生成目标为react JSX时，使用的JSX工厂函数。和reactNamespace冲突。
        //"jsxFragmentFactory": "React.Fragment", //指定生成JSX片段时，使用的工厂函数。
        "newLine": "lf", //当生成文件时指定行结束符： "crlf"（windows）或 "lf"（unix）。
        "noEmitHelpers": false, //不在输出文件中生成用户自定义的帮助函数代码，如 __extends。importHelpers为true的话该选项无效。
        "noImplicitUseStrict": false, //模块输出中不包含 "use strict"指令。
        "preserveConstEnums": false, //保留 const enum声明。
        // "reactNamespace": "React", //当目标为生成 "react" JSX时，指定 createElement和 __spread的调用对象
        "removeComments": true, //刪除所有注释
        //#endregion

        //#region 输出配置(21)
        "composite": false, //确保TypeScript可以找到引用项目的输出以编译项目。
        "declaration": false, //生成相应的 .d.ts文件。
        //"declarationDir": "", //.d.ts文件文件输出目录。
        "declarationMap": false, //生成.d.ts的Map文件。
        "emitDeclarationOnly": false, //仅输出.d.ts文件。
        //"generateCpuProfile": "profile.cpuprofile", //指定路径下生成CPU配置文件。
        "importsNotUsedAsValues": "error", //当导入了没使用的值或仅使用类型且没申明type时会如何处理
        "incremental": true, // 使用buildinfo记录上次编译信息，并优化编译速度。
        "inlineSourceMap": true, //将输出的map文件合并到代码文件中。
        "inlineSources": false, //将代码信息输出到sourcemaps信息中，要求设置了--inlineSourceMap或 --sourceMap属性。
        "listFilesOnly": false, //仅打印参与构建的文件名
        //"mapRoot": "", //指定map文件路径。该路径用于代码文件查找map文件
        "noEmit": false, //不生成输出文件。
        "noEmitOnError": true, //报错时不生成输出文件。
        //"outDir": ".tsc", //输出目录。
        //"outFile": "", //将输出文件合并为一个文件。合并的顺序是根据传入编译器的文件顺序和 ///<reference``>和 import的文件顺序决定的。用于AMD 和 System
        "preserveValueImports": false, // 保留未使用的import
        "sourceMap": false, //生成相应的Map文件。
        //"sourceRoot": "", //指定原文件路径。该路径用于map文件查找原文件
        //"tsBuildInfoFile": "", //指定buildinfo文件存储路径，默认在outDir下
        "useDefineForClassFields": true //使用 Object.defineProperty 定义类属性
        //#endregion

        //#region watch配置(4)
        //"watch": false,
        //"watchFile": "dynamicPriorityPolling",
        //"watchDirectory": "dynamicPriorityPolling",
        //"fallbackPolling": "dynamicPriority",
        //#endregion
    },

    //#region 其它选项
    //"buildOptions": {},
    //"include": []
    //"extends": "",
    //"files": [],
    //"exclude": [],
    //"references": []
    //"ts-node": {},
    //"typeAcquisition": {}
    //"watchOptions":{}
    //#endregion

    //#region 其它命令 
    /*
        --all 显示所有编译选项
        --generateTrace 生成编译栈
        --help,-h 显示帮助信息
        --init 初始化ts项目
        --locale 设置编译时的language
        --project,-p 指定编译时的tsconfig.json
        --showConfig 仅打印编译配置
        --version 显示tsc版本
        --build,-b 构建一个或多个相互依赖的项目
        --clean 删除构建输出
        --dry 显示将要构建的内容
        --force 强制构建所有项目
        --verbose,-v 开启详细日志

        --excludeDirectories 设置不需要监听的目录
        --excludeFiles 设置不需要监听的文件
        --synchronousWatchDirectory 不支持递归监听时，启用同步监听
    */
    //#endregion
}