{"name": "wasp-web", "private": true, "version": "1.6.0", "scripts": {"dev": "vite", "dev:no-cache": "vite --force", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build:pro": "vite build --mode production", "preview": "vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.tsx ./src", "lint:eslint-no-fix": "eslint --ext .js,.ts,.tsx .", "lint:prettier": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "commit": "git pull && git add -A && git-cz && git push"}, "dependencies": {"@ant-design/icons": "^5.6.0", "@ant-design/pro-components": "^2.8.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^1.9.2", "@tencentcloud/chat": "^3.5.2", "ahooks": "^3.8.4", "antd": "^5.23.4", "axios": "^0.27.2", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.7", "decimal.js": "^10.4.3", "dotenv": "^16.0.3", "driver.js": "^0.9.8", "echarts": "^5.6.0", "hls.js": "^1.6.0", "immer": "^9.0.15", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "lodash": "^4.17.21", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "qs": "^6.10.5", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-is": "^18.2.0", "react-quill": "^2.0.0", "react-redux": "^8.0.2", "react-router-dom": "^6.8.1", "react-transition-group": "^4.4.2", "react-virtualized": "^9.22.6", "redux-persist": "^6.0.0", "redux-promise": "^0.6.0", "redux-thunk": "^2.4.1", "screenfull": "^6.0.2", "styled-components": "^5.3.6", "tcplayer.js": "^5.2.0", "wordcloud": "^1.2.3"}, "devDependencies": {"@commitlint/cli": "^17.0.2", "@commitlint/config-conventional": "^17.0.2", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.199", "@types/node": "^17.0.45", "@types/nprogress": "^0.2.0", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "@types/react-router-dom": "^5.3.3", "@types/react-virtualized": "^9.22.0", "@types/redux-promise": "^0.5.29", "@types/styled-components": "^5.1.26", "@types/wordcloud": "^1.2.2", "@typescript-eslint/eslint-plugin": "^5.50.0", "@typescript-eslint/eslint-plugin-tslint": "^5.51.0", "@typescript-eslint/parser": "^5.50.0", "@vitejs/plugin-react-swc": "^3.0.1", "autoprefixer": "^10.4.7", "commitizen": "^4.2.4", "cz-git": "^1.3.5", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsdoc": "^39.8.0", "eslint-plugin-no-null": "^1.0.2", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.3", "eslint-plugin-unicorn": "^45.0.2", "lint-staged": "^13.0.2", "postcss": "^8.4.14", "prettier": "^2.7.1", "sass": "^1.84.0", "stylelint": "^14.9.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^9.0.0", "stylelint-config-styled-components": "^0.1.1", "stylelint-order": "^6.0.1", "stylelint-processor-styled-components": "^1.10.0", "tslint": "^6.1.3", "tslint-eslint-rules": "^5.4.0", "tslint-react": "^5.0.0", "typescript": "^4.9.5", "vite": "4.0.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "engines": {"node": ">=16.10.0", "pnpm": ">=7.19.0"}}