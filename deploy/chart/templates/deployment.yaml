apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
{{ include "project.labels" . | indent 4 }}
  name: {{ .Release.Name }}
spec:
  replicas: {{ .Values.api.replicas }}  
  revisionHistoryLimit: 10
  selector:
    matchLabels:
{{ include "project.matchLabels" . | indent 6 }}
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
      labels:
{{ include "project.matchLabels" . | indent 8 }}
    spec:
      containers:
      - name: {{ .Release.Name }}
{{- if .Values.api.image.url }}
        image: "{{ .Values.api.image.url }}"
{{- else }}
        image: "{{ .Values.api.image.repository }}:{{ .Values.api.image.tag }}"
{{- end }}
        imagePullPolicy: {{ .Values.api.image.pullPolicy }}
{{- if .Values.api.resources }}
        resources:
{{ toYaml .Values.api.resources | indent 10 }}
{{- end }}
      dnsPolicy: ClusterFirst
{{- with .Values.imagePullSecrets }}
      imagePullSecrets:
{{- toYaml . | nindent 8 }}
{{- end }}
      restartPolicy: Always
      schedulerName: default-scheduler
