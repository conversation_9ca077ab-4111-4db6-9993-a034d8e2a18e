apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}
  annotations:
  labels:
{{ include "project.labels" . | indent 4 }}
spec:
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
  selector:
{{ include "project.matchLabels" . | indent 4 }}
{{- if .Values.backend }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.backend.name }}
  annotations:
  labels:
{{ include "project.labels" . | indent 4 }}
spec:
  type: ExternalName
  externalName: {{ .Values.backend.host }}
{{- end }}
{{- if .Values.lowcode }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.lowcode.name }}
  annotations:
  labels:
{{ include "project.labels" . | indent 4 }}
spec:
  type: ExternalName
  externalName: {{ .Values.lowcode.host }}
{{- end }}