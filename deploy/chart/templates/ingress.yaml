apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  labels:
{{ include "project.labels" . | indent 4 }}
  annotations:
    cert-manager.io/cluster-issuer: {{ default "letsencrypt" .Values.clusterIssuer }}
    kubernetes.io/tls-acme: "true"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/service-weight: ""
    nginx.ingress.kubernetes.io/configuration-snippet: |
      add_header client-version {{ default "*" $.Values.client.version }};
  name: {{ .Release.Name }}
spec:
  rules:
  - host: {{ .Values.api.host }}
    http:
      paths:
{{- if .Values.backend }}
      - backend:
          serviceName: {{ .Values.backend.name }}
          servicePort: {{ .Values.backend.port }}
        path: {{ .Values.backend.path }}
        pathType: ImplementationSpecific
{{- end }}
      - backend:
          serviceName: {{ .Release.Name }}
          servicePort: 8080
        path: /
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - {{ .Values.api.host }}
    secretName: {{ .Release.Name }}-cert