{{- define "project.name" -}}
{{- default "project" .Values.nameOverride | trunc 63 | trimSuffix "-" -}}
{{- end -}}


{{- define "project.labels" -}}
release: {{ .Release.Name }}
app: {{ .Release.Name }}-project
{{- end -}}



{{/* matchLabels */}}
{{- define "project.matchLabels" -}}
release: {{ .Release.Name }}
app: {{ .Release.Name }}-project
{{- end -}}



{{- define "project.database.host" -}}
  {{- if eq .Values.database.environment "dev" -}}
    {{- .Values.database.dev.dev_host -}}
  {{- else if eq .Values.database.environment "uat01" -}}
    {{- .Values.database.uat01.uat01_host -}}
  {{- else -}}
    {{- .Values.database.uat02.uat02_host -}}
  {{- end -}}
{{- end -}}


{{- define "project.database.port" -}}
  {{- if eq .Values.database.environment "dev" -}}
    {{- .Values.database.dev.dev_port -}}
  {{- else if eq .Values.database.environment "uat01" -}}
    {{- .Values.database.uat01.uat01_port -}}
  {{- else -}}
    {{- .Values.database.uat02.uat02_port -}}
  {{- end -}}
{{- end -}}


{{- define "project.database.name" -}}
  {{- if eq .Values.database.environment "dev" -}}
    {{- .Values.database.dev.dev_database -}}
  {{- else if eq .Values.database.environment "uat01" -}}
    {{- .Values.database.uat01.uat01_database -}}
  {{- else -}}
    {{- .Values.database.uat02.uat02_database -}}
  {{- end -}}
{{- end -}}


{{- define "project.database.username" -}}
  {{- if eq .Values.database.environment "dev" -}}
    {{- .Values.database.dev.dev_username -}}
  {{- else if eq .Values.database.environment "uat01" -}}
    {{- .Values.database.uat01.uat01_username -}}
  {{- else -}}
    {{- .Values.database.uat02.uat02_username -}}
  {{- end -}}
{{- end -}}


{{- define "project.database.password" -}}
  {{- if eq .Values.database.environment "dev" -}}
    {{- .Values.database.dev.dev_password -}}
  {{- else if eq .Values.database.environment "uat01" -}}
    {{- .Values.database.uat01.uat01_password -}}
  {{- else -}}
    {{- .Values.database.uat02.uat02_password -}}
  {{- end -}}
{{- end -}}


{{- define "project.api" -}}
  {{- printf "%s-api" (include "project.fullname" .) -}}
{{- end -}}
