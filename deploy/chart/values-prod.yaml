imagePullSecrets:
  - name: front-image-secret

api:
  host: wasp.xboil.cn
  image:
    url: meiji-zt-registry-vpc.cn-shenzhen.cr.aliyuncs.com/mj-front/wasp-web:release
    pullPolicy: IfNotPresent
  replicas: 2
  service:
    port:

  resources:
    requests:
      cpu: "300m"
      memory: "256Mi"
    limits:
      cpu: "2"
      memory: "512Mi"

clusterIssuer: letsencrypt

client:
  version:

backend:
  name: platform-api
  path: /platform
  port: 8080
  host: huangfeng-proxy-platform-service.wasp-prod