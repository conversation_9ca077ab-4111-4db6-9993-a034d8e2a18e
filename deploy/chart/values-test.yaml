imagePullSecrets:
  - name: front-image-secret

api:
  host: test-wasp.xboil.cn
  image:
    repository: meiji-zt-registry-vpc.cn-shenzhen.cr.aliyuncs.com/mj-front/wasp-web
    tag:
    pullPolicy: IfNotPresent
  replicas: 1
  service:
    port:

  resources:
    requests:
      cpu: "300m"
      memory: "256Mi"
    limits:
      cpu: "2"
      memory: "512Mi"

clusterIssuer: letsencrypt-prod

client:
  version:

backend:
  name: platform-api
  path: /platform
  port: 8080
  host: huangfeng-proxy-platform-service.test


