user  nginx;
worker_processes  2;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"'
                      '"$http_cookie"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    gzip  on;

    include /etc/nginx/conf.d/*.conf;

    server {
        listen 8080 default_server;

        server_name _;
        root /app/dist;

        # Add index.php to the list if you are using PHP
        index index.html index.htm;

        # try_files $uri $uri/ @lowcode;

        location /assets/ {
            root /app/dist;
            try_files $uri $uri/ = 404;
        }

        #location ~ ^/version {
        #    default_type text/html;
        #    return 200 '${CLIENT_VERSION}';
        #}

        location / {
            # First attempt to serve request as file, then
            # as directory, then fall back to displaying a 404.
            try_files $uri $uri/ @lowcode;
        }
 
        location @lowcode {
            proxy_pass http://appsmith;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Cookie $http_cookie;
        }
    }
}
