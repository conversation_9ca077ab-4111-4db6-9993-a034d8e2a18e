/**
 * @Owners haoyang
 * @Title eslint 配置文件
 */

// TODO 弃用 tslint，转为 eslint 等效规则
// TODO 规则分类管理，尽可能采用现成规范包

module.exports = {
    root: true,
    settings: {
        react: {
            version: 'detect',
        },
    },
    env: {
        browser: true,
        node: true,
        es6: true,
    },
    /* 指定如何解析语法 */
    parser: '@typescript-eslint/parser',
    /* 优先级低于 parse 的语法解析配置 */
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
        jsxPragma: 'React',
        ecmaFeatures: {
            jsx: true,
        },
        project: 'tsconfig.json',
    },
    plugins: ['react', '@typescript-eslint', 'react-hooks', 'prettier', 'import', 'react-refresh'],
    /* 继承某些已有的规则 */
    extends: [
        // 'eslint:recommended',
        // 'plugin:react/recommended',
        // 'plugin:@typescript-eslint/recommended',
        // 'plugin:react/jsx-runtime',
        // 'plugin:react-hooks/recommended',
        // 'plugin:import/typescript',
        './configs/eslint/caibird/base/.eslintrc',
        './configs/eslint/caibird/.eslintrc',
        './configs/eslint/extra/.eslintrc',
        'prettier',
        'plugin:prettier/recommended',
    ],
    ignorePatterns: ['**/dist/**', '**/node_modules/**', '**/lib/**', '**/bundle/**', '**/assets/**', '**/server/models/**'],
    /*
     * "off" 或 0    ==>  关闭规则
     * "warn" 或 1   ==>  打开的规则作为警告（不影响代码执行）
     * "error" 或 2  ==>  规则作为一个错误（代码不能执行，界面报错）
     */
    rules: {
        // 验证组件是否符合react-refresh要求
        'react-refresh/only-export-components': 'off',
    },
};
