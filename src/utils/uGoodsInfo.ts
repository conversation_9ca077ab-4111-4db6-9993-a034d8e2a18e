/**
 * @Owners blv
 * @Title 计算商品价格相关逻辑 根据商品活动信息和优惠券信息计算商品价格
 */

import { type Console } from '@/api/interface/live/console';
import { type CSSProperties } from 'react';

import { uNumber } from './uNumber';
// import dayjs from 'dayjs';
// import sortBy from 'lodash/sortBy';

// import { uNumber } from './uNumber';
export type GoodsItem = Console.Response.FindLiveGoodsByPage['dataList'][number];
type ActiveTimeType = {
    expiryStart: string;
    expiryEnd: string;
    serverTime: string;
};
// 定义活动信息类型
type ActiveInfo = {
    activeType: number;
    startTime: string;
    endTime: string;
    serverTime: string;
    fullDecrementDetails?: string[];
    copyWriting?: string;
    activePrice?: number;
    seckillBuyNum?: number;
};

type PromotionSummaryType = {
    label: string;
    name: string;
    type: number;
    activeTime: ActiveTimeType;
};

/** 活动类型 */
export enum activityTypeEnum {
    /**
     * 预售活动
     */
    preSale = 13,
    /**
     * 秒杀活动
     */
    preSaleKill = 14,
    /**
     * 包邮活动
     */
    NPinkage = 15,
    /**
     * 满减活动
     */
    FullDiscount = 16,
    /**
     * 换购活动
     */
    SwapBuy = 17,
    /**
     * 阶梯团购
     */
    LADDER = 18,
    /**
     * 省钱券活动
     */
    RepurchaseCoupon = 19,
    /**
     * 拿样返券活动
     */
    SampleCommentCoupon = 29,
    /**
     * 卖赠活动
     */
    Giveaway = 21,
}
/** 活动状态 */
export const activityStatusEnum = {
    //  1.待提交 2.待发布 3.未开始 4.进行中 5.已结束 6.已取消
    /** 未开始 */
    Notstart: 3,
    /** 进行中 */
    padding: 1,
    /**
     * 待发布
     */
    paddingRelease: 2,
    /**
     * 未开始
     */
    unStart: 3,
    /**
     * 进行中
     */
    progress: 4,
    /**
     * 已结束
     */
    end: 5,
    /**
     * 已取消
     */
    cancelled: 6,
} as const;
/** 换购商品类型 */
export enum swapGoodTypeEnum {
    notSwap = 0,
    isSwap = 1,
}
export type SkuMinPriceReturns = {
    // sku 原价
    originalPrice: number;
    // 单品触达活动｜优惠券时最低的价格
    minPrice: number;
    // 优惠券 id
    couponId: number;
    // 需要购买的数量
    quantity: number;
    // 优惠券优惠的金额-单品维度
    couponDiscountAmount: number;
    // 优惠券优惠的金额-总量维度
    couponTotalDiscountAmount: number;
    // 商品活动优惠的金额-单品维度
    activeDiscountAmount: number;
    // 商品活动优惠的金额-总量维度
    activeTotalDiscountAmount: number;
    // 商品触达的活动信息
    activeDesc: string;
    // 商品触达的活动详细信息
    activeDetailDesc: string;
    // 商品触达优惠券的信息
    couponDesc: string;
};

// 商品 sku 优惠信息汇总
export type SkuPromotionPrice = {
    // 直播专享优惠价
    livePromotionPrice: number;
    // 商品活动优惠价格
    activePromotionPrice: number;
    // 商品券优惠价格
    couponPromotionPrice: number;
    // 商品优惠后单价
    resultPrice: number;
    // 商品优惠后单价*商品数量
    resultTotalPrice: number;
    // 下个阶梯优惠提示
    nextLevelTips?: string;
};

namespace _uGoodsInfo {
    // TODO: @钰晶关注 获取可参与计算的券
    export const getGoodsAvailableCoupons = (couponsObj: Console.Response.GetActiveCouponResDTO[] | null, skuId?: number) => {
        const res: Console.Response.CouponModel1[] = [];

        if (couponsObj) {
            // 原始逻辑 过滤省钱券计算价格
            // couponsObj.activeCouponList.filter(i => i.coupons.some(v => v.couponType !== 3)).forEach(element => {
            couponsObj.forEach(element => {
                if (element.coupons && element.coupons.length) {
                    element.coupons.forEach(coupon => {
                        if (!!skuId && Array.isArray(coupon.skuInfoList) && coupon.skuInfoList.length) {
                            // 存在 skuId 并且优惠券skuInfoList 存在数据 意味着这个券是部分 sku 可用
                            if (coupon.skuInfoList.some(i => i.skuId === skuId)) {
                                res.push({
                                    ...coupon,
                                    activeTime: {
                                        expiryStart: element.expiryStart,
                                        expiryEnd: element.expiryEnd,
                                        serverTime: element.serverTime,
                                    },
                                });
                            }
                        } else {
                            res.push({
                                ...coupon,
                                activeTime: {
                                    expiryStart: element.expiryStart,
                                    expiryEnd: element.expiryEnd,
                                    serverTime: element.serverTime,
                                },
                            });
                        }
                    });
                }
            });
        }

        return res;
    };

    /**
     * 优惠券倒计时时间获取 距离开始/仅剩 =》 xx天xx时x分x秒/xx时x分x秒
     * @param now 当前时间
     * @param start 开始时间
     * @param end 结束时间
     * @returns string
     */
    export function timeFormat(now: number, start: number, end: number) {
        if (now < 0) return '';
        const thousand = 1000;
        const sixTen = 60;
        const day = 24;
        const oneDay = thousand * sixTen * sixTen * day;

        const isUnStart = now < start;
        const isStarted = now >= start && now < end;
        const isEnded = now >= end;

        const diffLessThanOneDay = (diffValue: number, _prefixStr: string) => {
            const hours = Math.floor((diffValue / (thousand * sixTen * sixTen)) % day);
            const minutes = Math.floor((diffValue / (thousand * sixTen)) % sixTen);
            const seconds = Math.floor((diffValue / thousand) % sixTen);
            // return `${prefixStr}${hours}时${minutes}分${seconds}秒`;
            return {
                hours,
                minutes,
                seconds,
            };
        };

        const diffMoreThanOneDay = (diffValue: number, _prefixStr: string) => {
            const days = Math.floor(diffValue / (thousand * sixTen * sixTen * day));
            const hours = Math.floor((diffValue / (thousand * sixTen * sixTen)) % day);
            const minutes = Math.floor((diffValue / (thousand * sixTen)) % sixTen);
            const seconds = Math.floor((diffValue / thousand) % sixTen);
            return {
                days,
                hours,
                minutes,
                seconds,
            };
            // return `${prefixStr}${days}天${hours}时${minutes}分`;
        };

        if (isUnStart && start - now < oneDay) {
            // 未开始且离开始小于一天
            const diffValue = start - now;
            return diffLessThanOneDay(diffValue, '距离开始 ');
        }

        if (isUnStart && start - now >= oneDay) {
            // 未开始且离开始大于一天
            const diffValue = start - now;
            return diffMoreThanOneDay(diffValue, '距离开始 ');
        }

        if (isStarted && end - now < oneDay) {
            // 已开始且离结束小于一天
            const diffValue = end - now;
            return diffLessThanOneDay(diffValue, '仅剩');
        }

        if (isStarted && end - now >= oneDay) {
            // 已开始且离结束大于一天
            const diffValue = end - now;
            return diffMoreThanOneDay(diffValue, '仅剩');
        }

        if (isEnded) return '';

        return '';
    }
    /**
     * 格式化优惠券
     */
    export const _formatCoupon = (
        spuExclusive: PromotionSummaryType[],
        showCoupons: PromotionSummaryType[],
        activeDetail: PromotionSummaryType[]
    ): {
        text: string;
        prefix?: string;
        style?: CSSProperties;
        prefixStyle?: CSSProperties;
        type?: number;
        activeTime?: {
            expiryStart: string;
            expiryEnd: string;
            serverTime: string;
        };
    }[] => {
        const list = [
            ...spuExclusive.map(item => ({ text: item.name, prefix: item.label, activeTime: item.activeTime, type: item.type })),
            ...showCoupons.map(item => {
                const couponItem: {
                    text: string;
                    prefix?: string;
                    style?: CSSProperties;
                    prefixStyle?: CSSProperties;
                    type?: number;
                    activeTime?: {
                        expiryStart: string;
                        expiryEnd: string;
                        serverTime: string;
                    };
                } = {
                    text: item.name,
                    type: item.type,
                };
                if (item.label) {
                    couponItem.prefix = item.label;
                }
                if (item.activeTime) {
                    couponItem.activeTime = item.activeTime;
                }

                if (item.type === 2) {
                    // 直播券
                    couponItem.prefixStyle = {
                        fontWeight: 'bold',
                    };
                    couponItem.style = {
                        background: '#FFF1F0',
                    };
                }
                return couponItem;
            }),
            ...activeDetail.map(item => ({ text: item.name, prefix: item.label, activeTime: item.activeTime, type: item.type })),
        ];
        return list;
    };
    /**
     * 计算商品活动基本信息
     * @param param - retailPrice 当前零售价
     * @param param - skuId 当前商品的 skuId 信息
     * @param param - activeCoupon 商品优惠券信息
     * @param param - activeInfo 商品活动信息
     * @returns retailPrice -sku原价
     * @returns minPrice -优惠后的价格
     * @returns showCoupons -对外显示的优惠券数据
     * @returns fullDiscountLadder -满减活动数据
     */
    // TODO:
    export const getGoodsPromotionSummary = (param: {
        spuExclusivePrice?: number;
        activeCoupon?: Console.Response.GetActiveCouponResDTO[] | null;
        activeInfo: Console.Response.ActiveData;
    }): {
        spuExclusive: PromotionSummaryType[];
        showCoupons: PromotionSummaryType[];
        activeDetail: PromotionSummaryType[];
        // RepurchaseCoupon: string[];
        // SampleCommentCoupon: string[];
    } => {
        const { spuExclusivePrice, activeInfo, activeCoupon } = param;
        let coupons: Console.Response.CouponModel1[] = [];
        let showCoupons: PromotionSummaryType[] = [];
        let activeDetail: PromotionSummaryType[] = [];
        let spuExclusive: PromotionSummaryType[] = [];
        if (activeCoupon) {
            coupons = getGoodsAvailableCoupons(activeCoupon || []);
        }
        if (coupons.length || activeInfo) {
            if (coupons.length) {
                // 过滤掉直播券 只参与比价
                const filterCoupons = [...coupons];
                // 直播专享券 优先级最高
                const singleLiveCouponList = coupons?.filter(i => i.couponType === 2);
                let resultCoupons = filterCoupons;
                // let resultCoupons = distCoupon
                //     ? [distCoupon, ...filterCoupons.filter(i => i.id !== skuPromotionInfo.couponId)]
                //     : filterCoupons;

                //  合并直播专享券
                if (singleLiveCouponList?.length) {
                    resultCoupons = [...singleLiveCouponList, ...resultCoupons?.filter(i => i.couponType !== 2)];
                }
                // const expiryStart = dayjs(activeCoupon.expiryStart).valueOf(); // 开始时间
                // const expiryEnd = dayjs(activeCoupon.expiryEnd).valueOf(); // 截止时间
                // const timeStr = timeFormat(serverTime, expiryStart, expiryEnd);

                showCoupons = resultCoupons.map(i => ({
                    label: i.couponTab || '券',
                    name: i.name,
                    type: i?.couponType,
                    activeTime: i?.activeTime,
                }));
            }
        }
        // 优惠券和满减活动不能共存
        if (activeInfo?.activeType === activityTypeEnum.FullDiscount) {
            activeDetail = handleActiveDetail(activeInfo);
        }
        // 优惠券和满减活动不能共存
        if (activeInfo?.activeType === activityTypeEnum.SwapBuy) {
            activeDetail = handleActiveDetail(activeInfo);
        }

        if (activeInfo?.activeType === activityTypeEnum.NPinkage) {
            activeDetail = handleActiveDetail(activeInfo);
        }
        if (activeInfo?.activeType === activityTypeEnum.LADDER) {
            activeDetail = handleActiveDetail(activeInfo);
        }
        if (spuExclusivePrice) {
            spuExclusive = [
                {
                    label: '直播专享',
                    name: `${uNumber.centToYuan(spuExclusivePrice ?? 0)}元`,
                    type: -1,
                    activeTime: {
                        expiryStart: '',
                        expiryEnd: '',
                        serverTime: '',
                    },
                },
            ];
        }
        return {
            spuExclusive,
            showCoupons,
            activeDetail,
        };
    };
}
// 定义活动类型映射
const activityTypeMap: Record<number, (activeInfo: ActiveInfo) => PromotionSummaryType> = {
    [activityTypeEnum.FullDiscount]: (activeInfo: ActiveInfo) => ({
        label: '满减',
        name: activeInfo.fullDecrementDetails?.[0] ?? '',
        type: activeInfo.activeType,
        activeTime: createActiveTime(activeInfo),
    }),
    [activityTypeEnum.SwapBuy]: (activeInfo: ActiveInfo) => ({
        label: '超级换购',
        name: activeInfo.copyWriting ?? `满${uNumber.centToYuan(activeInfo.activePrice ?? 0)}参与换购`,
        type: activeInfo.activeType,
        activeTime: createActiveTime(activeInfo),
    }),
    [activityTypeEnum.NPinkage]: (activeInfo: ActiveInfo) => ({
        label: 'N件包邮',
        name: activeInfo.copyWriting ?? '',
        type: activeInfo.activeType,
        activeTime: createActiveTime(activeInfo),
    }),
    [activityTypeEnum.LADDER]: (activeInfo: ActiveInfo) => ({
        label: '阶梯团购',
        name: `已抢${activeInfo.seckillBuyNum}件`,
        type: activeInfo.activeType,
        activeTime: createActiveTime(activeInfo),
    }),
};

// 创建活动时间对象
const createActiveTime = (activeInfo: ActiveInfo) => ({
    expiryStart: activeInfo.startTime,
    expiryEnd: activeInfo.endTime,
    serverTime: activeInfo.serverTime,
});

// 处理活动详情
const handleActiveDetail = (activeInfo?: ActiveInfo): PromotionSummaryType[] => {
    if (!activeInfo?.activeType) return [];

    const handler = activityTypeMap[activeInfo.activeType];
    if (!handler) return [];

    const result = handler(activeInfo);
    return [result];
};
export const uGoodsInfo: typeof _uGoodsInfo = {
    ..._uGoodsInfo,
};
