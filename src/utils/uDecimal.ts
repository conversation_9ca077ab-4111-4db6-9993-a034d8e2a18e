/**
 * @Owners haoyang
 * @Title 小数计算
 */

import Decimal from 'decimal.js';

type DecimalFun = (num1: Decimal.Value, num2: Decimal.Value, fixed?: number) => number;

/**
 * 小数计算
 */
export namespace uDecimal {
    /** 加法 */
    export const add: DecimalFun = (num1, num2, fixed = 2) => Number(new Decimal(num1).add(num2).toFixed(fixed));

    /** 减法 */
    export const sub: DecimalFun = (num1, num2, fixed = 2) => Number(new Decimal(num1).sub(num2).toFixed(fixed));

    /** 乘法 */
    export const mul: DecimalFun = (num1, num2, fixed = 2) => Number(new Decimal(num1).mul(num2).toFixed(fixed));

    /** 除法 */
    export const div: DecimalFun = (num1, num2, fixed = 2) => Number(new Decimal(num1).div(num2).toFixed(fixed));

    /**
     * 转为小数
     * @params fixed 位数
     */
    export const toDecimal = (num: number, fixed = 2) => Number(new Decimal(num).div(10 ** fixed));

    /**
     * 转为整数
     * @params fixed 位数
     */
    export const toInteger = (num: number, fixed = 2) => Number(new Decimal(num).mul(10 ** fixed));

    /**
     * 分转金额
     * @params
     */
    export const FenToAmount = (val?: number, suffix?: string, emptyText?: string) => {
        if (val || val === 0) return `¥ ${new Decimal(val).div(100)}${suffix ? suffix : ''}`;
        return emptyText ? emptyText : '-';
    };

    /**
     * 分转元 乘法
     * @params
     */
    export const FenToYuan = (val?: number) => {
        if (val || val === 0) return new Decimal(val).div(100).toNumber();
        return undefined;
    };

    /**
     * 元转分 除法
     * @params
     */
    export const YuanToFen = (val?: number) => {
        if (val || val === 0) return new Decimal(val).mul(100).toNumber();
        return undefined;
    };
}
