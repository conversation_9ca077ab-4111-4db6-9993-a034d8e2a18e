/**
 * @Owners haoyang
 * @Title cookie-管理器
 */

import Cookies from 'js-cookie';

/**
 * cookie-管理器
 */
export namespace uCookies {
    /**
     * 获取 cookie
     * @param cname cookie 名称
     */
    export const get = (cname: string) => Cookies.get(cname);

    /**
     * 设置 cookie
     * @param cname cookie 名称
     * @param value cookie 值
     * @param expires 时间时间（day）默认7天
     */
    export const set = (cname: string, value: string, expires?: number) =>
        Cookies.set(cname, value, {
            expires: expires || 7,
        });

    /**
     * 移除 cookie
     * @param cname cookie 名称
     */
    export const remove = (cname: string) => Cookies.remove(cname);

    /**
     * 监听 cookie 变化
     * @param cname cookie 名称
     */
    export const onChange = (cname: string, cb: ({ changed, deleted }: ICookieStore.CookieCahnged) => void) => {
        const cookieStore = window.cookieStore;
        if (!cookieStore) return console.log('该浏览器不兼容 cookieStore');
        cookieStore.addEventListener('change', event => {
            const changed = event.changed.find(item => item.name === cname);
            const deleted = event.deleted.find(item => item.name === cname);
            cb({ changed, deleted });
        });
    };
}
