/**
 * @Owners xj
 * @Title AES加密
 */

import CryptoJ<PERSON> from 'crypto-js';

const algorithm = 'aes-128-cbc';
const key = CryptoJS.enc.Utf8.parse('65c0100906e11ebb');
const iv = CryptoJS.enc.Utf8.parse('caibird_default1');

export namespace uCryptoJS {
    export const Encrypt = (str: string) => {
        const srcs = CryptoJS.enc.Utf8.parse(str);
        const encrypted = CryptoJS.AES.encrypt(srcs, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7,
            algorithm,
        });
        return encrypted.ciphertext.toString();
    };
}
