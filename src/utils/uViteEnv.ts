/**
 * @Owners zp
 * @Title vite 编译时环境变量处理 - 工具方法
 */
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

export function isTest(mode?: string): boolean {
    return mode === 'test';
}

export function isDev(mode?: string): boolean {
    return mode === 'development';
}

export function isProd(mode?: string): boolean {
    return mode === 'production';
}

// Read all environment variable configuration files to process.env
export function wrapperEnv(envConf: Recordable<boolean | number | string>): ImportMetaEnv {
    const ret: { [key: keyof ImportMetaEnv]: unknown } = {};

    for (const envName of Object.keys(envConf)) {
        const val = envConf[envName];
        let realName = typeof val === 'string' ? val.replace(/\\n/g, '\n') : val;
        realName = realName === 'true' ? true : realName === 'false' ? false : realName;

        if (envName === 'VITE_PORT') {
            realName = Number(realName);
        }
        if (envName === 'VITE_PROXY') {
            try {
                realName = JSON.parse(realName as string);
            } catch (error) {
                console.log(error);
            }
        }
        ret[envName] = realName;
        process.env[envName] = realName as string;
    }
    return ret as ImportMetaEnv;
}

/**
 * Get the environment variables starting with the specified prefix
 * @param match prefix
 * @param confFiles ext
 */
export function getEnvConfig(match = 'VITE_GLOB_', confFiles = ['.env', '.env.production']) {
    let envConfig = {};
    confFiles.forEach(item => {
        try {
            const env = dotenv.parse(fs.readFileSync(path.resolve(process.cwd(), item)));
            envConfig = { ...envConfig, ...env };
        } catch (error) {
            console.error(`Error in parsing ${item}`, error);
        }
    });

    Object.keys(envConfig).forEach(key => {
        const reg = new RegExp(`^(${match})`);
        if (!reg.test(key)) {
            Reflect.deleteProperty(envConfig, key);
        }
    });
    return envConfig;
}

/**
 * Get user root directory
 * @param dir file path
 */
export function getRootPath(...dir: string[]) {
    return path.resolve(process.cwd(), ...dir);
}
