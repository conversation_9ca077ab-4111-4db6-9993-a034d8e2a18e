/**
 * @Owners haoyang
 * @Title 基础-工具方法
 */

/**
 * 基础-工具方法
 */
export namespace uBase {
    /**
     * @description 获取浏览器默认语言
     * @return string
     */
    export const getBrowserLang = () => {
        const browserLang = navigator.language ? navigator.language : navigator.browserLanguage;
        let defaultBrowserLang = '';
        if (browserLang.toLowerCase() === 'cn' || browserLang.toLowerCase() === 'zh' || browserLang.toLowerCase() === 'zh-cn') {
            defaultBrowserLang = 'zh';
        } else {
            defaultBrowserLang = 'en';
        }
        return defaultBrowserLang;
    };

    /**
     * @description 判断数据类型
     * @param val 需要判断类型的数据
     * @return string
     */
    export const isType = (val: unknown) => {
        if (val === null) return 'null';
        if (typeof val !== 'object') return typeof val;
        const i = 8;
        return Object.prototype.toString.call(val).slice(i, -1).toLocaleLowerCase();
    };

    /**
     * @description 生成随机数
     * @param min 最小值
     * @param max 最大值
     * @return number
     */
    export function randomNum(min: number, max: number): number {
        const num = Math.floor(Math.random() * (min - max) + max);
        return num;
    }

    /**
     * 比较两个一维数组
     * @description 不区分顺序
     */
    export function compareArrSame<T>(arr1: T[], arr2: T[]) {
        if (arr1.length !== arr2.length) return false;
        return arr1.every(item => arr2.includes(item));
    }

    /**
     * 将查询参数转为对象
     */
    export function parseURLToObj(strParam: string) {
        const _strParam = strParam.replace('?', '');
        if (!_strParam) return {};
        const resObj: { [key: string]: string } = {};
        _strParam.split('&').forEach(param => {
            const [key, val] = param.split('=');
            resObj[key] = val;
        });

        return resObj;
    }

    /**
     * 解析 urlencoded 格式
     */
    export function parseUrlencoded(urlencoded: string) {
        if (!urlencoded) return {};
        const result: { [key: string]: string } = {};
        const entries = urlencoded.replace(/^\?/, '').split('&');
        entries.forEach(entry => {
            const [key, value] = entry.split('=');
            result[key] = value;
        });
        return result;
    }

    /**
     * 序列化为 urlencoded 格式
     */
    export function stringifyUrlencoded(obj?: { [key: string]: number | string }) {
        if (!obj) return '';
        const entries = Object.entries(obj).map(entry => entry.join('='));
        return entries.join('&');
    }
}

/**
 * 复制文本到剪贴板
 */
export async function copyTextToClipboard(text: string) {
    if ('clipboard' in navigator) {
        return navigator.clipboard.writeText(text);
    }
    return document.execCommand('copy', true, text);
}
