/**
 * @Owners zp
 * @Title 菜单-工具方法
 */
import type { SysMenu } from '@/api/interface/system';
import * as Icons from '@ant-design/icons';
import React from 'react';

/**
 * @description 获取父级menu的path
 * @param path 当前访问地址
 * @returns string
 */
export const getMenuParentPath = (path: string, list: SysMenu.MenuItem[]): string => {
    let p = '';
    for (const item of list) {
        if (item.children?.map(m => m.path).includes(path)) {
            p = item.path;
            break;
        }
        item.children && getMenuParentPath(path, item.children);
    }
    return p;
};

/**
 * @description 获取父级menu的id
 * @param id 当前菜单id
 * @returns undefined | number
 */
export const getMenuParentId = (id: number, list?: SysMenu.MenuItem[]): number[] | null => {
    for (const item of list || []) {
        const nodeId = Number(item.id);
        // 说明已经找到目标节点，返回空即可
        if (nodeId === id) return [];
        if (item.children) {
            const pid: number[] | null = getMenuParentId(id, item.children);
            if (pid) {
                return pid.concat([nodeId]);
            }
        }
    }
    return null;
};

/**
 * 获取全部选中菜单
 * @param ids 选中菜单
 * @returns 选中菜单 + 父级菜单
 */
export const getCheckedMenuId = (ids: number[], menuList: SysMenu.MenuItem[]) => {
    const allIds: number[] = [];
    // 对每个id做遍历
    for (const item of ids) {
        // 找父级
        const pidList = getMenuParentId(item, menuList) || [];
        pidList && allIds.push(...pidList);
    }
    return Array.from(new Set(allIds));
};

/**
 * @description 获取需要展开的 subMenu
 * @param path 当前访问地址
 * @returns array
 */
export const getOpenKeys = (path: string) => {
    let newStr = '';
    const newArr: string[] = [];
    const arr = path.split('/').map(i => '/' + i);
    for (let i = 1; i < arr.length - 1; i++) {
        newStr += arr[i];
        newArr.push(newStr);
    }
    return newArr;
};

/**
 * @description 递归查询对应的菜单
 * @param path 当前访问地址
 * @param routes 菜单
 * @returns array
 */
export const searchMenu = (path: string, routes: SysMenu.MenuItem[] = []): SysMenu.MenuItem => {
    let result = {};
    for (const item of routes) {
        if (item.path === path) return item;
        if (item.children) {
            const res = searchMenu(path, item.children);
            if (Object.keys(res).length) result = res;
        }
    }
    return result as SysMenu.MenuItem;
};

/**
 * @description 递归当前路由的 所有 关联的路由，生成面包屑导航栏
 * @param path 当前访问地址
 * @param menuList 菜单列表
 * @returns array
 */
export const getBreadcrumbList = (path: string, menuList: SysMenu.MenuItem[]) => {
    const tempPath: SysMenu.MenuItem[] = [];
    try {
        const getNodePath = (node: SysMenu.MenuItem) => {
            tempPath.push(node);
            // 找到符合条件的节点，通过throw终止掉递归
            if (node.path === path) {
                throw new Error('GOT IT!');
            }
            if (node.children && node.children.length > 0) {
                for (const child of node.children) {
                    getNodePath(child);
                }
                // 当前节点的子节点遍历完依旧没找到，则删除路径中的该节点
                tempPath.pop();
            } else {
                // 找到叶子节点时，删除路径当中的该叶子节点
                tempPath.pop();
            }
        };
        for (const item of menuList) {
            getNodePath(item);
        }
    } catch (e) {
        return tempPath.map(item => item.name);
    }
};

/**
 * @description 双重递归 找出所有 面包屑 生成对象存到 redux 中，就不用每次都去递归查找了
 * @param menuList 当前菜单列表
 * @returns object
 */
export const findAllBreadcrumb = (menuList: SysMenu.MenuItem[]) => {
    const handleBreadcrumbList: { [path: string]: string[] } = {};
    const loop = (menuItem: SysMenu.MenuItem) => {
        if (menuItem?.children?.length) menuItem.children.forEach(loop);
        else {
            const breadcrumbList = getBreadcrumbList(menuItem.path, menuList);
            breadcrumbList && (handleBreadcrumbList[menuItem.path] = breadcrumbList);
        }
    };
    menuList.forEach(loop);
    return handleBreadcrumbList;
};

/**
 * @description 使用递归处理路由菜单，生成一维数组，做菜单权限判断
 * @param menuList 所有菜单列表
 * @param newArr 菜单的一维数组
 * @return array
 */
export function handleRouter(routerList: SysMenu.MenuItem[], newArr: string[] = []) {
    routerList.forEach((item: SysMenu.MenuItem) => {
        typeof item === 'object' && item.path && newArr.push(item.path);
        item.children && item.children.length && handleRouter(item.children, newArr);
    });
    return newArr;
}

/**
 * 根据名称获取icon组件
 * @param name icon 名称
 * @returns <IconName />
 */
export function getIconByName(name?: keyof typeof Icons) {
    return name && Icons[name] ? React.createElement(Icons[name] as React.FC) : '';
}

/**
 * 获取icons选择下拉项
 * @returns selectOption
 */
export function getIconsOption() {
    // 获取Icons对象，过滤下面的方法后就是真的icon
    const iconNameList = (Object.keys(Icons) as (keyof typeof Icons)[]).filter(i => i.startsWith(i[0].toUpperCase()));
    // const iconNameList = allIconName.slice(Math.floor(allIconName.length * 0), Math.floor(allIconName.length * 1));
    return iconNameList.map(icon => ({
        label: getIconByName(icon),
        value: icon.toLowerCase(),
        realValue: icon,
        className: 'menu-select-option',
    }));
}

/**
 * 转换后端数据，处理糟糕的命名,以及过滤掉禁用的菜单
 * @returns SysMenu.MenuItem
 */
export function createMenuNormalData(list: SysMenu.MenuItem[], needKey = true): SysMenu.MenuItem[] {
    return list
        ?.map((item: SysMenu.MenuItem) => {
            const path = item.path;
            const newMenu: SysMenu.MenuItem & { key?: string } = {
                ...item,
                path,
                children: item.chidrenMenuList ? createMenuNormalData(item.chidrenMenuList, needKey) : undefined,
            };
            if (needKey) {
                newMenu.key = path;
            }
            return newMenu;
        })
        .filter(m => m.status);
}
