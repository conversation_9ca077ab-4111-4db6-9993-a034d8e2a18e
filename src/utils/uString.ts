/**
 * @Owners haoyang
 * @Title 字符串-工具方法
 */
/**
 * 字符串-工具方法
 */
function decodeFilename(filename: string) {
    try {
        return decodeURIComponent(filename); // 解码 %20 等字符
    } catch {
        return filename; // 失败则返回原始值
    }
}
export namespace uString {
    /**
     * @description 字符串省略
     * @param str 字符串
     * @param len 长度
     * @param ellipsis 省略号
     * @return string
     */
    export const stringEllipsis = (str: string, len: number, ellipsis = '...') => {
        if (str.length <= len) return str;
        return str.slice(0, len) + ellipsis;
    };

    export const forceDownload = async (url: string) => {
        const a = document.createElement('a');
        a.href = url;
        let filename = '';
        filename = decodeFilename((url.split('/').pop() || '').split('?')[0]);
        if (filename) {
            a.download = filename; // 有传入自定义文件名
        } else {
            a.download = ''; // 让浏览器根据链接自己推断名字
        }
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };
}
