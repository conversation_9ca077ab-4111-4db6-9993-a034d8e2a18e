/**
 * @Owners haoyang
 * @Title Storage-管理器
 */

/**
 * Storage-管理器
 */
export namespace uStorage {
    /**
     * 获取localStorage
     * @param key Storage名称
     * @return string
     */
    export const get = (key: string) => {
        const value = window.localStorage.getItem(key);
        try {
            return JSON.parse(localStorage.getItem(key) as string);
        } catch (error) {
            return value;
        }
    };

    /**
     * 存储localStorage
     * @param key Storage名称
     * @param value Storage值
     * @return void
     */
    export const set = (key: string, value: unknown) => {
        localStorage.setItem(key, JSON.stringify(value));
    };

    /**
     * 清除localStorage
     * @param key Storage名称
     * @return void
     */
    export const remove = (key: string) => {
        localStorage.removeItem(key);
    };

    /**
     * 清除所有localStorage
     * @return void
     */
    export const clear = () => {
        localStorage.clear();
    };
}
