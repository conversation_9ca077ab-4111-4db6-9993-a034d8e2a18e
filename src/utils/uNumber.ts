/**
 * @Owners lzy
 * @Title 数据处理器
 */

/**
 * 格式化数字显示规则：
 * - 小于 1000：正常显示
 * - 1000 - 9999：显示为 2.51k 形式
 * - 10000 及以上：显示为 1.00万 - 99.22万 - 100.21万
 *
 * @param num 数字
 * @returns 格式化后的字符串
 */
export const formatNumber = (num: number): string => {
    if (num < 1000) {
        return num.toString();
    } else if (num < 10000) {
        return (num / 1000).toFixed(2) + 'k';
    }
    return (num / 10000).toFixed(2) + '万';
};

/**
 *
 * @param second 秒
 * @returns string
 */
export const formatDuration = (second: number): string => {
    if (second <= 0) return '0秒'; // 如果小于等于0秒，返回0秒

    const hours = Math.floor(second / 3600); // 计算小时数
    const minutes = Math.floor((second % 3600) / 60); // 计算分钟数
    const seconds = Math.floor(second % 60); // 计算剩余的秒数

    // 如果小时数大于0，显示小时、分钟、秒
    if (hours > 0) {
        return `${hours}小时${minutes.toString().padStart(2, '0')}分${seconds}秒`;
    }

    // 如果分钟数大于0且小时数为0，显示分钟、秒
    if (minutes > 0) {
        return `${minutes}分${seconds}秒`;
    }

    // 如果分钟数为0，显示秒数
    return `${seconds}秒`;
};

export const toFixed = (num: number, count = 1, type: 'ceil' | 'floor' | 'round' = 'round') => {
    const powBase = 10;
    return Math[type](num * Math.pow(powBase, count)) / Math.pow(powBase, count);
};

namespace _uNumber {
    const radix = 36;
    /**
     * 分转元函数
     * @param num 输入值
     * @returns number
     */
    export const centToYuan = (value: number, type: 'ceil' | 'floor' | 'round' = 'round') => {
        const centUnit = 100;
        if (value && !Number.isNaN(value)) {
            // 除法计算的时候需要注意除数不能为0 否则会造成负无穷
            return toFixed(value / centUnit, 2, type);
        }
        return 0;
    };

    /**
     * 分转元函数 保留两位小数 主要用于金额展示场景
     * @param num 输入值
     * @returns string
     */
    export const centToYuanWithTwoDecimal = (value: number) => {
        const centUnit = 100;
        if (!Number.isNaN(value)) {
            // 除法计算的时候需要注意除数不能为0 否则会造成负无穷
            const result = String(toFixed(value / centUnit, 2));
            if (result.indexOf('.') === -1) {
                return `${result}.00`;
            }
            const [intValue, decimalValue] = result.split('.');
            return decimalValue.length === 1 ? `${intValue}.${decimalValue}0` : result;
        }
        return '0.00';
    };

    /**
     * 保留两位小数，不进行四舍五入，直接删除第三位小数
     * @param value 输入值
     * @returns number
     */
    export const yuanWithTwoDecimal = (value: number) => {
        if (!Number.isNaN(value)) {
            const str = String(value);
            const arr = str.split('.');
            if (arr.length === 1 || arr[1].length <= 2) {
                return value;
            }
            return Number(`${arr[0]}.${arr[1].slice(0, 2)}`);
        }
        return 0;
    };

    /**
     * 元转分函数
     * @param num 输入值
     * @returns number
     */
    export const yuanToCent = (value: number) => {
        const centUnit = 100;
        if (!Number.isNaN(value)) {
            return toFixed(value * centUnit, 0);
        }
        return 0;
    };

    /**
     * 对数值进行向下取整操作先将数字分成整数位和小数位，
     * 然后操作小数位，根据要保留的长度，进行截取，最终将整数和小数进行拼接
     * @param number 要处理的数值
     * @param count 保留的小数位数 默认两位
     * @returns 小数 | 整数
     */
    export const mathFloor = (number: number, count = 2): number => {
        const numberString = number.toString();
        if (numberString.indexOf('.') === -1) {
            return number;
        }
        // 根据小数点， 将数字分成整数位和小数位
        const numberArr = numberString.split('.');
        // 整数位值
        const intValue = numberArr[0];
        // 小数位值
        const decimalValue = numberArr[1];
        // 最终小数位值
        let decimalResultValue = decimalValue;
        if (decimalValue.length <= count) {
            decimalResultValue = decimalValue;
        } else {
            decimalResultValue = decimalValue.substring(0, count);
        }
        return Number(`${intValue}.${decimalResultValue}`);
    };

    /**
     * 元转万元函数
     * @param num 输入值
     * @returns number
     */
    export const yuanToWanYuan = (value: number, level = 2) => {
        const centUnit = 10000;
        if (!Number.isNaN(value)) {
            // 除法计算的时候需要注意除数不能为0 否则会造成负无穷
            return toFixed(value / centUnit, level, 'floor');
        }
        return 0;
    };

    /** 字符串补零 */
    export const financial = (x: string) => Number.parseFloat(x).toFixed(2);

    /**
     * 分转万元方法
     * 大于万元 显示 x.xx万 小于显示 x.xx 0 显示 0.00
     */
    export const centToWanYuan = (value: number) => {
        const wan = 10000;
        const yuan = centToYuan(value);
        let newValue = '';
        if (yuan >= wan) {
            newValue = `${financial(String(yuanToWanYuan(yuan)))}万`;
        } else {
            newValue = financial(String(yuan));
        }
        return newValue;
    };

    /**
     * 千克转克函数
     * @param num 输入值
     * @returns number
     */
    export const kgTog = (value: number, level = 2) => {
        const centUnit = 1000;
        if (!Number.isNaN(value)) {
            // 除法计算的时候需要注意除数不能为0 否则会造成负无穷
            return toFixed(value * centUnit, level, 'floor');
        }
        return 0;
    };

    /**
     * 克转千克函数
     * @param num 输入值
     * @returns number
     */
    export const gTokg = (value: number, level = 1) => {
        const centUnit = 1000;
        if (!Number.isNaN(value)) {
            // 除法计算的时候需要注意除数不能为0 否则会造成负无穷
            return toFixed(value / centUnit, level, 'floor');
        }
        return 0;
    };

    /**
     * 三位数后面添加逗号分隔
     * 第一个参数数组，第二个参数为是否有小数点
     */
    export const toThousands = (num: number, float?: boolean) => {
        if (float) {
            const res = num.toString().replace(/\d+/, n => n.replace(/(\d)(?=(\d{3})+$)/g, $1 => $1 + ','));
            return res;
        }
        return (num || 0).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
    };

    export const fixZero = (number: number) => `${number >= 10 ? '' : '0'}${number}`;

    export const toShortStr = (number: number) => number.toString(radix);

    export const recoverFromShort = (number_code: string) => parseInt(number_code, radix);

    /**
     * 分转万元函数
     * @param value 要处理的数值
     * @param isMoney 是否是金额，默认单位是分，需要先分转元
     * @param count 保留的小数位数
     * @returns string
     */
    export const formattToThousands = (value: number, isMoney?: boolean, count?: number, fixed?: number) => {
        // return value;
        let newValue = value;
        if (isMoney) {
            newValue = centToYuan(newValue);
        }
        const thousand = 10000;
        // 判断是否超过万，超过用万代替
        if (newValue >= thousand) {
            // 如果是金额，默认保留2位小数
            let num = parseInt(newValue.toString()) / thousand;
            if (isMoney) {
                return financial(String(mathFloor(num, count || 2))) + '万';
            }
            if (count) {
                return mathFloor(num, count || 2) + '万';
            }
            if (fixed) {
                num = Number(num.toFixed(fixed));
            }
            return num + '万';
        }
        // 最后
        return toThousands(newValue, true);
    };
}
export const uNumber: typeof _uNumber = {
    ..._uNumber,
};
