/**
 * @Owners init
 * @Title 入口文件
 */

import App from '@/App';
import '@/assets/iconfont/iconfont.css';
import { persistor, store } from '@/redux';
import { CommonStyle, GlobalStyle, themeConfig } from '@/styles';
import '@/styles/reset.css';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { ThemeProvider } from 'styled-components';
import 'virtual:svg-icons-register';

// react 17 方式创建，控制台会警告，暂时不影响使用，主要为解决菜单折叠时闪烁问题，ant官方兼容 react 18 后
// 使用 ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render() 替代即可
// https://github.com/ant-design/ant-design/issues/36152
ReactDOM.render(
    // <React.StrictMode>
    <Provider store={store}>
        <PersistGate persistor={persistor}>
            <ThemeProvider theme={themeConfig}>
                <CommonStyle />
                <App />
                <GlobalStyle />
            </ThemeProvider>
        </PersistGate>
    </Provider>,
    // </React.StrictMode>
    document.getElementById('root') as HTMLElement
);
