/**
 * @Owners init
 * @Title loading
 */
import Loading from '@/views/components/common/Loading';
import ReactDOM from 'react-dom/client';

let needLoadingRequestCount = 0;

// TODO 修复 loading 样式

// 显示loading
export const showFullScreenLoading = () => {
    if (needLoadingRequestCount === 0) {
        const dom = document.createElement('div');
        dom.setAttribute('id', 'loading');
        document.body.appendChild(dom);
        ReactDOM.createRoot(dom).render(<Loading />);
    }
    needLoadingRequestCount++;
};

// 隐藏loading
export const tryHideFullScreenLoading = () => {
    if (needLoadingRequestCount <= 0) return;
    needLoadingRequestCount--;
    if (needLoadingRequestCount === 0) {
        document.body.removeChild(document.getElementById('loading') as HTMLElement);
    }
};
