/**
 * @Owners zp
 * @Title app
 */
import { type RootState, useDispatch, useSelector } from '@/redux';
import router from '@/routers/index';
import UseAppPrompt from '@/views/components/UseAppPrompt/index';
import { uCookies } from '@utils';
import { App, ConfigProvider } from 'antd';
import { type ThemeConfig } from 'antd/es/config-provider';
import zhCN from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';
import { useContext, useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { ThemeContext } from 'styled-components';

import { setToken } from './redux/modules/global';

const Index = () => {
    const { assemblySize } = useSelector((state: RootState) => state.global);
    const themeContext = useContext(ThemeContext);

    const dispatch = useDispatch();

    // 监听 cookie 中 token 的删除
    useEffect(() => {
        uCookies.onChange('token', ({ deleted }) => {
            // 将会重定向登录页
            deleted && dispatch(setToken(''));
        });
    }, []);

    const antdTheme: ThemeConfig = {
        token: {
            borderRadius: themeContext.borderRadius,
            colorPrimary: themeContext.colors.primaryColor,
            colorError: themeContext.colors.colorError,
            colorLink: themeContext.colors.primaryColor,
            colorLinkHover: `rgba(${themeContext.colors.primaryColorRgba}, 0.8)`,
            colorBgLayout: themeContext.colors.menuBgColor,
        },
    };

    return (
        <ConfigProvider componentSize={assemblySize} theme={antdTheme} locale={zhCN}>
            <App message={{ maxCount: 5 }} component={false}>
                <UseAppPrompt />
                <RouterProvider router={router} />
            </App>
        </ConfigProvider>
    );
};

export default Index;
