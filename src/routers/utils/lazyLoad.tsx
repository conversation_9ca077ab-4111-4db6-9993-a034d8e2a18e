/**
 * @Owners init
 * @Title 路由懒加载
 */
import ErrorBoundary from '@/views/pages/error/ErrorBoundary';
import { Spin } from 'antd';
import React, { Suspense, type FC } from 'react';

/**
 * @description 路由加载动画
 * @param Comp 需要访问的组件
 * @returns element
 */
const lazyLoad = (Comp: React.LazyExoticComponent<() => JSX.Element> | React.LazyExoticComponent<FC>): React.ReactNode => (
    <ErrorBoundary>
        <Suspense
            fallback={
                <Spin
                    size='large'
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                    }}
                />
            }
        >
            <Comp />
        </Suspense>
    </ErrorBoundary>
);

export default lazyLoad;
