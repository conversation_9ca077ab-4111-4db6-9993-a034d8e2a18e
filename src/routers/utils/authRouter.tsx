/**
 * @Owners zp
 * @Title 路由守卫-组件
 */
import { currentPageHasCache, reloadModal, versionHasUpdate } from '@/api/helper/checkVersion';
import { HOME_URL, SMITH_PREFIX } from '@/config/config';
import { type RootState, useSelector } from '@/redux';
import pagesRouter from '@/routers/pagesRouter';
import { searchRoute } from '@utils';
import { useLayoutEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

const AuthRouter = (Ele: React.ElementType): React.ReactNode => {
    function WrappedComponent(props: {}) {
        // 远程资源是否更新
        const [remoteHasUpdate, setRemoteHasUpdate] = useState(false);

        const { token } = useSelector((state: RootState) => state.global);

        // Dynamic Router(动态路由，根据后端返回的菜单数据生成的一维数组)
        const { authRouter } = useSelector((state: RootState) => state.auth);

        const { pathname, search } = useLocation();

        // 比较版本
        const startCheckVersion = async () => {
            const hasUpdate = await versionHasUpdate();
            if (hasUpdate) {
                setRemoteHasUpdate(true);
                reloadModal();
            }
        };

        useLayoutEffect(() => {
            // 节点已缓存不检查版本
            if (!currentPageHasCache(pathname)) {
                startCheckVersion();
            }
        });

        const route = searchRoute(pathname, pagesRouter);

        // 判断当前路由是否需要访问权限(不需要权限直接放行)
        if (route.meta && !route.meta?.requiresAuth) return <Ele />;

        // 判断是否有Token，若无登录态，则重定向登录页
        if (!token) return <Navigate to={'/login'} state={{ pathname: pathname + search || '' }} replace />;

        // 已登录情况下smith通用路由直接通过
        if (pathname.startsWith(SMITH_PREFIX)) return <Ele />;

        // Static Router(静态路由，必须配置首页地址，否则不能进首页获取菜单、按钮权限等数据)，获取数据的时候会loading，所有配置首页地址也没问题
        const staticRouter = [HOME_URL, '/403'];
        const routerList = authRouter.concat(staticRouter);

        // 如果访问的地址没有在路由表中重定向到403页面
        if (routerList.indexOf(pathname) === -1) return <Navigate to='/403' />;

        // 当前账号有权限返回 Router，正常访问页面
        return <Ele {...props} remoteHasUpdate={remoteHasUpdate} />;
    }
    return <WrappedComponent />;
};

export default AuthRouter;
