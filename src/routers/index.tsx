/**
 * @Owners zp
 * @Title 路由 - config
 */
import AuthRouter from '@/routers/utils/authRouter';
import Layout from '@/views/layouts/index';
import Login from '@/views/pages/login/index';
import { Navigate, createBrowserRouter } from 'react-router-dom';

import pagesRouter, { errorRoutes } from './pagesRouter';

const rootRouter = [
    {
        path: '/',
        element: <Navigate to='/home/<USER>' />,
    },
    {
        path: '/login',
        element: <Login />,
    },
    {
        element: AuthRouter(Layout),
        children: pagesRouter,
    },
    ...errorRoutes,
    {
        path: '*',
        element: <Navigate to='/404' />,
    },
];

export default createBrowserRouter(rootRouter);
