/**
 * @Owners init
 * @Title 路由相关类型
 */
// import { type NonIndexRouteObject as ReactRouteObject } from 'react-router-dom';
export interface MetaProps {
    keepAlive?: boolean;
    requiresAuth?: boolean;
    title: string;
    key?: string;
}

export interface RouteObject {
    children?: RouteObject[];
    element?: React.ReactNode;
    index?: boolean;
    path?: string;
    meta?: MetaProps;
    isLink?: string;
}
