/**
 * @Owners init
 * @Title 直播助手-路由
 */
import { type RouteObject } from '@/routers/interface';
import lazyLoad from '@/routers/utils/lazyLoad';
import React from 'react';

const homeRouter: RouteObject[] = [
    {
        path: '/live/newConsole',
        element: lazyLoad(React.lazy(() => import('@/views/pages/live/newConsole'))),
        meta: {
            requiresAuth: true,
            title: '直播中控台',
            key: 'newConsole',
        },
    },
    {
        path: '/live/liveDataPage',
        element: lazyLoad(React.lazy(() => import('@/views/pages/live/liveDataPage'))),
        meta: {
            requiresAuth: false,
            title: '直播数据',
            key: 'liveDataPage',
        },
    },
    {
        path: '/live/create',
        element: lazyLoad(React.lazy(() => import('@/views/pages/live/create/index'))),
        meta: {
            requiresAuth: true,
            title: '创建直播间',
            key: 'create',
        },
    },
];

export default homeRouter;
