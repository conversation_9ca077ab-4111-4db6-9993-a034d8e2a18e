/**
 * @Owners init
 * @Title 首页-路由
 */
import { type RouteObject } from '@/routers/interface';
import lazyLoad from '@/routers/utils/lazyLoad';
import React from 'react';

const homeRouter: RouteObject[] = [
    {
        path: '/home/<USER>',
        element: lazyLoad(React.lazy(() => import('@/views/pages/home/<USER>'))),
        meta: {
            requiresAuth: true,
            title: '首页',
            key: 'home',
        },
    },
];

export default homeRouter;
