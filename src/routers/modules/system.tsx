/**
 * @Owners zp
 * @Title 系统管理 - 路由
 */
import { type RouteObject } from '@/routers/interface';
import lazyLoad from '@/routers/utils/lazyLoad';
import React from 'react';

const systemManagementRouter: RouteObject[] = [
    {
        path: '/system/menu',
        element: lazyLoad(React.lazy(() => import('@/views/pages/system/menu/index'))),
        meta: {
            requiresAuth: true,
            title: '菜单管理',
            key: 'menuManagement',
        },
    },
    {
        path: '/system/roles',
        element: lazyLoad(React.lazy(() => import('@/views/pages/system/roles/index'))),
        meta: {
            requiresAuth: true,
            title: '角色管理',
            key: 'RolesManagement',
        },
    },
];

export default systemManagementRouter;
