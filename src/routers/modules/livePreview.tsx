/**
 * @Owners init
 * @Title 首页-路由
 */
import { type RouteObject } from '@/routers/interface';
import lazyLoad from '@/routers/utils/lazyLoad';
import React from 'react';

const livePreviewRouter: RouteObject[] = [
    {
        path: '/live-preview/index',
        element: lazyLoad(React.lazy(() => import('@/views/pages/LivePreview/index'))),
        meta: {
            requiresAuth: true,
            title: '直播预告',
            key: 'live-preview',
        },
    },
    {
        path: '/previous-live/index',
        element: lazyLoad(React.lazy(() => import('@/views/pages/LivePrevious/index'))),
        meta: {
            requiresAuth: true,
            title: '往期直播场次',
            key: 'previous-live',
        },
    },
];

export default livePreviewRouter;
