/**
 * @Owners zp
 * @Title 错误页面-路由
 */
import NotAuth from '@/views/pages/error/403';
import NotFound from '@/views/pages/error/404';
import NotNetwork from '@/views/pages/error/500';

// 错误页面模块
const errorRouter = [
    {
        path: '/403',
        element: <NotAuth />,
        meta: {
            requiresAuth: true,
            title: '403页面',
            key: '403',
        },
    },
    {
        path: '/404',
        element: <NotFound />,
        meta: {
            requiresAuth: false,
            title: '404页面',
            key: '404',
        },
    },
    {
        path: '/500',
        element: <NotNetwork />,
        meta: {
            requiresAuth: false,
            title: '500页面',
            key: '500',
        },
    },
];

export default errorRouter;
