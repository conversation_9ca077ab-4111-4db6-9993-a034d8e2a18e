/**
 * @Owners zp
 * @Title 页面路由配置
 */
import { type RouteObject } from 'react-router-dom';

import error from './modules/error';

// 导入所有router
const metaRouters: MetaRouters = import.meta.glob('./modules/*.tsx', { eager: true });

// 处理路由
const routerArray: RouteObject[] = [];
Object.keys(metaRouters).forEach(item => {
    if (!item.includes('error')) {
        Object.keys(metaRouters[item]).forEach(key => {
            routerArray.push(...metaRouters[item][key]);
        });
    }
});

// 异常页不需要被layout包裹，单独抽出
export const errorRoutes = error;

export default routerArray;
