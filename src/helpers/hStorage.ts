/**
 * @Owners haoyang
 * @Title 本地持久化-管理器
 */

import { cConfig } from '@consts';

interface IStorage {}

/**
 * 本地持久化-管理器
 */
class HStorage {
    public static readonly instance = new HStorage();
    private constructor() {}

    private _data: IStorage | undefined;

    private get data() {
        if (!this._data) {
            const json = localStorage.getItem(cConfig.LOCAL_STORAGE_KEY);
            this._data = json ? (JSON.parse(json) as IStorage) : {};
        }
        return this._data;
    }

    /** 保存数据 */
    private saveData() {
        if (!this._data) return;
        const data = JSON.stringify(this._data);
        localStorage.setItem(cConfig.LOCAL_STORAGE_KEY, data);
    }

    /** 获取数据 */
    public get<T extends keyof IStorage>(key: T): IStorage[T] {
        return this.data[key];
    }

    /** 设置数据 */
    public set<T extends keyof IStorage>(key: T, value: IStorage[T]) {
        this.data[key] = value;
        this.saveData();
    }
}

export const hStorage = HStorage.instance;
