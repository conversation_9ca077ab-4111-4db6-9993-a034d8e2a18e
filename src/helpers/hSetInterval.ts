/**
 * @Owners lyc
 * @Title Interval 当前时间定时器
 */

type Fn = () => void;

class _hSetInterval {
    // 全局预售活动定时器回调队列
    public static globalTimeoutCallBackList = new Map<string, Fn>();
    public static instance = new _hSetInterval();
    public timer = 0;

    public resetTimer = () => {
        this.timer = 0;
    };

    public register(id: number | string, fn: () => void, timeout = 1000) {
        if (!this.timer) {
            this.timer = setInterval(this.taskFn, timeout) as unknown as number;
        }
        this.push(id, fn);
        return this.timer;
    }

    public push(id: number | string, fn: () => void) {
        _hSetInterval.globalTimeoutCallBackList.set(String(id), fn);
    }

    public clearAllInterval() {
        _hSetInterval.globalTimeoutCallBackList.clear();
        clearInterval(this.timer);
        this.timer = 0;
    }

    public clearInterval(id: number | string) {
        const isHas = _hSetInterval.globalTimeoutCallBackList.get(String(id));
        if (isHas) {
            _hSetInterval.globalTimeoutCallBackList.delete(String(id));
        }
    }

    public taskFn = () => {
        _hSetInterval.globalTimeoutCallBackList.forEach(fn => fn());
        if (_hSetInterval.globalTimeoutCallBackList.size === 0) {
            this.clearAllInterval();
        }
    };
}

export const hSetInterval = _hSetInterval.instance;
