/**
 * @Owners haoyang
 * @Title 路由管理器
 */

import router from '@/routers/index';

/**
 * 路由管理器
 */
class HRouter {
    public static readonly instance = new HRouter();

    /**
     * 获取路径
     */
    public getPathname() {
        return window.location.hash.replace('#', '');
    }

    /**
     * 路由跳转
     * @param path 路径
     * @param state 状态参数
     */
    public navigateTo(path: string, state: { [key: string]: unknown } = {}) {
        router.navigate(path, { state });
    }
}

export const hRouter = HRouter.instance;
