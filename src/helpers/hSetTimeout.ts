/**
 * @Owners jeannette
 * @Title Timeout 管理器
 */
class _hSetTimeout {
    public static instance = new _hSetTimeout();
    private readonly globalTimeoutList = new Map<string, number>();

    public register(fn: () => void, timeout: number, autoClear = true) {
        const handler = autoClear
            ? () => {
                  fn();
                  this.clearTimeout(timer);
              }
            : fn;
        const timer = setTimeout(handler, timeout) as unknown as number;
        this.push(timer);

        return timer;
    }

    public log = () =>
        console.log(`TimeOut: Size: ${this.globalTimeoutList.size}`, `TimeOut List: ${this.globalTimeoutList.size}`);

    public push(timer: number) {
        this.globalTimeoutList.set(String(timer), timer);
    }

    public clearAllTimeout() {
        this.globalTimeoutList.forEach(timer => {
            clearTimeout(timer);
        });
        this.globalTimeoutList.clear();
    }

    public clearTimeout(timer: number) {
        const _timer = this.globalTimeoutList.get(String(timer));
        if (_timer) {
            clearTimeout(_timer);
            this.globalTimeoutList.delete(String(timer));
        }
    }
}

export const hSetTimeout = _hSetTimeout.instance;
