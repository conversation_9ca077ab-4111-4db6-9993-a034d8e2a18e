/**
 * @Owners haoyang
 * @Title 页面按钮权限-hooks
 */

import { type RootState, useSelector } from '@/redux';
import pagesRouter from '@/routers/pagesRouter';
import { searchRoute } from '@utils';
import { useLocation } from 'react-router-dom';

/**
 * @description 页面按钮权限 hooks
 */
const useAuthButtons = () => {
    const { authButtons } = useSelector((state: RootState) => state.auth);
    const { pathname } = useLocation();
    const route = searchRoute(pathname, pagesRouter);

    return {
        BUTTONS: authButtons[route.meta?.key || ''] || {},
    };
};

export default useAuthButtons;
