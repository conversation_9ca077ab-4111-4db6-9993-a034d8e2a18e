/**
 * @Owners zgy
 * @Title useStateWithRef
 */

import { useCallback, useRef, useState } from 'react';

/**
 * 定义类型：支持直接传值或函数式更新
 */
type SetStateAction<S> = S | ((prevState: S) => S);

/**
 * useStateWithRef - 一个自定义 Hook，用于同步 state 和 ref
 *
 * @param initialValue 初始值
 * @returns [state, setState, ref] 返回的数组包含 state、setState 更新函数、和 ref
 */
function useStateWithRef<S>(initialValue: S): [S, (value: SetStateAction<S>) => void, React.MutableRefObject<S>] {
    // 定义 state
    const [state, setState] = useState<S>(initialValue);

    // 定义 ref，用于存储最新状态
    const stateRef = useRef<S>(initialValue);

    // 自定义 setState 方法，更新 state 和 ref
    const setStateWithRef = useCallback((value: SetStateAction<S>) => {
        if (typeof value === 'function') {
            // 如果是函数式更新，获取最新的 state
            setState(prevState => {
                const newValue = (value as (prevState: S) => S)(prevState);
                stateRef.current = newValue; // 同步更新 ref
                return newValue;
            });
        } else {
            // 如果是直接赋值
            setState(value);
            stateRef.current = value; // 同步更新 ref
        }
    }, []);

    return [state, setStateWithRef, stateRef];
}

export default useStateWithRef;
