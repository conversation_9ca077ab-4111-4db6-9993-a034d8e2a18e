/**
 * @Owners zhanghj
 * @Title 倒计时hook
 */
import { hSetInterval, hSetTimeout } from '@/helpers';
import { uUuid } from '@/utils/uUuid';
import { useEffect, useMemo, useRef, useState } from 'react';

export type Props = {
    /** 当前时间 */
    currentTime?: number;
    /** 结束时间 */
    endTime?: number;
    /** 时间差（不传递当前时间和结束时间，传递时间差也OK） */
    differTime?: number;
    /** 是否显示毫秒级别变动 默认为false */
    showMS?: boolean;
    /** 返回值是否需要补0 默认为true */
    prefix?: boolean;
    /** 最大单位 默认day */
    maxUnit?: 'day' | 'hour';
    /** 时间传进来满足执行回调条件时，是否立即执行回调，默认false执行 */
    noImmediate?: boolean;
    active?: boolean;
    /** 倒计时完成后的回调 */
    onEnd?(): void;
};

export type CbTime = {
    // 倒计时天数
    d: string;
    // 倒计时小时数
    h: string;
    // 倒计时分钟数
    m: string;
    // 倒计时秒数
    s: string;
    // 倒计时毫秒数
    ms: string;
};

/**
 * Interval hooks组件
 * @param fn 执行函数
 * @param delay 定时间间隔时间
 * @param options immediate为true时，先立即执行一次fn函数后再执行定时器
 */
function useInterval(
    fn: () => void,
    delay: number | null | undefined,
    options?: {
        immediate?: boolean;
    }
): void {
    const immediate = options?.immediate;
    const timerRef = useRef<string>(uUuid.newUUID());

    useEffect(() => {
        if (delay === undefined || delay === null) {
            return undefined;
        }
        if (immediate) {
            fn?.();
        }
        hSetInterval.register(
            timerRef.current,
            () => {
                fn?.();
            },
            delay
        );
        return () => {
            hSetInterval.clearInterval(timerRef.current);
        };
    }, [delay]);
}

const useCountDown = (options: Props): CbTime => {
    const {
        currentTime = 0,
        endTime = 0,
        differTime = 0,
        showMS = false,
        prefix = true,
        // maxUnit = 'hour',
        active = true,
        noImmediate,
        onEnd,
    } = options;
    const [diffTime, setDiffTime] = useState(0);
    /** 组件接收到参数时的时间 */
    const entryTime = useRef<number>(0);
    /** 当前倒计时要求的时间差 */
    const maxTime = useRef<number>(0);
    /** 是否可以执行回调 */
    const isImplementCb = useRef(false);
    /** 是否是day最大单位 */
    // const isDayMaxUnit = maxUnit === 'day';

    useEffect(() => {
        if (!active) {
            return undefined;
        }
        if (!isImplementCb.current) {
            isImplementCb.current = true;
        }
        if ((currentTime > 0 && endTime > 0) || differTime > 0) {
            entryTime.current = new Date().getTime();
            maxTime.current = differTime > 0 ? differTime : endTime - currentTime;
            if (maxTime.current <= 0 && noImmediate) {
                isImplementCb.current = false;
            }
            setDiffTime(maxTime.current);
        }
        return undefined;
    }, [currentTime, endTime, differTime, active]);

    useInterval(
        () => {
            const curtTimes = new Date().getTime();
            const TimeDifference = curtTimes - entryTime.current;
            setDiffTime(maxTime.current - TimeDifference);
        },
        diffTime <= 0 ? null : showMS ? 100 : 1000
    );

    const timeObj = useMemo(() => {
        const time = diffTime > 0 ? diffTime : 0;
        const d = Math.floor(time / (1000 * 60 * 60 * 24));
        const h = Math.floor((time % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const m = Math.floor((time % (1000 * 60 * 60)) / (1000 * 60));
        const s = Math.floor((time % (1000 * 60)) / 1000);
        const ms = Math.ceil((time * 1000) % 60);

        if (diffTime <= 0 && isImplementCb.current) {
            const timer = hSetTimeout.register(() => {
                onEnd?.();
                hSetTimeout.clearTimeout(timer);
            }, 0);
        }
        return {
            d: `${d}`,
            h: prefix ? `${h < 10 ? '0' : ''}${h}` : `${h}`,
            m: prefix ? `${m < 10 ? '0' : ''}${m}` : `${m}`,
            s: prefix ? `${s < 10 ? '0' : ''}${s}` : `${s}`,
            ms: prefix ? `${ms < 10 ? '0' : ''}${ms}` : `${ms}`,
        };
    }, [diffTime]);

    return timeObj || ({} as CbTime);
};

export default useCountDown;
