/**
 * @Owners zp
 * @Title 全局主题变量配置
 */
import { type DefaultTheme } from 'styled-components';

const themeConfig: DefaultTheme = {
    colors: {
        primaryColor: '#00B68F',
        primaryColorRgba: '#00B68F',
        colorError: '#FF4D4F',
        menuBgColor: '#F4FAFA',
        tabBgColor: '#fff',
        lightBgColor: '#ffffff',
        lightMainBgColor: '#f0f2f5',
        lightBorderColor: '#e4e7ed',
        lightBorderHeaderColor: '#f6f6f6',
        lightTextColor: 'rgba(0, 0, 0, 0.85)',
        lightShadowColor: '0 0 12px #0000000d',
        lightScrollbarBgColor: '#dddee0',
        scrollbarColor: '#f1eeee',
        scrollbarThumbColor: '#dddee0',
    },
    borderRadius: 6,
};

export default themeConfig;
