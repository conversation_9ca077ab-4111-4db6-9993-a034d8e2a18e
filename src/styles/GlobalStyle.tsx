/**
 * @Owners zp
 * @Title 需要自定义覆盖的全局样式
 */
import mainBg from '@/assets/images/main-bg.png';
import sideIcon from '@/assets/images/side-icon.png';
import { createGlobalStyle } from 'styled-components';

export default createGlobalStyle`
body {
	background-image:  url(${sideIcon}), url(${mainBg});
	background-size: 310px auto, 100% auto;
	background-position: 0 80%, top;
	background-repeat: no-repeat;
	background-color: #FAFAFA;
	#driver-highlighted-element-stage {
			background-color: #fff !important;
	}
}

/* login container（先固定样式） */
.login-container {
	background-color: #eeeeee !important;
	.login-box {
		background-color: hsl(0deg 0% 100% / 80%) !important;
		.login-form {
			background-color: transparent !important;
			box-shadow: 2px 3px 7px rgb(0 0 0 / 20%) !important;
			.login-logo {
				.logo-text {
					color: #475768 !important;
				}
			}
			.login-btn {
				.ant-btn-default {
					color: #606266 !important;
				}
			}
		}
	}
}

/* layout */
.ant-layout {
	background-color: transparent !important;
	background: none !important;
}
/* container */
.container {
	/* sider */
	.ant-layout-sider {
		.ant-menu {
            border-right: none !important;
            &::-webkit-scrollbar {
                background-color: ${props => props.theme.colors.scrollbarColor} !important;
            }
            &::-webkit-scrollbar-thumb {
                background-color: ${props => props.theme.colors.scrollbarThumbColor} !important;
            }
		}
	}

	/* layout */
	.ant-layout {
		background-color: transparent !important;
		background: none !important;
		.tabs,
		.footer,
		.card {
			background-color:${props => props.theme.colors.lightBgColor} !important;
			border-color: ${props => props.theme.colors.lightBorderColor} !important;
		}
		.ant-layout-header {
			height: 55px;
			padding: 0 20px 0 20px;
            background-color: transparent;
			border-color: transparent;
			.icon-style,
			.username {
				color: ${props => props.theme.colors.lightTextColor} !important;
			}
		}
		.footer {
			a {
				color:  ${props => props.theme.colors.lightTextColor} !important;
			}
		}
		.card {
			box-shadow: ${props => props.theme.colors.lightShadowColor} !important;
			.text {
				color: #585858 !important;
			}
		}
		.ant-layout-content {
			&::-webkit-scrollbar {
				background-color: ${props => props.theme.colors.lightMainBgColor} !important;
			}
			&::-webkit-scrollbar-thumb {
				background-color: ${props => props.theme.colors.lightScrollbarBgColor} !important;
			}
			.card {
				&::-webkit-scrollbar {
					background-color: ${props => props.theme.colors.lightBgColor} !important;
				}
				&::-webkit-scrollbar-thumb {
					background-color: ${props => props.theme.colors.lightScrollbarBgColor} !important;
				}
			}
		}
	}

    .ant-pro-card-title {
        font-weight: 600;
    }

    .ant-statistic-title {
        text-align: center;
    }

    .ant-statistic-content {
        text-align: center;
        width: 160px;
        font-size: 20px;
    }

}

/* 系统设置-菜单管理-新增 */
.sys-menu-icon-select{
    .rc-virtual-list-holder-inner {
        flex-direction: row !important;
        justify-content: start;
        align-content: flex-start;
        flex-wrap: wrap;
        height: 250px;

        .menu-select-option {
            width: 72px;
            height: 72px;

            .ant-select-item-option-content {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
            }
        }

    }
}
`;
