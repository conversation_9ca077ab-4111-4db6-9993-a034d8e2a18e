/**
 * @Owners xj
 * @Title 首页底部区域
 */

import { type Home } from '@/api/interface/home';
import { pastLivePage } from '@/api/modules/home';
import M3U8Player from '@/views/components/M3U8Player';
import { DownloadOutlined } from '@ant-design/icons';
import { ProCard, ProTable, type ActionType, type ProColumns } from '@ant-design/pro-components';
import { uString } from '@utils';
import { Button, Col, Image, Row } from 'antd';
import { forwardRef, useImperativeHandle, useRef } from 'react';
import { useNavigate } from 'react-router-dom';

type DataItem = Home.Response.PastLivePage['dataList'][number];

const _BottomBox = (_props: unknown, ref: React.Ref<unknown> | undefined) => {
    const navigate = useNavigate();
    const tableRef = useRef<ActionType>();

    useImperativeHandle(ref, () => ({
        resetFields: () => tableRef.current?.reload(),
    }));

    const columns: ProColumns<DataItem>[] = [
        {
            title: '直播间信息',
            dataIndex: 'coverUrl',
            width: 270,
            renderText: (_, row) => (
                <Row align={'middle'}>
                    <Col>
                        <Image width={80} src={row.coverUrl} />
                        {row.liveType === 1 && (
                            <div
                                style={{
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    background: '#FFFBDC',
                                    padding: '0 4px',
                                    borderRadius: '4px',
                                }}
                            >
                                公开
                            </div>
                        )}
                        {row.liveType === 2 && (
                            <div
                                style={{
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    background: '#FFFBDC',
                                    padding: '0 4px',
                                    borderRadius: '4px',
                                }}
                            >
                                私密
                            </div>
                        )}
                    </Col>
                    <Col offset={2}>
                        <div className='mle mw-150'>{row.liveTheme || '-'}</div>
                        <div className='sub-text-color'>房间号：{row.roomCode || '-'}</div>
                        <div className='sub-text-color'>直播时长：{row.totalLiveTimeStr || '-'}</div>
                    </Col>
                </Row>
            ),
        },
        {
            title: '成交金额',
            dataIndex: 'fullTotalGmv',
            renderText: (_, { fullTotalGmv }) => <div>¥ {fullTotalGmv || 0}</div>,
        },
        {
            title: '销售件数',
            dataIndex: 'fullSkuNumbers',
            renderText: (_, record: DataItem) => <div>{record.fullSkuNumbers || 0}件</div>,
        },
        {
            title: '观看人数',
            dataIndex: 'totalOnlineUsers',
            renderText: text => `${text || 0} 人`,
        },
        {
            title: '观看人次',
            dataIndex: 'totalOnlineCount',
            renderText: text => `${text || 0} 次`,
        },
        {
            title: 'UV价值',
            dataIndex: 'uvValue',
        },
        {
            title: '下单转化',
            dataIndex: 'orderRate',
            renderText: text => (text ? `${text}%` : '-'),
        },
        {
            title: '操作',
            key: 'option',
            render: (_, row) => [
                <Button key='btn-1' type='link' onClick={() => navigate(`/live/newConsole?liveId=${row.liveId}`)}>
                    详情
                </Button>,
                row.videoUrl ? <M3U8Player key='btn-2' src={row.videoUrl} /> : null,
                row.videoDownloadUrl ? (
                    <Button
                        key='btn-2'
                        icon={<DownloadOutlined />}
                        type='link'
                        // href={row.videoDownloadUrl}
                        onClick={() => {
                            uString.forceDownload(row.videoDownloadUrl);
                        }}
                        target='_blank'
                        variant='dashed'
                        color='default'
                        style={{
                            marginLeft: '12px',
                        }}
                    >
                        回放下载
                    </Button>
                ) : null,
            ],
        },
    ];

    return (
        <ProCard
            title='往期直播场次'
            className='mb-12'
            boxShadow
            extra={
                <Button type='link' onClick={() => navigate('/previous-live/index')}>
                    查看全部
                </Button>
            }
        >
            <ProTable<DataItem>
                actionRef={tableRef}
                rowKey='liveId'
                columns={columns}
                dateFormatter='string'
                options={false}
                search={false}
                ghost
                request={async params => {
                    const { current = 1, pageSize = 10 } = params;
                    const data = await pastLivePage({
                        page: current,
                        rows: pageSize,
                    });
                    return {
                        data: data.data?.dataList.length ? data.data?.dataList : [],
                        total: data.data?.total || 0,
                    };
                }}
                pagination={false}
            />
        </ProCard>
    );
};

const BottomBox = forwardRef(_BottomBox);
export default BottomBox;
