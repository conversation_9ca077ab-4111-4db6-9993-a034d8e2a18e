/**
 * @Owners xj
 * @Title 首页顶部区域
 */
import { type Home } from '@/api/interface/home';
import { getLiveOverview } from '@/api/modules/home';
import { useSelector } from '@/redux';
import { formatDuration } from '@/utils/uNumber';
import { RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { cLive } from '@consts';
import { Avatar, Button, Col, Empty, Row, Statistic, Tag } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const _TopBox = (_props: unknown, ref: React.Ref<unknown> | undefined) => {
    const { userInfo } = useSelector(state => state.global);
    const { nickname, phone, avatar, shopInfo } = userInfo;

    const navigate = useNavigate();

    const [dataSource, setDataSource] = useState<Home.Response.LiveOverview>();

    const getLiveOverviewInfo = async () => {
        const res = await getLiveOverview();
        setDataSource(res.data);
    };

    useImperativeHandle(ref, () => ({
        resetFields: getLiveOverviewInfo,
    }));

    useEffect(() => {
        getLiveOverviewInfo();
    }, []);

    return (
        <Row gutter={12}>
            <Col span={6} className='mb-12'>
                <ProCard boxShadow style={{ height: '100%' }}>
                    <div className='user-info'>
                        <Avatar className='user-avatar' size={50} src={avatar} />
                        <div>
                            <div className='user-name'>{nickname}</div>
                            <div className='user-phone'>{phone}</div>
                            <div className='sub-text-color'>店铺：{shopInfo?.shopName}</div>
                        </div>
                    </div>
                </ProCard>
            </Col>
            <Col span={18} className='mb-12'>
                <ProCard title='近期直播' boxShadow>
                    {dataSource ? (
                        <>
                            <Row gutter={24} className='mb-4'>
                                <Col className='sle mw-400'>{dataSource.liveName}</Col>
                                <Col flex={1}>
                                    <Tag color={cLive.PushStatusTextMap[dataSource.pushStatus].color}>
                                        {cLive.PushStatusTextMap[dataSource.pushStatus].text}
                                    </Tag>
                                </Col>
                                <Col onClick={() => navigate(`/live/newConsole?liveId=${dataSource.id}`)} className='link-btn'>
                                    直播详情
                                    <RightOutlined />
                                </Col>
                            </Row>
                            <div className='sub-text-color mb-12'>
                                {dataSource.liveRuntimeBigStatistics.liveStartTime ? (
                                    <span className='mr-20'>{dataSource.liveRuntimeBigStatistics.liveStartTime} 开播</span>
                                ) : (
                                    ''
                                )}
                                已直播 {formatDuration(dataSource.liveRuntimeBigStatistics.totalLiveTimes / 1000 || 0)}
                            </div>
                            <div className='statistic-list'>
                                <Statistic
                                    title='成交金额'
                                    value={dataSource.liveRuntimeBigStatistics.pureOrderTotalGmv}
                                    precision={2}
                                />
                                <Statistic title='下单用户数' value={dataSource.liveRuntimeBigStatistics.pureOrderUsers} />
                                {/* <Statistic title='UV价值' value={dataSource.liveSummary.uvValue} precision={2} /> */}
                                <Statistic title='销售件数' value={dataSource.liveRuntimeBigStatistics.pureOrderSkuNumbers} />
                                <Statistic title='在线人数' value={dataSource.liveRuntimeBigStatistics.currentViewsOnlineUsers} />
                                <Statistic title='观看人次' value={dataSource.liveRuntimeBigStatistics.totalViewsUsers} />
                                <Statistic
                                    title='下单转化'
                                    value={dataSource.liveRuntimeBigStatistics.pureOrderUsersRate + '%'}
                                    precision={2}
                                />
                                {/* <Statistic title='首单用户' value={dataSource.liveSummary.liveFirstOrderUsers} /> */}
                                {/* <Statistic
                                    title='平均观看时长'
                                    value={dataSource.liveRuntimeBigStatistics.avgViewTimes || '00:00:00'}
                                /> */}
                                {/* <Statistic title='新增关注' value={dataSource.liveSummary.increFans} /> */}
                                {/* <Statistic title='最高在线' value={dataSource.liveSummary.maxOnlineCount} /> */}
                                {/* <Statistic title='分享人数' value={dataSource.liveSummary.shareUsers} /> */}
                                {/* <Statistic title='分享次数' value={dataSource.liveSummary.shareCount} /> */}
                            </div>
                        </>
                    ) : (
                        <Empty imageStyle={{ display: 'none' }} image={Empty.PRESENTED_IMAGE_SIMPLE} description='近期暂未开播'>
                            <Button type='primary' onClick={() => navigate('/live/create?time=1')}>
                                立即开播
                            </Button>
                        </Empty>
                    )}
                </ProCard>
            </Col>
        </Row>
    );
};

const TopBox = forwardRef(_TopBox);
export default TopBox;
