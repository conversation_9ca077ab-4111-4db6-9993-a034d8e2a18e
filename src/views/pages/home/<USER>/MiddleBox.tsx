/**
 * @Owners xj
 * @Title 首页中间区域
 */

import { type Home } from '@/api/interface/home';
import { preLiveList } from '@/api/modules/home';
import { PlusCircleOutlined, RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { cLive } from '@consts';
import { Button, Empty, Image, Tag, Tooltip } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useNavigate } from 'react-router-dom';

const _MiddleBox = (_props: unknown, ref: React.Ref<unknown> | undefined) => {
    const [dataSource, setDataSource] = useState<Home.Response.PreLiveList>();
    const navigate = useNavigate();

    const getPreLiveList = async () => {
        const res = await preLiveList();
        setDataSource(res.data);
    };

    useImperativeHandle(ref, () => ({
        resetFields: getPreLiveList,
    }));

    useEffect(() => {
        getPreLiveList();
    }, []);
    return (
        <ProCard
            title='直播预告'
            boxShadow
            className='mb-12'
            extra={
                <div>
                    <span className='sub-text-color'>最多创建5场直播预告</span>
                    <span className='link-btn' onClick={() => navigate('/live/create?time=2')}>
                        <PlusCircleOutlined className='add-icon' />
                        创建直播预告
                    </span>
                </div>
            }
        >
            {dataSource && dataSource.length > 0 ? (
                <div className='list-content'>
                    <div className='live-list'>
                        {dataSource.map((i, index) => (
                            <div className='live-item' key={index}>
                                <div className='cover-box'>
                                    <Image src={i.liveCoverUrl ? i.liveCoverUrl : i.coverUrl} className='live-cover' />
                                </div>
                                <div className='live-title'>
                                    <Tooltip title={i.liveTheme}>{i.liveTheme}</Tooltip>
                                </div>
                                <div className='flx-justify-between  mb-4'>
                                    <span className='sub-text-color'>{i.preStartTime} 开播</span>
                                    <Tag color={cLive.PushStatusTextMap[i.pushStatus].color}>
                                        {cLive.PushStatusTextMap[i.pushStatus].text}
                                    </Tag>
                                </div>
                                <div className='flx-justify-between'>
                                    <div className='sub-text-color'>
                                        已预约<span className='people-count'>{i.subscribeNumber} 人</span>
                                    </div>
                                    <div onClick={() => navigate(`/live/newConsole?liveId=${i.liveId}`)} className='link-btn'>
                                        直播详情
                                        <RightOutlined />
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    <div className='view-all flx-center' onClick={() => navigate('/live-preview/index')}>
                        查看全部
                    </div>
                </div>
            ) : (
                <Empty imageStyle={{ display: 'none' }} image={Empty.PRESENTED_IMAGE_SIMPLE} description='暂无直播计划'>
                    <Button type='primary' onClick={() => navigate('/live/create?time=2')}>
                        创建预告
                    </Button>
                </Empty>
            )}
        </ProCard>
    );
};

const MiddleBox = forwardRef(_MiddleBox);
export default MiddleBox;
