/**
 * @Owners init
 * @Title 首页
 */

// import { findUserLivePermissions } from '@/api/modules/live/create';
import { type FormInstance } from 'antd';
import { useEffect, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';

import HomeContainer from './Style';
import BottomBox from './components/BottomBox';
import MiddleBox from './components/MiddleBox';
import TopBox from './components/TopBox';

const Home = () => {
    // const [isPublicLive, setIsPublicLive] = useState(false);

    const [searchParams] = useSearchParams();
    const reload = searchParams.get('reload');

    const topRef = useRef<FormInstance>();
    const middleRef = useRef<FormInstance>();
    const bottomRef = useRef<FormInstance>();

    // const handleGetPermission = async () => {
    //     const res = await findUserLivePermissions();
    //     if (res.data) {
    //         const _isPublicLive = res.data.permissions.includes('1');
    //         setIsPublicLive(_isPublicLive);
    //     }
    // };
    useEffect(() => {
        // handleGetPermission();
    }, []);

    useEffect(() => {
        if (reload === '1') {
            topRef?.current?.resetFields();
            middleRef?.current?.resetFields();
            bottomRef?.current?.resetFields();
        }
    }, [reload]);

    return (
        <HomeContainer className='homepage'>
            <TopBox ref={topRef} />
            <MiddleBox ref={middleRef} />
            <BottomBox ref={bottomRef} />
        </HomeContainer>
    );
};

export default Home;
