/**
 * @Owners init
 * @Title 首页-样式
 */

import styled from 'styled-components';

export default styled.div({
    paddingBottom: '1px',
    minWidth: 1000,
    '.user-info': {
        display: 'flex',
        backgroundColor: 'rgba(242, 242, 242, 1)',
        height: 'calc(100% - 32px)',
        padding: '20px 20px 10px',
        margin: '16px 0',
        borderRadius: 10,
        '.user-avatar': {
            marginRight: 16,
            minWidth: 50,
        },
        '.user-name': {
            fontSize: 20,
            lineHeight: '28px',
            marginBottom: '4px',
        },
        '.user-phone': {
            fontSize: 14,
            lineHeight: '22px',
            marginBottom: '4px',
        },
    },

    '.sub-text-color': {
        fontSize: 12,
        fontWeight: 400,
        fontStyle: 'normal',
        color: '#AAAAAA',
        lineHeight: '22px',
    },

    '.list-content': {
        position: 'relative',
        marginRight: '100px',
        '.view-all': {
            position: 'absolute',
            top: 0,
            right: '-124px',
            background: '#F2F2F2',
            width: '100px',
            height: '100%',
            borderRadius: '20px 0 0 20px',
        },
    },

    '.live-list': {
        display: 'flex',
        overflowX: 'auto',
        '.live-item': {
            background: '#F2F2F2',
            borderRadius: '20px',
            padding: '14px',
            margin: '0 30px 10px 0',
            '&:last-child': {
                marginRight: 0,
            },
            '.live-cover': {
                width: '250px',
                height: '200px',
                borderRadius: '10px 10px 0 0 ',
            },
            '.live-title': {
                margin: '4px 0',
                '&>span': {
                    display: 'inline-block',
                    maxWidth: '250px',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                },
            },
            '.people-count': {
                color: '#D9001B',
                marginLeft: '4px',
            },
        },
    },

    '.statistic-list': {
        display: 'flex',
        flexWrap: 'nowrap',
        overflowX: 'scroll',
        paddingBottom: '10px',
    },

    '.mb-4': {
        marginBottom: 4,
    },
    '.mb-12': {
        marginBottom: 12,
    },
    '.mr-20': {
        marginRight: 20,
    },
    '.mw-400': {
        maxWidth: 400,
    },
    '.mw-150': {
        maxWidth: 150,
    },
    '.link-btn': {
        cursor: 'pointer',
    },
    '.add-icon': {
        margin: '0 5px 0 10px',
        cursor: 'pointer',
    },
});
