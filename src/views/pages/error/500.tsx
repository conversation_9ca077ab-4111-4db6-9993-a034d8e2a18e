/**
 * @Owners zp
 * @Title 服务端问题/网络异常
 */
import { HOME_URL } from '@/config/config';
import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';

import ResultContainer from './Style';

const NotNetwork = () => {
    const navigate = useNavigate();

    const go = () => navigate(HOME_URL);

    return (
        <ResultContainer
            status='500'
            title='500'
            subTitle='Sorry, something went wrong.'
            extra={
                <Button type='primary' onClick={go}>
                    {'回到首页'}
                </Button>
            }
        />
    );
};

export default NotNetwork;
