/**
 * @Owners init
 * @Title 未授权-页面
 */

import { HOME_URL } from '@/config/config';
import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';

import ResultContainer from './Style';

const NotAuth = () => {
    const navigate = useNavigate();
    const goHome = () => {
        navigate(HOME_URL);
    };
    return (
        <ResultContainer
            status='403'
            title='403'
            subTitle='Sorry, you are not authorized to access this page.'
            extra={
                <Button type='primary' onClick={goHome}>
                    Back Home
                </Button>
            }
        />
    );
};

export default NotAuth;
