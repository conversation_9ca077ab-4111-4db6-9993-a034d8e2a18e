/**
 * @Owners init
 * @Title 丢失-页面
 */

import { HOME_URL } from '@/config/config';
import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';

import ResultContainer from './Style';

const NotFound = () => {
    const navigate = useNavigate();
    const goHome = () => {
        navigate(HOME_URL);
    };
    return (
        <ResultContainer
            status='404'
            title='404'
            subTitle='Sorry, the page you visited does not exist.'
            extra={
                <Button type='primary' onClick={goHome}>
                    Back Home
                </Button>
            }
        />
    );
};

export default NotFound;
