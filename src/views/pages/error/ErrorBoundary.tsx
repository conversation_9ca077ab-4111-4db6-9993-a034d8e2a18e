/**
 * @Owners zp
 * @Title 代码异常捕获/网络异常
 */
import React, { type ErrorInfo, type ReactNode } from 'react';

import ResultContainer from './Style';

type IProps = { children: ReactNode };
type IState = { hasError: boolean; error?: Error; isCodeException: boolean };

const ErrorTypeList: string[] = ['Error', 'TypeError', 'SyntaxError', 'RangeError', 'ReferenceError', 'EvalError'];

class ErrorBoundary extends React.Component<IProps, IState> {
    public constructor(props: IProps) {
        super(props);
        this.state = { hasError: false, isCodeException: false };
    }

    public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error(error);
        console.error(errorInfo.componentStack);
        if (error) {
            this.setState({
                hasError: true,
                error,
            });
            // 排除异步module加载失败的情况
            if (ErrorTypeList.includes(error?.name) && !error?.stack?.includes('Failed to fetch dynamically imported module')) {
                this.setState({ hasError: true, isCodeException: true, error });
            }
        }
    }

    public renderExceptionView = () => {
        const { error, isCodeException } = this.state;
        return (
            <ResultContainer
                status={'error'}
                subTitle={
                    isCodeException ? (
                        <span>
                            页面运行出错,错误信息:
                            <section>{error?.stack?.split('at')?.slice(0, 3)?.join('at')}</section>
                            <div>请联系开发人员处理</div>
                        </span>
                    ) : (
                        '页面加载失败，请检查网络'
                    )
                }
            />
        );
    };

    public render() {
        return this.state.hasError ? this.renderExceptionView() : this.props.children;
    }
}

export default ErrorBoundary;
