/**
 * @Owners blv
 * @Title 创建成功组件
 */
import { type Goods } from '@/api/interface/goods';
import { getAllLastLiveGoods } from '@/api/modules/live/create';
import { GoodStatusMap } from '@/consts/cGoods';
import { store } from '@/redux';
import { uDecimal } from '@/utils/uDecimal';
import { SelectGoodsModal } from '@/views/components/SelectGoodsModal';
import { CheckOutlined, EditOutlined } from '@ant-design/icons';
import {
    Button,
    Card,
    Col,
    Dropdown,
    InputNumber,
    Popconfirm,
    Popover,
    Row,
    Space,
    Table,
    Tooltip,
    message,
    type MenuProps,
} from 'antd';
import { type ColumnsType } from 'antd/es/table';
import { unionBy } from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

type GoodsItem = Goods.Response.getGoodsByPage['dataList'][number];

type Props = {};

enum MenuPropsKeys {
    /**
     * 手动添加
     */
    addShop = '5',
    /**
     * 导入上场直播商品
     */
    importLastShop = '6',
}

const items3: MenuProps['items'] = [
    {
        key: MenuPropsKeys.addShop,
        label: '手动添加',
    },
    {
        key: MenuPropsKeys.importLastShop,
        label: '导入上场直播商品',
    },
];

const _SelectGoods = (props: Props, ref: React.Ref<unknown> | undefined) => {
    const [goodsList, setGoodsList] = useState<GoodsItem[]>([]);
    const [selectedSpuIds, setSelectedSpuIds] = useState<number[]>([]);
    const [showSelectModal, setShowSelectModal] = useState<boolean>(false);
    const [operateSpuId, setOperateSpuId] = useState<number>(0);
    const [alterSortValue, setAlterSortValue] = useState<number>(0);
    const [serialNoSpuId, setSerialNoSpuId] = useState<number>(0);
    const [serialNoValue, setSerialNoValue] = useState<number>(0);

    const [loading, setLoading] = useState<boolean>(false);

    console.log('goodsList', goodsList);

    useEffect(() => {
        setGoodsList([]);
    }, []);

    const validateFields = async () => goodsList.map((v, i) => ({ spuId: v.spuId, sort: i + 1, serialNo: v.serialNo + 1 }));

    useImperativeHandle(ref, () => ({
        validateFields,
    }));

    const handleUpdateAlterSortValue = (spuId: number, oldSort: number) => {
        if (oldSort === alterSortValue) {
            setAlterSortValue(0);
            setOperateSpuId(0);
            return;
        }
        let distIndex = alterSortValue;
        if (distIndex < 1) {
            distIndex = 1;
        } else if (distIndex > goodsList.length) {
            distIndex = goodsList.length;
        }
        const oldIndex = goodsList.findIndex(i => i.spuId === spuId);
        const sortGoodsList = [...goodsList];
        const targetItem = goodsList[oldIndex];
        sortGoodsList.splice(oldIndex, 1);
        sortGoodsList.splice(distIndex - 1, 0, targetItem);
        setGoodsList(sortGoodsList);
        setAlterSortValue(0);
        setOperateSpuId(0);
    };

    // 商品链接序号更新
    const handleUpdateSerialNoValue = (oldSerialNoValue: number) => {
        if (oldSerialNoValue === serialNoValue) {
            setSerialNoValue(0);
            setSerialNoSpuId(0);
            return;
        }
        const lastSort = goodsList.length - 1;
        if (serialNoValue > lastSort) {
            const newItems = [...goodsList];
            const oldIndex = newItems.findIndex(v => v.serialNo === oldSerialNoValue - 1);
            const newIndex = newItems.findIndex(v => v.serialNo === goodsList.length - 1);
            const oldValue = newItems[oldIndex].serialNo;
            const newValue = newItems[newIndex].serialNo;
            newItems[oldIndex].serialNo = newValue;
            newItems[newIndex].serialNo = oldValue;
            setGoodsList(newItems);
        } else {
            const newItems = [...goodsList];
            const oldIndex = newItems.findIndex(v => v.serialNo === oldSerialNoValue - 1);
            const newIndex = newItems.findIndex(v => v.serialNo === serialNoValue - 1);
            const oldValue = newItems[oldIndex].serialNo;
            const newValue = newItems[newIndex].serialNo;
            newItems[oldIndex].serialNo = newValue;
            newItems[newIndex].serialNo = oldValue;
            setGoodsList(newItems);
        }
        setSerialNoValue(0);
        setSerialNoSpuId(0);
    };

    const handleSetData = (newGoodsList: GoodsItem[]) => {
        if (!newGoodsList.length) {
            return;
        }
        const hasAddSpuIds = goodsList.map(v => v.spuId);
        const canAddGoods = newGoodsList.filter(v => !hasAddSpuIds.includes(v.spuId));
        if (canAddGoods.length + goodsList.length > 500) {
            message.warning('最多选择500个商品');
            return;
        }
        setGoodsList(
            [...goodsList, ...canAddGoods].map((v, i) => ({
                ...v,
                sort: i,
                serialNo: i,
            }))
        );
        setShowSelectModal(false);
    };

    const handleBatchDelete = () => {
        if (!selectedSpuIds.length) {
            return message.warning('请先勾选要删除的商品～');
        }
        setGoodsList(goodsList.filter(i => !selectedSpuIds.includes(i.spuId)));
    };

    const handleDelete = (spuId: number, serialNo: number) => {
        const newGoodsList = goodsList
            .filter(v => v.spuId !== spuId)
            .map(v => {
                if (v.serialNo > serialNo) {
                    v.serialNo = v.serialNo - 1;
                }
                return v;
            });
        // console.log('newGoodsList: ', newGoodsList);
        setGoodsList(newGoodsList);
    };

    const columns = [
        {
            title: '商品位置序号',
            key: 'sort',
            width: 150,
            dataIndex: 'sort',
            align: 'center',
            render: (_text: string, record: GoodsItem, index: number) => (
                <Row justify='center' align='middle'>
                    <Col>
                        {operateSpuId === record.spuId ? (
                            <InputNumber
                                style={{ width: '70px' }}
                                value={alterSortValue}
                                min={1}
                                max={500}
                                precision={0}
                                onChange={e => {
                                    setAlterSortValue(e as number);
                                }}
                            />
                        ) : (
                            index + 1
                        )}
                    </Col>
                    <Col>
                        {operateSpuId === record.spuId ? (
                            <CheckOutlined onClick={() => handleUpdateAlterSortValue(record.spuId, index + 1)} />
                        ) : (
                            <EditOutlined
                                onClick={() => {
                                    setOperateSpuId(record.spuId);
                                    setAlterSortValue(index + 1);
                                }}
                            />
                        )}
                    </Col>
                </Row>
            ),
        },
        {
            title: '商品链接序号',
            key: 'sort',
            width: 120,
            dataIndex: 'sort',
            align: 'center',
            render: (_text: number, item: GoodsItem) => (
                <Row justify='center' align='middle'>
                    <Col>
                        {serialNoSpuId === item.spuId ? (
                            <InputNumber
                                style={{ width: '70px' }}
                                value={serialNoValue}
                                min={1}
                                max={500}
                                precision={0}
                                onChange={e => {
                                    setSerialNoValue(e as unknown as number);
                                }}
                            />
                        ) : (
                            item.serialNo + 1
                        )}
                    </Col>
                    <Col>
                        {serialNoSpuId === item.spuId ? (
                            <CheckOutlined onClick={() => handleUpdateSerialNoValue(item.serialNo + 1)} />
                        ) : (
                            <EditOutlined
                                onClick={() => {
                                    setSerialNoSpuId(item.spuId);
                                    setSerialNoValue(item.serialNo + 1);
                                }}
                            />
                        )}
                    </Col>
                </Row>
            ),
        },
        {
            title: '商品信息',
            key: 'spuId',
            width: 400,
            dataIndex: 'spuId',
            align: 'center',
            render: (_text: string, record: GoodsItem) => (
                <Row>
                    <div className='goods-info' style={{ display: 'flex', flexDirection: 'row' }}>
                        <img
                            src={(record.pics && record.pics[0]?.picUrl) || ''}
                            style={{ width: '65px', height: '65px', marginRight: '10px' }}
                        />
                        <span
                            style={{
                                textAlign: 'left',
                            }}
                        >
                            {record.spuName}
                        </span>
                    </div>
                </Row>
            ),
        },
        {
            title: '状态',
            key: 'status',
            width: 250,
            dataIndex: 'status',
            align: 'center',
            render: (text: string, record: GoodsItem) => (
                <>
                    <div>商品状态：{GoodStatusMap[text as '1'].text}</div>
                    <div>商品编码：{record.spuCode || '-'}</div>
                    <div>商品类型：{record.goodsType === 1 ? '普通商品' : '跨境商品'}</div>
                </>
            ),
        },
        {
            title: '属性',
            key: 'propertyValuesStr',
            dataIndex: 'propertyValuesStr',
            align: 'center',
            width: 400,
            render: (_text: string, record: GoodsItem) => (
                <>
                    {!!record.skus &&
                        !!record.skus.length &&
                        record.skus.slice(0, 1).map(i => (
                            <Row key={i.skuCode} justify={'start'}>
                                <Col span={10}>
                                    <Tooltip title={i.propertyValuesStr}>
                                        属性：<span>{i.propertyValuesStr ? i.propertyValuesStr.slice(0, 10) : '--'}</span>
                                    </Tooltip>
                                </Col>
                                <Col span={7}>价格：{uDecimal.FenToAmount(i.retailPrice)}</Col>
                                <Col span={7}>库存：{i.num}</Col>
                            </Row>
                        ))}
                    {!!record.skus && record.skus.length > 1 && (
                        <Popover
                            content={
                                <Table
                                    rowKey={'skuId'}
                                    pagination={false}
                                    dataSource={record.skus}
                                    style={{ width: '600px' }}
                                    columns={[
                                        { title: '属性', dataIndex: 'propertyValuesStr', key: 'propertyValuesStr' },
                                        {
                                            title: '价格',
                                            dataIndex: 'retailPrice',
                                            key: 'retailPrice',
                                            render: (value: number) => uDecimal.FenToAmount(value),
                                        },
                                        { title: '库存', dataIndex: 'num', key: 'num' },
                                    ]}
                                />
                            }
                        >
                            <Button type='link'>查看所有规格</Button>
                        </Popover>
                    )}
                </>
            ),
        },
        {
            title: '操作',
            key: 'spuId',
            width: 150,
            dataIndex: 'spuId',
            fixed: 'right',
            align: 'center',
            render: (_text: number, item: GoodsItem) => (
                <Space>
                    <Popconfirm title='确认删除此商品？' onConfirm={() => handleDelete(item.spuId, item.serialNo)}>
                        <Button type='link'>删除</Button>
                    </Popconfirm>
                </Space>
            ),
        },
    ] as unknown as ColumnsType<{}>;

    const handleGetAllLastLiveGoods = () => {
        setLoading(true);
        const { userInfo } = store.getState().global;
        getAllLastLiveGoods({ anchorUserId: userInfo.liveUserId || 0 })
            .then(res => {
                handleSetData(unionBy([...goodsList, ...(res.data?.dataList || [])], 'spuId'));
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const handleOnClick: MenuProps['onClick'] = ({ key }) => {
        switch (key) {
            case MenuPropsKeys.addShop:
                setShowSelectModal(true);
                break;
            case MenuPropsKeys.importLastShop:
                handleGetAllLastLiveGoods();
        }
    };

    return (
        <Card
            title='橱窗商品'
            extra={
                <Space>
                    <Button type='link' onClick={handleBatchDelete}>
                        批量删除商品
                    </Button>
                    <Dropdown
                        menu={{
                            items: items3,
                            onClick: handleOnClick,
                        }}
                        placement='bottom'
                        arrow={{ pointAtCenter: true }}
                    >
                        <Button
                            type='primary'
                            // onClick={() => {
                            //     setShowSelectModal(true);
                            // }}
                            loading={loading}
                        >
                            添加商品
                        </Button>
                    </Dropdown>
                </Space>
            }
        >
            {showSelectModal && (
                <SelectGoodsModal
                    goodsUpFlag={1}
                    goodsStatus={[3, 4]}
                    onOk={handleSetData}
                    onCancel={() => {
                        setShowSelectModal(false);
                    }}
                />
            )}
            <Table
                rowKey={'spuId'}
                pagination={false}
                dataSource={goodsList}
                columns={columns}
                scroll={{ x: 300, y: window.innerHeight - 400 }}
                rowSelection={{
                    type: 'checkbox',
                    selectedRowKeys: selectedSpuIds,
                    onChange: (selectedRowKeys: React.Key[]) => {
                        setSelectedSpuIds(selectedRowKeys as number[]);
                    },
                }}
            />
        </Card>
    );
};

const SelectGoods = forwardRef(_SelectGoods);
export default SelectGoods;
