/**
 * @Owners init
 * @Title 首页
 */

import MJUpload, { type UploadFile } from '@/views/components/common/MJUpload/index';
// import { cLive } from '@consts';
import { Card, DatePicker, Form, Input, Radio, Typography, type FormInstance } from 'antd';
import dayjs from 'dayjs';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

type Props = {
    isPreLive: boolean;
    // 是否公开直播间
    isPublicLive: boolean;
    // 权限列表
    permissions: number[];
};
const maxFileSize = 5 * 1024 * 1024;

const _BaseInfo = (props: Props, ref: React.Ref<unknown> | undefined) => {
    const { isPreLive, isPublicLive, permissions } = props;
    const formRef = useRef<FormInstance>(null);
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [liveImage, setLiveImage] = useState<UploadFile[]>([]);

    useEffect(() => {
        formRef.current?.setFieldsValue({
            liveMode: 1,
            pushType: 1,
            isRecord: !isPublicLive ? 0 : undefined,
        });
    }, [isPublicLive]);

    const validateFields = async () => formRef?.current?.validateFields();

    useImperativeHandle(ref, () => ({
        validateFields,
    }));

    return (
        <Card>
            <Form name='basic' labelCol={{ span: 3 }} wrapperCol={{ span: 12 }} ref={formRef}>
                <Typography.Title level={5}>基础信息</Typography.Title>
                <Form.Item label='直播间名称' name='liveName' rules={[{ required: true, message: '请输入直播间名称' }]}>
                    <Input maxLength={30} />
                </Form.Item>
                <Form.Item label='直播主题' name='liveTheme' rules={[{ required: true, message: '请输入直播间名' }]}>
                    <Input.TextArea maxLength={300} showCount />
                </Form.Item>
                <Form.Item
                    label='直播间分享封面'
                    name='coverUrl'
                    extra={'建议尺寸 ：5:4；分辨率：1000*800图片；大小：2M以下'}
                    rules={[{ required: true, message: '请上传直播间分享封面' }]}
                >
                    <MJUpload
                        name='file'
                        fileList={fileList}
                        maxFileLength={1}
                        acceptExt={'image/*'}
                        maxFileSize={maxFileSize}
                        maxFileSizeTips='图片超过2M,请重新上传'
                        listType='picture-card'
                        onChange={(_fileList: UploadFile[]) => {
                            setFileList(_fileList);
                        }}
                    />
                </Form.Item>
                <Form.Item
                    label='直播间封面'
                    name='liveCoverUrl'
                    extra={'尺寸 ：16:9；大小：2M以下'}
                    rules={[{ required: true, message: '请上传直播间封面' }]}
                >
                    <MJUpload
                        name='file'
                        fileList={liveImage}
                        maxFileLength={1}
                        acceptExt={'image/*'}
                        maxFileSize={maxFileSize}
                        maxFileSizeTips='图片超过2M,请重新上传'
                        listType='picture-card'
                        onChange={(_fileList: UploadFile[]) => {
                            setLiveImage(_fileList);
                        }}
                    />
                </Form.Item>
                <br />
                <Typography.Title level={5}>直播配置</Typography.Title>
                <Form.Item name='isRecord' label='是否开启录播' rules={[{ required: true, message: '请选择是否开启录播！' }]}>
                    <Radio.Group disabled={!isPublicLive}>
                        <Radio value={1}>开启</Radio>
                        <Radio value={0}>关闭</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item label='直播方式' name='liveMode'>
                    <Radio.Group>
                        <Radio value={1}>横屏直播</Radio>
                        <Radio value={2}>竖屏直播</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item label='推流类型' name='pushType'>
                    <Radio.Group>
                        <Radio value={1}>外设推流</Radio>
                        <Radio value={2}>手机推流</Radio>
                    </Radio.Group>
                </Form.Item>
                <Form.Item label='直播类型' name='liveType' rules={[{ required: true, message: '请选择直播类型！' }]}>
                    <Radio.Group>
                        {permissions.includes(1) && <Radio value={1}>公开直播</Radio>}
                        {permissions.includes(2) && <Radio value={2}>私密直播</Radio>}
                    </Radio.Group>
                    {/* <Select placeholder='请选择'>
                        {Object.keys(cLive.LiveTypeTextMap).map(i => (
                            <Select.Option value={+i} key={i}>
                                {cLive.LiveTypeTextMap[+i].text}
                            </Select.Option>
                        ))}
                    </Select> */}
                </Form.Item>
                {isPreLive && (
                    <Form.Item label='直播时间' name='preStartTime' rules={[{ required: true, message: '请选择直播时间' }]}>
                        <DatePicker
                            showTime
                            disabledDate={current =>
                                current && (current < dayjs().startOf('day') || current > dayjs().add(2, 'months').startOf('day'))
                            }
                        />
                    </Form.Item>
                )}
            </Form>
        </Card>
    );
};

const BaseInfo = forwardRef(_BaseInfo);
export default BaseInfo;
