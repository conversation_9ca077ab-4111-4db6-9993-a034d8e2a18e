/**
 * @Owners init
 * @Title 首页
 */
import { addLive, findUserLivePermissions } from '@/api/modules/live/create';
import { HOME_URL } from '@/config/config';
import { type UploadFile } from '@/views/components/common/MJUpload/index';
import { Button, Card, Space, Steps, message } from 'antd';
import { type FormInstance } from 'antd/lib/form/Form';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import BaseInfo from './BaseInfo';
import SelectGoods from './SelectGoods';
import SuccessInfo from './SuccessInfo';
type BaseInfo = {
    coverUrl: UploadFile[];
    liveName: string;
    liveTheme: string;
    isRecord: number;
    liveMode: number;
    pushType: number;
    preStartTime: string;
    liveType: number;
    liveCoverUrl: UploadFile[];
};

const Home = () => {
    const [current, setCurrent] = useState(0);
    const baseInfoRef = useRef<FormInstance>();
    const goodsListRef = useRef<FormInstance>();
    const [baseInfo, setBaseInfo] = useState<BaseInfo | null>(null);
    const [searchParams] = useSearchParams();
    const [isPreLive, setIsPreLive] = useState<boolean>(false);
    const [isPublicLive, setIsPublicLive] = useState(false);
    const [createLiveId, setCreateLiveId] = useState(0);
    const [permissions, setPermissions] = useState<number[]>([]);

    useEffect(() => {
        // 根据路由参数判断是立即开播还是直播预告
        const time = searchParams.get('time');
        setIsPreLive((time ? (+time === 2 ? 2 : 1) : 1) === 2);
        handleGetPermission();
    }, []);

    const handleGetPermission = async () => {
        const res = await findUserLivePermissions();
        if (res.data) {
            const _isPublicLive = res.data.permissions.includes(1);
            setIsPublicLive(_isPublicLive);
            setPermissions(res.data.permissions);
        }
    };

    const handleNext = async () => {
        const baseValues = (await baseInfoRef?.current?.validateFields()) as BaseInfo;
        setCurrent(1);
        setBaseInfo({
            coverUrl: baseValues.coverUrl,
            isRecord: baseValues.isRecord,
            liveMode: baseValues.liveMode,
            liveName: baseValues.liveName,
            liveTheme: baseValues.liveTheme,
            liveType: baseValues.liveType,
            liveCoverUrl: baseValues.liveCoverUrl,
            preStartTime: dayjs(baseValues.preStartTime).format('YYYY-MM-DD HH:mm:ss'),
            pushType: baseValues.pushType,
        });
    };

    const handlePrev = () => {
        setCurrent(0);
    };

    const handleSubmit = async () => {
        if (!baseInfo) {
            return;
        }
        const goodsList = (await goodsListRef?.current?.validateFields()) as { spuId: number; sort: number }[];
        const params = {
            preStartTime: isPreLive ? baseInfo.preStartTime : '',
            liveCategory: isPreLive ? 2 : 1,
            goodsList,
            coverUrl: baseInfo.coverUrl.length ? baseInfo.coverUrl[0].url || '' : '',
            isRecord: baseInfo.isRecord,
            liveMode: baseInfo.liveMode,
            liveName: baseInfo.liveName,
            liveTheme: baseInfo.liveTheme,
            pushType: baseInfo.pushType,
            liveCoverUrl: baseInfo.liveCoverUrl.length ? baseInfo.liveCoverUrl[0].url || '' : '',
            liveType: baseInfo.liveType,
        };
        const res = await addLive(params);
        if (res.data) {
            setCurrent(2);
            setCreateLiveId(res.data);

            if (res.msg === '部分会员专享商品因不支持分享，上架失败，请在待上架列表查看') {
                message.warning(res.msg);
            }
        }
    };

    const navigate = useNavigate();
    const location = useLocation();

    const handleBack = () => {
        const { pathname } = location.state || {};

        if (pathname) navigate(pathname);
        else navigate(HOME_URL);
    };

    return (
        <Card>
            <Space direction='vertical' style={{ width: '100%' }}>
                <Steps current={current} items={[{ title: '基本信息' }, { title: '直播间商品设置' }, { title: '完成' }]} />
                <br />
                <div style={{ display: current === 0 ? 'block' : 'none' }}>
                    <BaseInfo permissions={permissions} isPreLive={isPreLive} ref={baseInfoRef} isPublicLive={isPublicLive} />
                </div>
                <div style={{ display: current === 1 ? 'block' : 'none' }}>
                    <SelectGoods ref={goodsListRef} />
                </div>
                <div style={{ display: current === 2 ? 'block' : 'none' }}>
                    <SuccessInfo liveId={createLiveId} />
                </div>
                {current !== 2 && (
                    <Space>
                        {current === 0 ? (
                            <>
                                <Button type='primary' onClick={handleNext}>
                                    下一步
                                </Button>
                                <Button onClick={handleBack}>取消</Button>
                            </>
                        ) : (
                            <>
                                <Button onClick={handlePrev}>上一步</Button>
                                <Button type='primary' onClick={handleSubmit}>
                                    提交
                                </Button>
                            </>
                        )}
                    </Space>
                )}
            </Space>
        </Card>
    );
};

export default Home;
