/**
 * @Owners blv
 * @Title 创建成功组件
 */
import { HOME_URL } from '@/config/config';
import CheckCircleFilled from '@ant-design/icons/CheckCircleFilled';
import { Button, Row, Space, Typography } from 'antd';
import { useNavigate } from 'react-router-dom';

type Props = {
    liveId: number;
};
const SuccessInfo = (props: Props) => {
    const { liveId } = props;
    const navigate = useNavigate();

    const handleBack = () => {
        navigate(`${HOME_URL}?reload=1`);
    };

    const handleToLive = () => {
        navigate(`/live/newConsole?liveId=${liveId}`);
    };

    return (
        <Space direction='vertical' style={{ width: '100%' }}>
            <Row justify={'center'}>
                <CheckCircleFilled size={30} style={{ fontSize: '50px', color: '#52c41b' }} />
            </Row>
            <Row justify={'center'}>
                <Typography.Title level={4}>创建成功</Typography.Title>
            </Row>
            <Row justify={'center'}>
                <Typography.Paragraph style={{ padding: ' 0 100px', textAlign: 'center' }}>
                    直播间创建成功，推流模式若为外设推流，使用平台系统提供的推流地址，通过OBS等第三方平台输出直播流。
                    <br />
                    推流模式若为手机推流，则直接使用手机直接开播输出直播流。
                </Typography.Paragraph>
            </Row>
            <Row justify={'center'}>
                <Space>
                    <Button type='primary' onClick={handleBack}>
                        返回
                    </Button>
                    <Button onClick={handleToLive}>直播详情</Button>
                </Space>
            </Row>
        </Space>
    );
};

export default SuccessInfo;
