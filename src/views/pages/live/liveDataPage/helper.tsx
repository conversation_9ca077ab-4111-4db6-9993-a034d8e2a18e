/**
 * @Owners zgy
 * @Title utils
 */

import { cLive } from '@consts';

export function splitTimeString(input: string): string[] {
    const regex = /(\d+)([\u4e00-\u9fa5]+)/g;
    const result: string[] = [];
    let match: RegExpExecArray | null = regex.exec(input);
    while (match !== null) {
        result.push(match[1], match[2]); // 数字和单位依次加入数组
        match = regex.exec(input);
    }

    return result;
}

export function renderTimeString(str: number | string) {
    const s = splitTimeString(str.toString());
    return (
        <span>
            {s.map((item, index) => (
                <span
                    key={index}
                    style={{ fontSize: /\d+/.test(item) ? '32px' : '14px', fontWeight: /\d+/.test(item) ? 'bold' : 'normal' }}
                >
                    {item}
                </span>
            ))}
        </span>
    );
}

/**
 * 公开直播数据说明
 */
const liveDataPublicTips = {
    liveData: [
        '直播间成交金额: 直播期间，本直播间上架的产品，在全平台成功交易的实际支付总额（含退款）',
        '人均在线时长: 累计用户观看时长 ÷ 累计观看用户数',
        '订单数: 直播期间，本直播间上架的产品，在全平台成功交易的订单总量',
        '成交件数: 直播期间，本直播间上架的产品，在全平台成功在的商品总件数',
        '下单用户数: 直播期间，本直播间上架的产品，在全平台下单支付的独立用户数（同一用户多次下单计为 1 个）',
        '下单会员数: 直播期间，本直播间上架的产品，在全平台下单支付的独立会员数（同一会员多次下单计为 1 个）',
        '累计观看用户数: 直播期间，累计进入过的用户数（同一用户多次观看计为 1 个）',
        '最高同时在线人数: 直播期间，直播间内同时在线最高用户数',
        '会员单价: 直播期间，本直播间上架的产品，在全平台的会员下单金额 ÷ 下单会员数',
        '用户单价: 直播期间，本直播间上架的产品，在全平台的用户下单金额 ÷ 下单用户数',
        'UV价值: 直播期间，本直播间上架的产品，在全平台的下单金额 ÷ 直播间观看用户数（UV）',
    ],
    goodsData: [
        '出单商品数: 直播期间，有产生过成功交易的商品种类数（同一商品多次被购买计为 1 个）',
        '曝光用户数: 直播期间，曝光过直播间商品卡片的用户数',
        '点击用户数: 直播期间，点击过直播间商品卡片的用户数',
        '加购用户数: 直播期间，通过直播间加购商品的用户数',
        '下单用户数: 直播期间，本直播间上架的产品，在全平台下单支付的独立用户数（同一用户多次下单计为 1 个）',
        '直播间观看用户数: 直播期间，累计进入过的用户数（同一用户多次观看计为 1 个）',
        '商品曝光用户数: 直播期间，曝光过直播间商品卡片的用户数',
        '下单用户数: 直播期间，本直播间上架的产品，在全平台下单支付的独立用户数（同一用户多次下单计为 1 个）',
        '观看-下单率: 下单用户数 ÷ 直播间观看用户数（UV）',
        '观看-商品曝光率: 直播间商品曝光用户数 ÷ 直播间观看用户数（UV）',
        '商品曝光-下单率: 下单用户数 ÷ 直播间商品曝光用户数',
        '销售额: 直播期间，本直播间上架的产品，在全平台成功交易的实际支付总额（含退款）',
    ],
    platformData: [
        '销售额: 直播间用户成功交易的实际支付总额（含退款）',
        '成交件数: 直播间成功交易的商品总件数',
        '订单数: 直播间成功交易的订单总量',
        '下单用户数: 直播间下单支付的独立用户数（同一用户多次下单计为 1 个）',
    ],
    userData: [
        '累计观看会员数: 直播期间，累计进入过的会员数（同一用户多次观看计为 1 个）',
        '累计观看用户数: 直播期间，累计进入过的用户数（同一用户多次观看计为 1 个）',
        '人均在线时长: 累计用户观看时长 ÷ 累计观看用户数',
        '拉新会员数: 通过直播间产生的新注册会员数',
        '复购会员数: 会员历史下单过，且再次购买的商品是本直播在直播期间间上架产品的会员数（同一会员多次复购计为 1 个）',
        '首单用户数: 用户在全平台完成首次下单，且首次购买的商品是本直播在直播期间间上架产品的用户数',
        '拉新用户数: 通过直播间产生的新注册用户数',
        '复购用户数: 用户历史下单过，且再次购买的商品是本直播在直播期间间上架产品的用户数（同一用户多次复购计为 1 个）',
        '首单会员数: 会员在全平台完成首次下单，且首次购买的商品是本直播在直播期间间上架产品的会员数',
    ],
};

/**
 * 直播间数据-私密直播
 */
const liveDataPrivateTips = {
    liveData: [
        '直播间成交金额: 直播间成功交易的实际支付总额（含退款）',
        '人均在线时长: 累计用户观看时长 ÷ 累计观看用户数',
        '订单数: 直播间成功交易的订单总量',
        '成交件数: 直播间成功在的商品总件数',
        '下单用户数: 直播间下单支付的独立用户数（同一用户多次下单计为 1 个）',
        '下单会员数: 直播间下单支付的独立会员数（同一会员多次下单计为 1 个）',
        '累计观看用户数: 直播期间，累计进入过的用户数（同一用户多次观看计为 1 个）',
        '最高同时在线人数: 直播期间，直播间内同时在线最高用户数',
        '会员单价: 直播间会员下单金额 ÷ 下单会员数',
        '用户单价: 直播间用户下单金额 ÷ 下单用户数',
        'UV价值: 直播间下单金额 ÷ 直播间观看用户数（UV）',
    ],
    goodsData: [
        '出单商品数: 直播期间，有产生过成功交易的商品种类数（同一商品多次被购买计为 1 个）',
        '曝光用户数: 直播期间，曝光过直播间商品卡片的用户数',
        '点击用户数: 直播期间，点击过直播间商品卡片的用户数',
        '加购用户数: 直播期间，通过直播间加购商品的用户数',
        '下单用户数: 直播间下单支付的独立用户数（同一用户多次下单计为 1 个）',
        '直播间观看用户数: 直播期间，累计进入过的用户数（同一用户多次观看计为 1 个）',
        '商品曝光用户数: 直播期间，曝光过直播间商品卡片的用户数',
        '下单用户数: 直播间下单支付的独立用户数（同一用户多次下单计为 1 个）',
        '观看-下单率: 下单用户数 ÷ 直播间观看用户数（UV）',
        '观看-商品曝光率: 直播间商品曝光用户数 ÷ 直播间观看用户数（UV）',
        '商品曝光-下单率: 下单用户数 ÷ 直播间商品曝光用户数',
        '销售额: 直播间用户成功交易的实际支付总额（含退款）',
    ],
    userData: [
        '累计观看会员数: 直播期间，累计进入过的会员数（同一用户多次观看计为 1 个）',
        '累计观看用户数: 直播期间，累计进入过的用户数（同一用户多次观看计为 1 个）',
        '人均在线时长: 累计用户观看时长 ÷ 累计观看用户数',
        '拉新会员数: 通过直播间产生的新注册会员数',
        '复购会员数: 会员历史下单过，且本次在直播间再次下单支付的会员数（同一会员多次复购计为 1 个）',
        '首单用户数: 本直播间完成首次下单的用户数',
        '拉新用户数: 通过直播间产生的新注册用户数',
        '复购用户数: 用户历史下单过，且本次在直播间再次下单支付的用户数（同一用户多次复购计为 1 个）',
        '首单会员数: 本直播间完成首次下单的会员数',
    ],
    platformData: [
        '销售额: 直播期间，本直播间上架的产品，在全平台成功交易的实际支付总额（含退款）',
        '成交件数: 直播期间，本直播间上架的产品，在全平台成功在的商品总件数',
        '订单数: 直播期间，本直播间上架的产品，在全平台成功交易的订单总量',
        '下单用户数: 直播期间，本直播间上架的产品，在全平台下单支付的独立用户数（同一用户多次下单计为 1 个）',
    ],
};

export function getLiveDataTips(type: 'goodsData' | 'liveData' | 'platformData' | 'userData', liveType?: number) {
    const isPublic = liveType === cLive.LiveTypeMap.PUBLIC;
    return isPublic ? liveDataPublicTips[type] : liveDataPrivateTips[type];
}
