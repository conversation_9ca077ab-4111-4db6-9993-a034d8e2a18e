/**
 * @Owners zgy
 * @Title 直播订单表格卡片
 */

import { type Console } from '@/api/interface/live/console';
import { getOrderList } from '@/api/modules/live/console';
import { uNumber } from '@/utils/uNumber';
import { ProTable, type ProColumns } from '@ant-design/pro-components';
import { Card } from 'antd';
import classNames from 'classnames';

import './index.scss';

type OrderItem = Console.Response.LiveOrderVO;
type OrderParams = Console.Params.LiveOrderReqVO;

export default function OrderTableCard(props: { liveId: number; className?: string; title: string }) {
    const { liveId, className, title = '订单详细' } = props;
    // 定义表格列
    const columns: ProColumns<OrderItem>[] = [
        {
            title: '序号',
            dataIndex: 'index',
            width: 60,
            search: false,
            align: 'center',
            render: (_, __, index, action) => {
                // 计算行号：(当前页码-1) * 每页条数 + 当前行索引 + 1
                const current = action?.pageInfo?.current || 1;
                const pageSize = action?.pageInfo?.pageSize || 10;
                const rowIndex = (current - 1) * pageSize + index + 1;
                return rowIndex;
            },
        },
        {
            title: '订单编号',
            dataIndex: 'orderCode',
            width: 120,
            ellipsis: true,
            align: 'center',
        },
        {
            title: '下单时间',
            dataIndex: 'gmtCreate',
            width: 120,
            search: false,
            align: 'center',
        },
        {
            title: '下单商品',
            dataIndex: 'goodsName',
            width: 280,
            render: (_, { goodsName, goodsImgUrl }) => (
                <div className='goods-item'>
                    <img src={goodsImgUrl} alt={goodsName} />
                    <div className='goods-name'>{goodsName}</div>
                </div>
            ),
            search: true,
            align: 'center',
            formItemProps: {
                label: '商品名称',
            },
        },
        {
            title: '商品单价',
            dataIndex: 'skuPrice',
            width: 100,
            renderText: text => `¥${uNumber.centToYuan(Number(text) || 0)}`,
            search: false,
            align: 'center',
        },
        {
            title: '商品件数',
            dataIndex: 'skuNum',
            width: 100,
            search: false,
            align: 'center',
        },
        {
            title: '实付金额',
            dataIndex: 'goodsTotalAmount',
            width: 100,
            renderText: text => `¥${uNumber.centToYuan(Number(text) || 0)}`,
            search: false,
            align: 'center',
        },
        {
            title: '买家昵称',
            dataIndex: 'receiverNick',
            width: 150,
            render: (_, { receiverNick, avatar }) => (
                <div className='receiver-wrapper'>
                    <img src={avatar} alt={receiverNick} />
                    <div className='receiver-nick'>{receiverNick}</div>
                </div>
            ),
            search: false,
            align: 'center',
        },
        {
            title: '订单状态',
            dataIndex: 'orderStatus',
            width: 100,
            valueEnum: {
                1: { text: '待支付', status: 'warning' },
                2: { text: '待审核', status: 'warning' },
                3: { text: '待发货', status: 'processing' },
                4: { text: '出库中', status: 'processing' },
                5: { text: '已发货', status: 'success' },
                6: { text: '已签收', status: 'success' },
                7: { text: '已完成', status: 'success' },
                8: { text: '已取消', status: 'default' },
                9: { text: '已删除', status: 'default' },
            },
            align: 'center',
        },
    ];

    return (
        <Card className={classNames(['order-table-card', className])} title={title} bordered={false}>
            <ProTable<OrderItem, OrderParams>
                rowKey='orderCode'
                columns={columns}
                dateFormatter='string'
                options={false}
                search={{
                    labelWidth: 'auto',
                    // filterType: 'light',
                }}
                form={{
                    // 由于配置了 transform，提交的参与与定义的不同这里需要转化一下
                    syncToUrl: false,
                }}
                request={async params => {
                    if (!liveId) return { data: [], success: true };

                    const { current, pageSize, orderStatus, orderCode, goodsName } = params;

                    // 构建查询条件
                    const condition: OrderParams = {
                        liveId,
                        orderStatus,
                        orderCodeOrReceiverNick: orderCode,
                        goodsName,
                    };

                    const data = await getOrderList({
                        condition,
                        page: current,
                        rows: pageSize,
                    });

                    return {
                        data: data.data?.dataList || [],
                        total: data.data?.total || 0,
                        success: true,
                    };
                }}
                pagination={{
                    showQuickJumper: true,
                    showSizeChanger: true,
                }}
            />
        </Card>
    );
}
