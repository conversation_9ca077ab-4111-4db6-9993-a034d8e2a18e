@font-face {
    font-family: 'DIN-BOLD';
    src: url('https://applet.ifengqun.com/fq-mall/common/DINMIYUAN-Bold.otf');
}

.liveData {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    position: relative;

    padding-inline: 0;
    background-color: #ffffff;
    border-radius: 6px;
    margin-bottom: 12px;
    padding-bottom: 12px;
    .img {
        position: absolute;
        left: 0px;
        right: 0px;
        top: 0px;
        width: 100%;
        height: 10px;
    }
    .title {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #eeeeee;
        padding: 16px 24px;

        font-weight: 600;
        font-size: 20px;
        color: #333333;

        .left {
            .s {
                font-weight: 400;
                font-size: 14px;
                color: #999999;
                margin-left: 8px;
            }
        }
        .right {
            cursor: pointer;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            margin-left: 8px;

            .data-detail-text {
                margin-left: 2px;
            }
        }
    }
    .data {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 0 24px;

        .ant-statistic {
            min-width: 14.2%;
            flex: 1;
        }

        .ant-statistic-content {
            width: auto;
        }
    }

    .ant-statistic-title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #666666;
        line-height: 23px;
        margin-top: 20px;
    }
    .ant-statistic-content-value {
        font-family: DIN-BOLD;
        font-weight: 700;
        font-size: 32px;
        color: #333333;
        line-height: 40px;
    }
}
