/**
 * @Owners mzh
 * @Title 直播数据模块
 */
import { type Console } from '@/api/interface/live/console';
import { type LiveDataTsType } from '@/api/interface/live/liveData';
import { overviewData } from '@/api/modules/live/console';
import { getLiveData } from '@/api/modules/live/liveData';
import liveDataBack from '@/assets/images/liveDataBack.png';
import { formatDuration, uNumber } from '@/utils/uNumber';
import SvgIcon from '@/views/components/common/SvgIcon';
import TipsIcon from '@/views/components/common/TipsIcon';
import { RightOutlined } from '@ant-design/icons';
import { cLive } from '@consts';
import { Statistic } from 'antd';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { getLiveDataTips, renderTimeString } from '../../helper';

import './index.scss';

type Props = {
    isDetail?: boolean;
    setDataSource?(res: LiveDataTsType.Response.GetLiveDataResponse): void;
};

const App: React.FC<Props> = memo(({ setDataSource }) => {
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId') || 0);

    const [data, setData] = useState<Console.Response.LiveRuntimeBigStatisticsDTO>();
    const [liveData, setLiveData] = useState<LiveDataTsType.Response.GetLiveDataResponse>();
    const [time, setTime] = useState(dayjs().format('YYYY-MM-DD HH:mm'));

    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const isLiveEnd = useMemo(
        () => data?.pushStatus === cLive.PushStatusMap.StopPush || data?.pushStatus === cLive.PushStatusMap.Expired,
        [data]
    );
    const isConsolePage = useMemo(() => location.pathname.includes('/live/newConsole'), []);

    useEffect(() => {
        getLiveInfo();
        handleStart();
        return () => {
            handleEnd();
        };
    }, []);

    useEffect(() => {
        if (isLiveEnd) handleEnd();
    }, [isLiveEnd]);

    const getLiveInfo = async () => {
        overviewData({ liveId }).then(res => {
            const newTime = dayjs().format('YYYY-MM-DD HH:mm');
            setTime(newTime);
            setData(res.data);
        });

        getLiveData({ liveId }).then(res => {
            setLiveData(res.data as LiveDataTsType.Response.GetLiveDataResponse);

            setDataSource?.(res.data as LiveDataTsType.Response.GetLiveDataResponse);
        });
    };

    const handleStart = () => {
        handleEnd();
        intervalRef.current = setInterval(() => {
            getLiveInfo();
        }, 30 * 1000); // 5分钟 = 300000 毫秒
    };

    const handleEnd = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null; // 清除引用
        }
    };

    const handleToDataDetailPage = () => {
        window.open(`/live/liveDataPage?liveId=${liveId}`);
    };

    return (
        <div className='liveData'>
            <img src={liveDataBack} className='img' alt='' />
            <div className='title'>
                <div className='left'>
                    <span style={{ marginRight: '4px' }}>直播数据</span>
                    <TipsIcon content={getLiveDataTips('liveData', liveData?.liveType)} tipsTitle='数据说明' />
                    <span className='s'>{time}更新</span>
                    {isConsolePage && <span className='s'>（每30秒更新）</span>}
                </div>
                {isConsolePage && (
                    <div onClick={handleToDataDetailPage} className='right'>
                        <SvgIcon name='data-detail-icon' />
                        <span className='data-detail-text'>数据详情</span>
                        <RightOutlined style={{ fontSize: '12px' }} />
                    </div>
                )}
            </div>

            <div className='data'>
                <Statistic
                    title='直播间成交金额'
                    value={
                        liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? // 返回的单位是元
                              uNumber.centToWanYuan((liveData?.fullOrderTotalGmv || 0) * 100)
                            : uNumber.centToWanYuan((liveData?.pureOrderTotalGmv || 0) * 100) || '-'
                    }
                    suffix={<span style={{ fontSize: '14px' }}>元</span>}
                />
                <Statistic
                    title='订单数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullOrderTotalNumbers
                            : liveData?.pureOrderTotalNumbers) || '-'
                    }
                />
                <Statistic
                    title='成交件数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullOrderSkuNumbers
                            : liveData?.pureOrderSkuNumbers) || '-'
                    }
                />
                <Statistic
                    title='下单用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC ? liveData?.fullOrderUsers : liveData?.pureOrderUsers) ||
                        '-'
                    }
                />
                <Statistic
                    title='下单会员数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullOrderDerUsers
                            : liveData?.pureOrderDerUsers) || '-'
                    }
                />
                {/* <Statistic title='拉新用户数' value={liveData?.totalIncreUsers || '-'} />
                <Statistic title='拉新会员数' value={liveData?.totalIncreDerUsers || '-'} />
                <Statistic
                    title='首单会员数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullFirstOrderDealers
                            : liveData?.pureFirstOrderDealers) || '-'
                    }
                />
                <Statistic
                    title='首单用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullFirstOrderUsers
                            : liveData?.pureFirstOrderUsers) || '-'
                    }
                />
                <Statistic
                    title='复购会员数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullRepayOrderDealers
                            : liveData?.pureRepayOrderDealers) || '-'
                    }
                />
                <Statistic
                    title='复购用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullRepayOrderUsers
                            : liveData?.pureRepayOrderUsers) || '-'
                    }
                /> */}
                <Statistic
                    title='用户单价'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? uNumber.centToYuan(liveData?.fullUnitPrice || 0)
                            : uNumber.centToYuan(liveData?.pureUnitPrice || 0)) || '-'
                    }
                    suffix={<span style={{ fontSize: '14px' }}>元</span>}
                />
                <Statistic
                    title='会员单价'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? uNumber.centToYuan(liveData?.fullDerUnitPrice || 0)
                            : uNumber.centToYuan(liveData?.pureDerUnitPrice || 0)) || '-'
                    }
                    suffix={<span style={{ fontSize: '14px' }}>元</span>}
                />
                <Statistic
                    title='场观人次'
                    value={liveData?.totalViewsUserTimes || '-'}
                    // suffix={<span style={{ fontSize: '14px' }}>人</span>}
                />
                <Statistic
                    title='累积观看用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.totalViewsUsers
                            : liveData?.totalViewsUsers) || '-'
                    }
                />
                <Statistic
                    title='最高同时在线人数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.maxViewsOnlineUsers
                            : liveData?.maxViewsOnlineUsers) || '-'
                    }
                />
                <Statistic
                    title='人均在线时长'
                    value={formatDuration(liveData?.avgViewTimes || 0)}
                    formatter={renderTimeString}
                />
                <Statistic
                    title='UV价值'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? uNumber.centToYuan(liveData?.fullUvValue || 0)
                            : uNumber.centToYuan(liveData?.pureUvValue || 0)) || '-'
                    }
                    suffix={<span style={{ fontSize: '14px' }}>元</span>}
                />
                {liveData?.showReward === 1 ? (
                    <Statistic
                        title='预估直播奖励'
                        value={liveData?.pureRewardAmt || '-'}
                        suffix={<span style={{ fontSize: '14px' }}>元</span>}
                    />
                ) : (
                    <div className='ant-statistic' />
                )}
                <div className='ant-statistic' />
                <div className='ant-statistic' />
            </div>
        </div>
    );
});

export default App;
