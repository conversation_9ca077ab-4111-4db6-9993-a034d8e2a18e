/**
 * @Owners mzh
 * @Title 直播平台数据
 */
import { type LiveDataTsType } from '@/api/interface/live/liveData';
import { uNumber } from '@/utils/uNumber';
import TipsIcon from '@/views/components/common/TipsIcon';
import { cLive } from '@consts';
import { Card, Statistic } from 'antd';
import React, { memo, useMemo } from 'react';

import { getLiveDataTips } from '../../helper';

import './index.scss';

type Props = {
    liveData?: LiveDataTsType.Response.GetLiveDataResponse;
};

const LivePlatformData: React.FC<Props> = memo((props: Props) => {
    const { liveData } = props;

    const isPrivateLive = liveData?.liveType === cLive.LiveTypeMap.PRIVATE;

    const orderTotalGmv = useMemo(() => {
        const totalGmv = isPrivateLive ? liveData?.fullOrderTotalGmv : liveData?.pureOrderTotalGmv;
        if (!totalGmv) {
            return '-';
        }
        if (totalGmv < 10000) {
            return totalGmv;
        }
        return `${uNumber.yuanToWanYuan(totalGmv)}万`;
    }, [liveData]);

    return (
        <Card
            className='live-platform-data-card'
            title={
                <span>
                    <span style={{ marginRight: 4 }}>{isPrivateLive ? '平台数据' : '直播间数据'}</span>
                    <TipsIcon content={getLiveDataTips('platformData', liveData?.liveType)} tipsTitle='数据说明' />
                </span>
            }
            bordered={false}
        >
            <div className='statistic-box'>
                <Statistic title='销售额' value={orderTotalGmv} suffix={<span style={{ fontSize: '12px' }}>元</span>} />
                <Statistic
                    title='订单数'
                    value={(isPrivateLive ? liveData?.fullOrderTotalNumbers : liveData?.pureOrderTotalNumbers) || '-'}
                />
                <Statistic
                    title='成交件数'
                    value={(isPrivateLive ? liveData?.fullOrderSkuNumbers : liveData?.pureOrderSkuNumbers) || '-'}
                />
                <Statistic
                    title='下单用户数'
                    value={(isPrivateLive ? liveData?.fullOrderUsers : liveData?.pureOrderUsers) || '-'}
                />
            </div>
        </Card>
    );
});

export default LivePlatformData;
