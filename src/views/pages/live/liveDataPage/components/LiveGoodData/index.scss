.live-good-data-card {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    position: relative;
    height: 100%;

    .ant-card-head {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
    }

    .more-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        color: #666666;
    }

    .statistic-box {
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        row-gap: 16px;

    }

    .ant-statistic {
        width: 33.3%;
    }

    // .ant-statistic:nth-child(n + 3) {
    //     margin-top: 30px;
    // }
    .ant-statistic-title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 22px;
    }

    .ant-statistic-content-value {
        font-family: DIN-BOLD;
        font-weight: 700;
        font-size: 24px;
        color: #333333;
        line-height: 32px;
    }

    .ant-card-body {
        flex: 1;
        // display: flex;
        // align-items: center;
    }

    .sub-title {
        color: #333333;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .data-funnel {
        position: relative;
        margin: auto;
        padding: 0 30px;

        .data-item {
            position: absolute;
            color: white;
            text-align: right;
            font-size: 14px;
            padding: 4px 0;
            line-height: 1;

            .value {
                font-size: 16px;
                font-family: DIN-BOLD;
                line-height: 1;
                // margin-bottom: 8px;
            }

            &.rate {
                text-align: center;
                color: #666666;
                background-color: white;
                font-size: 12px;

                .value {
                    // margin-bottom: 10px;
                    color: #333333;
                }
            }
        }
    }
}