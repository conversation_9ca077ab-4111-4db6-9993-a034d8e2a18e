/**
 * @Owners mzh
 * @Title 直播商品数据
 */
import { type LiveDataTsType } from '@/api/interface/live/liveData';
import iconFunnel from '@/assets/images/data-funnel.png';
import TipsIcon from '@/views/components/common/TipsIcon';
import { RightOutlined } from '@ant-design/icons';
import { cLive } from '@consts';
import { Card, Divider, Statistic, message } from 'antd';
import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { getLiveDataTips } from '../../helper';

import './index.scss';

type Props = {
    liveData?: LiveDataTsType.Response.GetLiveDataResponse;
};

const LiveGoodData: React.FC<Props> = (props: Props) => {
    const { liveData } = props;
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId') || 0);

    const jumpToNewConsole = () => {
        if (liveId) {
            navigate(`/live/newConsole?liveId=${liveId}&activeTopTab=${cLive.ConsoleTopTabs.Shop}`, {
                replace: true,
            });
        } else {
            message.warning('直播间id不存在，请稍候');
        }
    };

    return (
        <Card
            className='live-good-data-card'
            title={
                <span>
                    <span style={{ marginRight: 4 }}>商品数据</span>
                    <TipsIcon content={getLiveDataTips('goodsData', liveData?.liveType)} tipsTitle='数据说明' />
                </span>
            }
            bordered={false}
            extra={
                <div onClick={jumpToNewConsole} className='more-btn'>
                    更多
                    <RightOutlined style={{ fontSize: '12px' }} />
                </div>
            }
        >
            <div className='statistic-box'>
                <Statistic
                    title='出单商品数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC ? liveData?.fullOrderGoods : liveData?.pureOrderGoods) ||
                        '-'
                    }
                />
                <Statistic
                    title='曝光用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullViewGoodsUsers
                            : liveData?.pureViewGoodsUsers) || '-'
                    }
                />
                <Statistic
                    title='点击用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullClickGoodsUsers
                            : liveData?.pureClickGoodsUsers) || '-'
                    }
                />
                <Statistic
                    title='加购用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullAddcarGoodsUsers
                            : liveData?.pureAddcarGoodsUsers) || '-'
                    }
                />
                <Statistic
                    title='下单用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC ? liveData?.fullOrderUsers : liveData?.pureOrderUsers) ||
                        '-'
                    }
                />
                {/* <Statistic
                    title='首单用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullFirstOrderUsers
                            : liveData?.pureFirstOrderUsers) || '-'
                    }
                />
                <Statistic
                    title='复购用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullRepayOrderUsers
                            : liveData?.pureRepayOrderUsers) || '-'
                    }
                /> */}
            </div>
            <Divider style={{ margin: '20px 0' }} />
            <div className='sub-title'>成交转化漏斗图</div>
            <div className='data-funnel'>
                <img src={iconFunnel} style={{ width: '100%', display: 'block' }} />
                <div className='data-item' style={{ top: 8, right: 90 }}>
                    <div className='value'>{liveData?.totalViewsUsers || '-'}</div>
                    <span>直播间观看用户数</span>
                </div>

                <div className='data-item' style={{ top: 88, right: 90 }}>
                    <div className='value'>
                        {(liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullViewGoodsUsers
                            : liveData?.pureViewGoodsUsers) || '-'}
                    </div>
                    <span>商品曝光用户数</span>
                </div>
                <div className='data-item' style={{ top: 170, right: 90 }}>
                    <div className='value'>
                        {(liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullOrderUsers
                            : liveData?.pureOrderUsers) || '-'}
                    </div>
                    <span>下单用户数</span>
                </div>
                <div className='data-item rate' style={{ top: 100, left: 0 }}>
                    <div className='value'>
                        {((liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullViewOrderRate
                            : liveData?.pureViewOrderRate) || 0) + '%' || '-'}
                    </div>
                    <span>观看-下单率</span>
                </div>
                <div className='data-item rate' style={{ top: 48, right: -16 }}>
                    <div className='value'>
                        {((liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullViewGoodsExposureRate
                            : liveData?.pureViewGoodsExposureRate) || 0) + '%' || '-'}
                    </div>
                    <span>观看-商品曝光率</span>
                </div>
                <div className='data-item rate' style={{ top: 139, right: -16 }}>
                    <div className='value'>
                        {((liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullGoodsExposureOrderRate
                            : liveData?.pureGoodsExposureOrderRate) || 0) + '%' || '-'}
                    </div>
                    <span>商品曝光-下单率</span>
                </div>
            </div>
        </Card>
    );
};

export default LiveGoodData;
