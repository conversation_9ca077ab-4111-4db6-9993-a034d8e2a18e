.live-user-data-card {
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    position: relative;

    .ant-card-head {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
    }

    .more-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        color: #666666;
    }

    .live-user-box {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    .ant-statistic-title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #666666;
        line-height: 23px;
    }

    .ant-statistic-content-value {
        font-family: DIN-BOLD;
        font-weight: 700;
        font-size: 24px;
        color: #333333;
        line-height: 40px;
    }

    .buy-user-box {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        border-top: 1px solid #eeeeee;
        padding-top: 20px;

        .ant-statistic {
            margin-top: 20px;
            min-width: 33.3%;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .ant-statistic-content {
            width: auto;
        }
    }

    &.large {
        .live-user-box {
            padding: 40px 0;
        }

        .buy-user-box {
            padding-bottom: 50px;

            .ant-statistic {
                margin-top: 50px;
            }
        }
    }
}