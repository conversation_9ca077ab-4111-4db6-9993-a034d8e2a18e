/**
 * @Owners mzh
 * @Title 直播用户数据
 */
import { type Console } from '@/api/interface/live/console';
import { formatDuration } from '@/utils/uNumber';
import TipsIcon from '@/views/components/common/TipsIcon';
import { RightOutlined } from '@ant-design/icons';
import { cLive } from '@consts';
import { Card, Statistic, message } from 'antd';
import React from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { getLiveDataTips, renderTimeString } from '../../helper';

import './index.scss';

type Props = {
    liveDetail?: Console.Response.LiveDetailInfo;
    liveData?: Console.Response.LiveRuntimeBigStatisticsDTO;
    size?: 'large' | 'small';
};

const LiveUserData: React.FC<Props> = (props: Props) => {
    const { liveData, size = 'small' } = props;
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId') || 0);

    const jumpToNewConsole = () => {
        if (liveId) {
            navigate(`/live/newConsole?liveId=${liveId}&activeTopTab=${cLive.ConsoleTopTabs.User}`, {
                replace: true,
            });
        } else {
            message.warning('直播间id不存在，请稍候');
        }
    };

    return (
        <Card
            className={`live-user-data-card ${size}`}
            title={
                <span>
                    <span style={{ marginRight: 4 }}>用户数据</span>
                    <TipsIcon content={getLiveDataTips('userData', liveData?.liveType)} tipsTitle='数据说明' />
                </span>
            }
            bordered={false}
            extra={
                <div onClick={jumpToNewConsole} className='more-btn'>
                    更多
                    <RightOutlined style={{ fontSize: '12px' }} />
                </div>
            }
        >
            <div className='live-user-box'>
                <Statistic title='最高同时在线人数' value={liveData?.maxViewsOnlineUsers || '-'} />
                <Statistic title='累计观看用户数' value={liveData?.totalViewsUsers || '-'} />
                <Statistic title='累计观看会员数' value={liveData?.totalViewsDerUsers || '-'} />
                <Statistic
                    title='人均在线时长'
                    value={formatDuration(liveData?.avgViewTimes || 0)}
                    formatter={renderTimeString}
                />
            </div>
            <div className='buy-user-box'>
                <Statistic title='拉新用户数' value={liveData?.totalIncreUsers || '-'} />
                <Statistic
                    title='首单用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullFirstOrderUsers
                            : liveData?.pureFirstOrderUsers) || '-'
                    }
                />
                <Statistic
                    title='复购用户数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullRepayOrderUsers
                            : liveData?.pureRepayOrderUsers) || '-'
                    }
                />
                <Statistic title='拉新会员数' value={liveData?.totalIncreDerUsers || '-'} />
                <Statistic
                    title='首单会员数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullFirstOrderDealers
                            : liveData?.pureFirstOrderDealers) || '-'
                    }
                />
                <Statistic
                    title='复购会员数'
                    value={
                        (liveData?.liveType === cLive.LiveTypeMap.PUBLIC
                            ? liveData?.fullRepayOrderDealers
                            : liveData?.pureRepayOrderDealers) || '-'
                    }
                />
            </div>
        </Card>
    );
};

export default LiveUserData;
