/**
 * @Owners mzh
 * @Title 直播数据页
 */

import { type LiveDataTsType } from '@/api/interface/live/liveData';
import { useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import LiveDataComponent from './components/LiveData';
import LiveGoodData from './components/LiveGoodData';
import LivePlatformData from './components/LivePlatformData';
import LiveUserData from './components/LiveUserData';
import OrderTableCard from './components/OrderTableCard';
import './index.scss';

const LiveDataPage = () => {
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId') || 0);
    const [liveData, setLiveData] = useState<LiveDataTsType.Response.GetLiveDataResponse>();

    const handleSetLiveData = (res: LiveDataTsType.Response.GetLiveDataResponse) => {
        setLiveData(res);
    };

    return (
        <div className='live-data-page'>
            <LiveDataComponent setDataSource={handleSetLiveData} />
            <div className='bottom-area'>
                <div className='live-good-data-box'>
                    <LiveGoodData liveData={liveData} />
                </div>
                <div className='live-user-data-box'>
                    {liveData?.liveType === 1 && (
                        <div className='live-platform-data'>
                            <LivePlatformData liveData={liveData} />
                        </div>
                    )}
                    <LiveUserData liveData={liveData} size={liveData?.liveType !== 1 ? 'large' : 'small'} />
                </div>
            </div>
            <OrderTableCard liveId={liveId} className='order-area' title={'订单详细'} />
        </div>
    );
};

export default LiveDataPage;
