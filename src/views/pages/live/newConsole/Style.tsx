/**
 * @Owners init
 * @Title 中控台-样式
 */

import styled from 'styled-components';

export default styled.div`
    padding-bottom: 1px;
    .mb-12 {
        margin-bottom: 12px;
    }
    .mr-10 {
        margin-right: 10px;
    }
    .ml-10 {
        margin-left: 10px;
    }
    .mr-20 {
        margin-right: 20px;
    }
    .mr-40 {
        margin-right: 40px;
    }
    .link-btn {
        color: #1677ff;
        cursor: pointer;
    }
    .live-info {
        display: flex;
        .left-box {
            font-size: 18px;
            margin-right: 50px;
            min-width: 200px;

            .live-cover {
                min-width: 100px;
                min-height: 80px;
                max-width: 200px;
                max-height: 160px;
            }
            .input-text {
                display: flex;
                & > :last-child {
                    margin-left: 10px;
                }
                & > div {
                    max-width: 200px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
        .right-box {
            max-width: 800px;
            min-height: 160px;
            & > :nth-child(1) {
                line-height: 28px;
            }
            .bottom-box {
                // 标签28 标签margin12 描述padding16
                height: calc(100% - 28px - 12px + 16px);
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }
        }
    }
    .tool-list {
        display: flex;
        justify-content: space-around;
        .tool-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            & > :nth-child(1) {
                margin-bottom: 10px;
            }
        }
    }
    .statistic-list {
        display: flex;
        flex-wrap: nowrap;
        overflow-x: scroll;
        padding-bottom: 10px;
    }
`;
