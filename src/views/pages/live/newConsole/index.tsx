/**
 * @Owners init
 * @Title 直播中控台
 */
import { type Console } from '@/api/interface/live/console';
import { createLiveDataQueryToken, getAnchorIsSupplier, getRecentlyPrePushPreLive } from '@/api/modules/live/console';
import AI_Icon from '@/assets/images/AI_Icon.png';
import { PushTypeMap } from '@/consts/cLive';
import { uString } from '@/utils';
import SvgIcon from '@/views/components/common/SvgIcon';
import { ProCard } from '@ant-design/pro-components';
import { cConfig, cLive } from '@consts';
import TencentCloudChat from '@tencentcloud/chat';
import { Button, Collapse, Flex, Tabs, Tooltip, message, type TabsProps } from 'antd';
import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import LiveData from '../liveDataPage/components/LiveData';

import AiAnalysis from './components/AiAnalysis';
import GoodsAnalysis from './components/GoodsAnalysis';
import HeadderLiveInfo from './components/HeadderLiveInfo';
import InteractiveLive from './components/InteractiveLive';
import LiveCard from './components/LiveCard';
import LivePlay from './components/LivePlay';
import LiveSettingsDrawer, { type LiveSettingsDrawerRef } from './components/LiveSettingsDrawer';
import PreLiveModal from './components/LiveSettingsDrawer/components/PreLiveModal';
import LiveStreamResultModal from './components/LiveStreamResultModal';
import Shop from './components/Shop';
import UserAnalysis from './components/UserAnalysis';
import UserList from './components/UserList';
import { LiveContext } from './contexts/LiveContext';
import { useChat } from './hooks/useChat';
import { useLiveDetail } from './hooks/useLiveDetail';
import './index.scss';
export type Dataitem = Console.Response.LiveDetailInfo;

const topItems: TabsProps['items'] = [
    {
        key: '1',
        label: '直播中控台',
    },
    {
        key: '2',
        label: '商品分析',
    },
    {
        key: '3',
        label: '用户分析',
    },
    {
        key: '4',
        label: (
            <div
                style={{
                    display: 'flex',
                    justifyItems: 'center',
                    alignItems: 'center',
                }}
            >
                <img
                    src={AI_Icon}
                    style={{
                        width: '24px',
                        height: '24px',
                    }}
                />
                诊断
            </div>
        ),
    },
];

const LiveConsole = () => {
    const [searchParams] = useSearchParams();
    const liveCardRef = useRef<HTMLDivElement>(null);
    const [liveToast, contextHolder] = message.useMessage({
        getContainer: () => liveCardRef.current as HTMLElement,
        maxCount: 3,
        top: 140,
    });
    const _liveId = Number(searchParams.get('liveId') || 0);
    const activeTopTabFromRoute = searchParams.get('activeTopTab');

    const { liveDetail, getLiveDetail, setLiveDetail } = useLiveDetail(_liveId);
    const { chat, chatInited } = useChat();
    const [liveId, setLiveId] = useState<number>();
    const [isEnd, setIsEnd] = useState(false);
    const [activeKey, setActiveKey] = useState<string>(cLive.ConsoleInteractiveTabs.Live);
    const [topActiveKey, setTopActiveKey] = useState<string>(
        activeTopTabFromRoute ? activeTopTabFromRoute : cLive.ConsoleTopTabs.Live
    );
    /** 是否供应商 */
    const [anchorIsSupplier, setAnchorIsSupplier] = useState(false);
    const liveSettingsDrawerRef = useRef<LiveSettingsDrawerRef>(null);
    /** 设置 tooltip 显隐 */
    const [settingTipVisible, setSettingTipVisible] = useState(false);

    const [preLiveInfo, setPreLiveInfo] = useState<Console.Response.GetRecentlyPrePushPreLive>();

    const [statisticsData, setStatisticsData] = useState<Console.Response.LiveRuntimeBigStatisticsDTO>();

    const showReward = statisticsData?.showReward === 1;

    useEffect(() => {
        const firstEnter = localStorage.getItem(cLive.FIRST_ENTER_CONSOLE);
        let timer: NodeJS.Timeout | null = null;
        if (!firstEnter || firstEnter !== '1') {
            localStorage.setItem(cLive.FIRST_ENTER_CONSOLE, '1');
            setSettingTipVisible(true);

            timer = setTimeout(() => {
                setSettingTipVisible(false);
            }, 10 * 1000);
        }
        fetchPrePushPreLive();

        return () => {
            if (timer) {
                clearTimeout(timer);
            }
        };
    }, []);

    const handlePushStateChange = () => {
        fetchPrePushPreLive();
    };

    const fetchPrePushPreLive = async () => {
        const resp = await getRecentlyPrePushPreLive();
        setPreLiveInfo(resp?.data);
    };

    // const [collapseActiveKey, setCollapseActiveKey] = useState<string>('2');

    const goDataScreen = async (path: string) => {
        const { data } = await createLiveDataQueryToken({ liveId: liveId || 0 });
        window.open(`${cConfig.DATA_SCREEN_FRONT}${path}?liveId=${liveId}&code=${data}`, '_blank');
    };

    useEffect(() => {
        if (_liveId) {
            setLiveId(Number(_liveId));
        }
    }, []);

    useEffect(() => {
        if (liveId) {
            fetchAnchorIsSupplier();
        }
    }, [liveId]);

    /** 获取是否供应商 */
    const fetchAnchorIsSupplier = async () => {
        const resp = await getAnchorIsSupplier();
        setAnchorIsSupplier(resp?.data?.isSupplier === 1);
    };

    /**
     * 处理接收到的消息
     */
    const handleCustomMessage = useMemo<Record<string, (msg: Console.ChatMessage) => void>>(
        () => ({
            /** 直播间状态 */
            [cLive.IMMessageCustomEvent.RoomStatus]: (msg: Console.ChatMessage) => {
                const { data } = msg.payload;
                const { status } = data;

                if (status === cLive.PushStatusMap.StopPush) {
                    setIsEnd(true);
                }
            },
            /** 主播开启连麦 */
            [cLive.IMMessageCustomEvent.LiveMicOpen]: () => {
                liveToast?.open({
                    content: '已开启多人聊天，观众可与你连麦聊天',
                });
            },
            /** 用户主动断开连麦 */
            [cLive.IMMessageCustomEvent.ManageMicUserDisconnect]: (msg: Console.ChatMessage) => {
                const { data } = msg.payload;
                const { nickname } = data;

                liveToast?.open({
                    content: `${uString.stringEllipsis(nickname, 5)}已断开连线`,
                });
            },
            /** 连麦观众接受/拒绝主播邀请打开摄像头 */
            [cLive.IMMessageCustomEvent.LiveMicInviteUserOpenCamera]: (msg: Console.ChatMessage) => {
                const { data } = msg.payload;
                const { isAccept, userId } = data;
                if (isAccept) return;
                liveToast?.open({
                    content: `${uString.stringEllipsis(userId.toString(), 5)}拒绝了你的视频邀请`,
                });
            },
        }),
        [liveToast]
    );

    /**
     * 处理接收到的消息
     */
    const handleReceivedMessage = useCallback(
        (event: { name: string; data: Console.ChatMessage[] }) => {
            const { data } = event;
            data.forEach(msg => {
                const { type, payload } = msg;
                // 只处理自定义事件
                if (type !== TencentCloudChat.TYPES.MSG_CUSTOM) return;

                const customData = payload.data ? JSON.parse(payload.data) : {};
                const customEvent: string = customData.event ?? '';
                handleCustomMessage[customEvent] &&
                    handleCustomMessage[customEvent]({
                        ...msg,
                        payload: {
                            ...msg.payload,
                            data: customData,
                        },
                    });
            });
        },
        [handleCustomMessage]
    );

    useEffect(() => {
        if (chatInited && chat?.current) {
            chat.current.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
        }

        return () => {
            if (chat?.current) {
                chat.current.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
            }
        };
    }, [chatInited, handleReceivedMessage]);

    const isLiveEnd =
        liveDetail?.pushStatus === cLive.PushStatusMap.StopPush || liveDetail?.pushStatus === cLive.PushStatusMap.Expired;

    const items: TabsProps['items'] = useMemo(
        () => [
            {
                key: cLive.ConsoleInteractiveTabs.Live,
                label: '实时互动',
                // children: <LiveCard />,
            },
            {
                key: cLive.ConsoleInteractiveTabs.UserList,
                label: '在线用户',
                // children: <UserList liveId={_liveId} groupId={liveDetail?.imGroupId || 0} />,
            },
            {
                key: cLive.ConsoleInteractiveTabs.InteractiveLive,
                label: '连麦',
                disabled: liveDetail?.pushType === PushTypeMap.OBS,
            },
        ],
        [liveDetail?.pushType]
    );

    const onTabChange = (key: string) => {
        // setCollapseActiveKey('2');
        setActiveKey(key);
    };

    const onTopActiveKeyChange = (key: string) => {
        setTopActiveKey(key);
    };

    return (
        <LiveContext.Provider
            value={{ chat, chatInited, liveId, liveDetail, getLiveDetail, tabActiveKey: activeKey, setLiveDetail, liveToast }}
        >
            <div className='console'>
                <ProCard className='mb-12'>
                    <div className='header'>
                        <HeadderLiveInfo liveDetail={liveDetail} />
                        <Flex>
                            <div className='tool-item' onClick={() => goDataScreen('/anchor')}>
                                {/* <Image src={dataIcon} width={16} preview={false} className='img' /> */}
                                <span className='s'>主播大屏</span>
                            </div>
                            <div className='tool-item' onClick={() => goDataScreen('/operation')}>
                                {/* <Image src={dataIcon} width={16} preview={false} className='img' /> */}
                                <span className='s'>助播大屏</span>
                            </div>
                        </Flex>
                    </div>
                </ProCard>
                <LiveData setDataSource={setStatisticsData} pushStatus={liveDetail?.pushStatus} />
                <ProCard className='mb-12 topActiveCard' style={{ padding: '0px' }}>
                    <Tabs
                        className='topActive'
                        defaultActiveKey={topActiveKey}
                        activeKey={topActiveKey}
                        items={topItems}
                        onChange={onTopActiveKeyChange}
                    />
                </ProCard>
                <div
                    className='flotter'
                    style={{
                        display: topActiveKey === cLive.ConsoleTopTabs.Live ? 'flex' : 'none',
                    }}
                >
                    <div className='live-console-bottom-left' ref={liveCardRef}>
                        {contextHolder}
                        <Collapse
                            bordered={false}
                            accordion
                            className='live-console-collapse'
                            expandIcon={({ isActive }) => (
                                <div className='live-console-collapse-icon'>
                                    <img
                                        src={'https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/arrow.png'}
                                        style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(180deg)', width: 22 }}
                                    />
                                </div>
                            )}
                            expandIconPosition='end'
                            defaultActiveKey={'1'}
                        >
                            <Collapse.Panel
                                key='1'
                                className='live-console-collapse-interaction'
                                header={
                                    <Tabs
                                        className='live-card-tabs'
                                        defaultActiveKey={activeKey}
                                        items={items}
                                        onChange={onTabChange}
                                        onClick={event => event.stopPropagation()}
                                        tabBarExtraContent={{
                                            right: (
                                                <Tooltip
                                                    placement='bottom'
                                                    title='直播推流地址、屏蔽关键词搬到这里啦！还有快捷评论可以设置哦～'
                                                    open={settingTipVisible}
                                                    onOpenChange={setSettingTipVisible}
                                                >
                                                    <Button
                                                        className='live-card-setting-button'
                                                        type='primary'
                                                        size='small'
                                                        icon={<SvgIcon name='setting-outlined' />}
                                                        onClick={() => liveSettingsDrawerRef.current?.open()}
                                                    >
                                                        设置
                                                    </Button>
                                                </Tooltip>
                                            ),
                                        }}
                                    />
                                }
                            >
                                <div
                                    className={classNames('live-console-bottom-left-tabs-tabpane', {
                                        'live-console-bottom-left-tabs-tabpane-hidden':
                                            activeKey !== cLive.ConsoleInteractiveTabs.Live,
                                    })}
                                >
                                    <LiveCard />
                                </div>
                                <div
                                    className={classNames('live-console-bottom-left-tabs-tabpane', {
                                        'live-console-bottom-left-tabs-tabpane-hidden':
                                            activeKey !== cLive.ConsoleInteractiveTabs.UserList,
                                    })}
                                >
                                    <UserList isLiveEnd={isLiveEnd} liveId={_liveId} groupId={liveDetail?.imGroupId || 0} />
                                </div>
                                <div
                                    className={classNames('live-console-bottom-left-tabs-tabpane', {
                                        'live-console-bottom-left-tabs-tabpane-hidden':
                                            activeKey !== cLive.ConsoleInteractiveTabs.InteractiveLive,
                                    })}
                                >
                                    <InteractiveLive />
                                </div>
                            </Collapse.Panel>
                            {anchorIsSupplier && (
                                <Collapse.Panel
                                    key='2'
                                    header={<div className='live-console-bottom-left-collapse'>直播工具</div>}
                                >
                                    <LivePlay liveId={_liveId} />
                                </Collapse.Panel>
                            )}
                        </Collapse>
                    </div>
                    {/* <LiveCard /> */}
                    <Shop isLiveEnd={isLiveEnd} liveDetail={liveDetail} liveId={_liveId} />
                </div>
                <div
                    style={{
                        display: topActiveKey === cLive.ConsoleTopTabs.User ? 'block' : 'none',
                    }}
                >
                    {topActiveKey === cLive.ConsoleTopTabs.User ? <UserAnalysis liveId={_liveId} /> : null}
                </div>

                {topActiveKey === cLive.ConsoleTopTabs.Shop && (
                    <GoodsAnalysis liveId={_liveId} isEnd={isEnd} showReward={showReward} />
                )}
                {topActiveKey === cLive.ConsoleTopTabs.Ai && <AiAnalysis liveId={_liveId} liveDetail={liveDetail} />}
            </div>
            {isEnd ? <LiveStreamResultModal liveId={_liveId} /> : null}

            <LiveSettingsDrawer ref={liveSettingsDrawerRef} handlePushStateChange={handlePushStateChange} />

            {preLiveInfo && (
                <PreLiveModal
                    liveId={_liveId}
                    countdownSec={preLiveInfo.preStartTimeSec ? Date.now() + preLiveInfo.preStartTimeSec * 1000 : 0}
                    preLiveInfo={preLiveInfo}
                    onLiveSettingsDrawerOpen={() => liveSettingsDrawerRef.current?.open()}
                />
            )}
        </LiveContext.Provider>
    );
};

export default LiveConsole;
