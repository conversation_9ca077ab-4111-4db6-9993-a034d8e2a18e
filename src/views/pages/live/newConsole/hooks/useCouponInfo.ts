/**
 * @Owners felix.cai
 * @Title 处理优惠券信息
 */

import { type Coupon } from '@/api/interface/coupon';
import { uNumber } from '@/utils/uNumber';
import { useMemo } from 'react';

/** 优惠类型 1 满减券 2 折扣券 3 无门槛 */
export enum DiscountType {
    /** 满减券 */
    Coupon = 1,
    /** 折扣券 */
    Discount = 2,
    /** 无门槛 */
    NoThreshold = 3,
}

type SupplierLiveCouponPageItem = Coupon.Response.supplierLiveCouponPage['dataList'][number];

export const useCouponInfo = (couponInfo?: SupplierLiveCouponPageItem) => {
    /** 满减券 */
    const isDecreaseActivity = useMemo(() => couponInfo?.discountType === DiscountType.Coupon, [couponInfo]);

    /** 折扣券 */
    const isDiscountActivity = useMemo(() => couponInfo?.discountType === DiscountType.Discount, [couponInfo]);

    /** 无门槛券 */
    const isCashActivity = useMemo(() => couponInfo?.discountType === DiscountType.NoThreshold, [couponInfo]);

    /** 面额 */
    const denomination = useMemo(() => {
        let _denomination = 0;

        const sub = couponInfo?.discountRuleModels?.[0]?.sub ?? 0; // 减
        const cash = couponInfo?.discountRuleModels?.[0]?.cash ?? 0; // 无门槛
        const discount = couponInfo?.discountRuleModels?.[0]?.discount ?? 0; // 折扣

        switch (couponInfo?.discountType) {
            case DiscountType.Coupon:
                _denomination = uNumber.centToYuan(sub);
                break;
            case DiscountType.Discount:
                _denomination = discount;
                break;
            case DiscountType.NoThreshold:
                _denomination = uNumber.centToYuan(cash);
                break;
            default:
        }

        return _denomination;
    }, [couponInfo]);

    /** 优惠券描述 */
    const couponDesc = useMemo(() => {
        if (couponInfo) {
            const discountRuleModel = couponInfo?.discountRuleModels?.[0];
            const isFullCounts = couponInfo?.fullSubtractType === 2; // 满减类型 1 满额减 2 满件减
            const to = discountRuleModel?.to || 0; // 满
            // const sub = discountRuleModel?.sub || 0; // 减
            const cash = discountRuleModel?.cash || 0; // 无门槛
            // const discount = discountRuleModel?.discount || 0; // 折扣

            switch (couponInfo?.discountType) {
                // 满减券
                case DiscountType.Coupon:
                    if (isFullCounts) return `满${to}件可用`;

                    return `满${uNumber.centToYuan(to)}元可用`;
                // 折扣券
                case DiscountType.Discount:
                    return `满${uNumber.centToYuan(to)}可用`;
                // 无门槛券
                case DiscountType.NoThreshold:
                    return `${uNumber.centToYuan(cash)}元无门槛券`;
                default:
                    return `满${uNumber.centToYuan(to)}可用`;
            }
        }
        return '';
    }, [couponInfo]);

    /** 优惠券商品可用范围 */
    const goodsScopeInfo = useMemo(() => {
        if (couponInfo && couponInfo?.spuInfoList) {
            return {
                categories: couponInfo?.spuInfoList?.[0]?.categoryName ?? '-',
                spuPic: couponInfo?.spuInfoList?.[0]?.spuPic,
            };
        }

        return null;
    }, [couponInfo]);

    /** 优惠券可用类型 */
    const couponTypeText = useMemo(() => {
        const scopeType = couponInfo?.spuScopeType;
        if (couponInfo && scopeType) {
            // 是否是多商品
            // const isMultiGoods = couponInfo?.spuInfoList?.length > 1;

            // 1.全部 2.指定商品 3.前台类目 4.品牌 5.专题
            switch (scopeType) {
                case 1:
                    return `${couponInfo?.spuInfoList?.[0]?.categoryName}可用`;
                case 2:
                    return '指定商品可用';
                case 3:
                    return '指定品类可用';
                case 4:
                    return '指定品牌可用';
                default:
                    return '';
            }
        }

        return '';
    }, [couponInfo]);

    return {
        /** 满减券 */
        isDecreaseActivity,
        /** 折扣券 */
        isDiscountActivity,
        /** 无门槛券 */
        isCashActivity,
        /** 面额 */
        denomination,
        /** 优惠券描述 */
        couponDesc,
        /** 优惠券商品可用范围 */
        goodsScopeInfo,
        /** 优惠券可用类型 */
        couponTypeText,
    };
};
