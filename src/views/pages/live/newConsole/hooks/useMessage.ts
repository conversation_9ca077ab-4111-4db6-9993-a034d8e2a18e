/**
 * @Owners ljh
 * @Title 聊天消息
 */
import { type Console } from '@/api/interface/live/console';
import { getUserMutedList } from '@/api/modules/live/console';
import { IMMessageCustomEvent } from '@/consts/cLive';
import { cLive } from '@consts';
import TencentCloudChat, { type Message } from '@tencentcloud/chat';
import { pick } from 'lodash';
import { type SetStateAction, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';

import { LiveContext } from './../contexts/LiveContext';

export interface UseMessageOptions {
    groupId: number;
}

export const useMessage = (options: UseMessageOptions) => {
    const { groupId } = options;
    const { chat, chatInited, tabActiveKey } = useContext(LiveContext);
    const [messageList, _setMessageList] = useState<Console.ChatMessage[]>([]); // 消息列表 旧 -> 新
    const [userMutedList, setUserMutedList] = useState<number[]>([]); // 用户禁言列表
    const isFirstRender = useRef(true); // 是否第一次加载
    const autoScrollBottom = useRef(true); // 是否自动滚动到底部
    const stopRenderIndex = useRef<number>(0); // 停止渲染索引
    const [scrollToIndex, setScrollToIndex] = useState<number>(); // 滚动到指定索引
    const newMessageListCacheRef = useRef<Console.ChatMessage[]>([]); // 新消息缓存，当列表看不到的时候使用
    const throttleUpdateTimer = useRef<NodeJS.Timeout | null>(null); // 节流更新
    const [unreadMessageCount, setUnreadMessageCount] = useState<number>(0); // 未读消息数量

    /**
     * @description: 二次封装设置消息列表
     * @param value 消息列表
     * @param ignoreUnread 是否忽略未读（因为防止设置历史记录导致计算未读）
     */
    const setMessageList = useCallback((value: SetStateAction<Console.ChatMessage[]>, ignoreUnread = false) => {
        if (!value || Array.isArray(value)) {
            _setMessageList(value);
            return;
        }
        _setMessageList(prev => {
            autoScrollBottom.current = prev.length - 1 <= stopRenderIndex.current;
            if (!autoScrollBottom.current) {
                setScrollToIndex(undefined);
                if (!ignoreUnread) {
                    setUnreadMessageCount(prev => prev + value.length);
                }
            }
            return value(prev);
        });
    }, []);

    useEffect(() => {
        getUserMutedList({
            groupId,
        }).then(res => {
            setUserMutedList(uml => uml.concat(res.data?.map(item => item.imUserId) ?? []));
        });
    }, []);

    useEffect(() => {
        if (messageList.length > 0 && autoScrollBottom.current) {
            setScrollToIndex(messageList.length - 1);
            isFirstRender.current = false;
        }
    }, [messageList]);

    useEffect(() => {
        // 加入群聊
        if (chatInited && groupId && chat?.current) {
            chat?.current.joinGroup({
                groupID: String(groupId),
                // applyMessage: import.meta.env.MODE === 'production' ? '1496493177438304' : '1545875406979104',
                type: TencentCloudChat.TYPES.GRP_AVCHATROOM,
            });
        }
    }, [chatInited, groupId]);

    /**
     * @description: 简化消息体
     */
    const pickMessage = useCallback((msg: Message): Console.ChatMessage => {
        const pickedMessage = pick(msg, [
            'cloudCustomData',
            'conversationType',
            'from',
            'ID',
            'nick',
            'payload',
            'to',
            'type',
            'msgStatus',
        ]) as Console.ChatMessage;
        pickedMessage.cloudCustomData = msg.cloudCustomData ? JSON.parse(msg.cloudCustomData) : undefined;
        return pickedMessage;
    }, []);

    /**
     * @description: 节流更新
     */
    const startThrottleUpdate = useCallback(() => {
        if (throttleUpdateTimer.current) return;
        throttleUpdateTimer.current = setTimeout(() => {
            setMessageList(prev => [...prev, ...newMessageListCacheRef.current]);
            newMessageListCacheRef.current = [];
            throttleUpdateTimer.current = null;
        }, 1000);
    }, [setMessageList]);

    useEffect(() => {
        if (tabActiveKey === cLive.ConsoleInteractiveTabs.Live) {
            startThrottleUpdate();
        }
    }, [tabActiveKey, startThrottleUpdate]);

    /**
     * 处理消息
     */
    const handleMessage = useMemo<Record<string, (msg: Message) => void>>(
        () => ({
            /**
             * 文本消息
             */
            [TencentCloudChat.TYPES.MSG_TEXT]: (msg: Message) => {
                const { conversationType } = msg;
                if (conversationType !== TencentCloudChat.TYPES.CONV_GROUP) {
                    return;
                }

                newMessageListCacheRef.current.push(pickMessage(msg));

                if (tabActiveKey === cLive.ConsoleInteractiveTabs.Live) {
                    startThrottleUpdate();
                }
            },
            /**
             * 自定义消息
             */
            [TencentCloudChat.TYPES.MSG_CUSTOM]: (msg: Message) => {
                const { payload } = msg;
                const customData = payload.data ? JSON.parse(payload.data) : {};
                const customEvent: string = customData.event ?? '';
                if (customEvent === IMMessageCustomEvent.Mute) {
                    setUserMutedList(prev =>
                        customData.muteStatus ? [...prev, customData.userId] : prev.filter(item => item !== customData.userId)
                    );
                }
            },
        }),
        [setMessageList, tabActiveKey]
    );

    /**
     * 处理接收到的消息
     */
    const onReceivedMessage = useCallback(
        (event: { name: string; data: Message[] }) => {
            const { data } = event;

            data.forEach(msg => {
                const { type } = msg;
                handleMessage[type] && handleMessage[type](msg);
            });
        },
        [handleMessage]
    );

    useEffect(() => {
        if (chatInited && chat?.current) {
            chat.current.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, onReceivedMessage);
        }

        return () => {
            if (chat?.current) {
                chat.current.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, onReceivedMessage);
            }
        };
    }, [chatInited, onReceivedMessage]);

    return {
        messageList,
        unreadMessageCount,
        setUnreadMessageCount,
        setMessageList,
        setUserMutedList,
        userMutedList,
        chatInited,
        chat,
        autoScrollBottom,
        stopRenderIndex,
        scrollToIndex,
        setScrollToIndex,
        isFirstRender,
    };
};
