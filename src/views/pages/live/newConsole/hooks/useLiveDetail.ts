/**
 * @Owners ljh
 * @Title 直播详情
 */
import { type Console } from '@/api/interface/live/console';
import { liveDetailInfo } from '@/api/modules/live/console';
import { useCallback, useEffect, useState } from 'react';

export const useLiveDetail = (liveId?: number) => {
    const [liveDetail, setLiveDetail] = useState<Console.Response.LiveDetailInfo>();

    /**
     * @description: 获取直播详情
     */
    const getLiveDetail = useCallback(async () => {
        if (!liveId) {
            throw new Error('直播id不存在');
        }
        const res = await liveDetailInfo({ liveId: Number(liveId) });
        setLiveDetail(res.data);
        return res.data;
    }, [liveId]);

    useEffect(() => {
        getLiveDetail();
    }, [getLiveDetail]);

    return {
        liveDetail,
        getLiveDetail,
        setLiveDetail,
    };
};
