/**
 * @Owners ljh
 * @Title 聊天
 */
import TencentCloudChat, { type ChatSDK } from '@tencentcloud/chat';
import { useCallback, useEffect, useRef, useState } from 'react';

export const useChat = () => {
    const chat = useRef<ChatSDK | null>(null);
    const [chatInited, setChatInited] = useState(false);
    const [chatReady, setChatReady] = useState(false);

    const onSDKError = useCallback((event: unknown) => {
        console.error('chat.error:', event);
    }, []);

    const onSdkReady = useCallback(() => {
        setChatReady(true);
    }, []);

    useEffect(() => {
        chat.current = TencentCloudChat.create({
            SDKAppID: import.meta.env.VITE_TENCENT_CLOUD_CHAT_APP_ID,
        });
        setChatInited(true);

        // 设置日志级别
        chat.current.setLogLevel(3);
        // 监听 SDK 准备就绪
        chat.current.on(TencentCloudChat.EVENT.SDK_READY, onSdkReady);
        // 监听错误
        chat.current.on(TencentCloudChat.EVENT.ERROR, onSDKError);

        return () => {
            if (chat.current) {
                // 移除监听
                chat.current.off(TencentCloudChat.EVENT.SDK_READY, onSdkReady);
                chat.current.off(TencentCloudChat.EVENT.ERROR, onSDKError);
                chat.current.destroy();
                chat.current = null;
            }
        };
    }, []);

    return {
        chat,
        chatInited,
        chatReady,
    };
};
