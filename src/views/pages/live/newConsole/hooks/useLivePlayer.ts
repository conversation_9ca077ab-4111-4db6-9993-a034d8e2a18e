/**
 * @Owners ljh
 * @Title 直播播放器
 */
import { operateLive } from '@/api/modules/live/console';
import { useCallback, useEffect, useRef, useState } from 'react';
import TCPlayer from 'tcplayer.js';

/** 推流类型 */
export enum PushType {
    /** 暂停推流 */
    PausePush = 1,
    /** 恢复推流 */
    ResumePush = 2,
    /** 结束推流 */
    EndPush = 3,
}

export const useLivePlayer = (containerId: string, liveId?: number) => {
    const playerInstance = useRef<typeof TCPlayer | null>(null);
    /** 视频容器 */
    const videoContainerRef = useRef<HTMLDivElement>(null);
    /** 是否暂停 */
    const [paused, setPaused] = useState(true);
    /** 是否静音 */
    const [isMuted, setIsMuted] = useState(false);
    /** 是否全屏 */
    const [isFullscreen, setIsFullscreen] = useState(false);
    /** 错误信息 */
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const [errorInfo, setErrorInfo] = useState<any>(null);

    /** 全屏事件 */
    const onFullscreen = useCallback((event: Event) => {
        const elem = event.target as HTMLElement;
        const fullscreen = elem === videoContainerRef.current;
        if (fullscreen) {
            setIsFullscreen(prev => !prev);
        }
    }, []);

    useEffect(() => {
        document.addEventListener('fullscreenchange', onFullscreen);

        return () => {
            document.removeEventListener('fullscreenchange', onFullscreen);
        };
    }, [onFullscreen]);

    const onPlay = () => {
        setPaused(false);
    };

    const onPause = () => {
        setPaused(true);
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const onError = (event: any) => {
        const { data } = event;
        if (data?.code === 14) {
            // HTML5 + hls.js 模式下播放 hls 出现网络异常
            setErrorInfo(data);
        }
    };

    /**
     * @description: 清空错误信息
     */
    const clearError = () => {
        setErrorInfo(null);
    };

    /**
     * @description: 销毁播放器
     */
    const destroyTCPlayer = () => {
        if (!playerInstance.current) {
            return;
        }

        console.log('【播放器销毁】');
        playerInstance.current.off('play', onPlay);
        playerInstance.current.off('pause', onPause);
        playerInstance.current.off('error', onError);
        playerInstance.current.dispose();
        playerInstance.current = null;
    };

    /**
     * @description: 创建播放器
     */
    const createTCPlayer = (url: string) => {
        if (playerInstance.current) {
            console.warn('TCPlayer has been initialized');
            return;
        }
        playerInstance.current = TCPlayer(containerId, {
            sources: [
                {
                    src: url,
                },
            ],
            licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1319827559_1/v_cube.license',
            autoplay: true,
            controls: false,
        });

        playerInstance.current.on('play', onPlay);
        playerInstance.current.on('pause', onPause);
        playerInstance.current.on('error', onError);
    };

    useEffect(
        () => () => {
            destroyTCPlayer();
        },
        []
    );

    /**
     * @description: 切换静音
     */
    const toggleMuted = useCallback(() => {
        const nextIsMuted = !isMuted;
        if (!playerInstance.current) return;
        playerInstance.current.muted(nextIsMuted);
        setIsMuted(nextIsMuted);
    }, [isMuted]);

    /**
     * @description: 修改直播推流状态
     */
    const handleChangePushStatus = async (type: PushType) => {
        if (!liveId) {
            throw new Error('直播id不存在');
        }

        const res = await operateLive({
            id: liveId,
            type,
        });

        return res;
    };

    /**
     * @description: 切换全屏
     */
    const togglePlayerFullscreen = useCallback(() => {
        const nextIsFullscreen = !isFullscreen;
        if (nextIsFullscreen) {
            videoContainerRef.current?.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }, [isFullscreen]);

    return {
        videoContainerRef,
        playerInstance,
        createTCPlayer,
        destroyTCPlayer,
        errorInfo,
        clearError,
        paused,
        toggleMuted,
        isFullscreen,
        togglePlayerFullscreen,
        isMuted,
        handleChangePushStatus,
    };
};
