/**
 * @Owners ljh
 * @Title 聊天记录
 */
import { type Console } from '@/api/interface/live/console';
import { getCommentList } from '@/api/modules/live/console';
import TencentCloudChat from '@tencentcloud/chat';
import { useCallback, useRef, useState } from 'react';

const PAGE_SIZE = 40;

export const useChatHistory = (groupId: number) => {
    const [hasMore, setHasMore] = useState(true);
    const [loading, setLoading] = useState(false);
    /** 最后一条消息id */
    const lastMsgId = useRef<string>();

    /**
     * @description: 格式化历史消息
     * @param messages 历史消息列表
     */
    const formatHistoryMessage = useCallback<(messages: Console.Response.GetCommentList) => Console.ChatMessage[]>(
        (messages: Console.Response.GetCommentList) =>
            messages.map(item => ({
                ID: item.msgId,
                // atUserList: [],
                // avatar: item.msgSenderAvatar,
                // clientSequence: item.msgSeq,
                // clientTime: item.gmtCreate,
                cloudCustomData: item.cloudCustomData ? JSON.parse(item.cloudCustomData) : undefined,
                // conversationID: `GROUP${item.groupId}`,
                // conversationSubType: undefined,
                conversationType: TencentCloudChat.TYPES.CONV_GROUP,
                // flow: '',
                from: String(item.msgSenderId),
                // hasRiskContent: false,
                // isBroadcastMessage: false,
                // isDeleted: false,
                // isModified: false,
                // isPeerRead: false,
                // isPlaceMessage: 0,
                // isRead: false,
                // isResend: false,
                // isRevoked: false,
                // isSupportExtension: false,
                // isSystemMessage: false,
                // msgStatus: item.msgStatus,
                // nameCard: '',
                // needReadReceipt: false,
                nick: item.msgSenderNickname,
                payload: item.msgContent
                    ? JSON.parse(item.msgContent)
                    : {
                          text: '',
                      },
                // priority: TencentCloudChat.TYPES.MSG_PRIORITY_NORMAL,
                // protocol: 'JSON',
                // random: Math.random() * 100000,
                // readReceiptInfo: {
                //     readCount: undefined,
                //     unreadCount: undefined,
                //     isPeerRead: undefined,
                //     timestamp: 0,
                // },
                // revokeReason: '',
                // revoker: '',
                // revokerInfo: {
                //     userID: '',
                //     nick: '',
                //     avatar: '',
                // },
                // senderTinyId: '',
                // sequence: item.msgSeq,
                // status: 'success',
                // time: new Date(item.gmtCreate).getTime(),
                to: String(item.groupId),
                type: item.msgType as TencentCloudChat.TYPES,
                // version: 0,
            })),
        []
    );

    const fetchChatHistory = useCallback(async () => {
        if (loading || !hasMore) return undefined;
        try {
            setLoading(true);
            const res = await getCommentList({ groupId, size: PAGE_SIZE, anchorImMsgId: lastMsgId.current });
            const messages = formatHistoryMessage(res.data ?? []);

            if (messages.length) {
                // 记录最后一条消息id
                lastMsgId.current = messages[messages.length - 1].ID;
            }

            setHasMore(messages.length >= PAGE_SIZE);
            setLoading(false);
            return messages.reverse();
        } finally {
            setLoading(false);
        }
    }, [groupId, formatHistoryMessage, loading, hasMore]);

    return {
        loading,
        hasMore,
        fetchChatHistory,
    };
};
