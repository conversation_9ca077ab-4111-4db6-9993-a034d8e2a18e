.console {
    .ant-pro-card {
        box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.1);
    }

    .header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .tool-item {
            margin-left: 10px;
            cursor: pointer;

            .img {
                padding-bottom: 2px;
            }

            .s {
                margin-left: 4px;
            }

            padding: 8px 12px;
            // width: 100px;
            height: 32px;

            background: #ebfaf6;
            border-radius: 6px 6px 6px 6px;
            border: 1px solid rgba(0, 182, 143, 0.7);
            font-family: PingFang SC,
            PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;

            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .flotter {
        display: flex;
        justify-content: space-between;
        gap: 16px;
        padding-bottom: 12px;
        min-height: 682px;

        .live-console-bottom-left-collapse {
            padding: 0 4px !important;
        }

        .live-console-collapse {
            background-color: transparent;

            .live-console-collapse-icon {
                width: 26px;
                height: 26px;
                border-radius: 6px;
                background-color: #f7f8f9;
                display: flex;
                justify-content: center;
                align-items: center;

                &:hover {
                    background-color: #eeeeee;
                }

                img {
                    transition: all 300ms ease-in-out;
                }
            }

            .ant-collapse-content-box {
                border-top: 1px solid #eee;
            }

            .live-console-collapse-interaction {
                .ant-collapse-content-box {
                    padding: 0;
                }
            }

            .ant-collapse-item {
                box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.1);
                border-radius: 6px;

                .ant-collapse-header {
                    padding: 0 24px;
                    border-radius: 6px !important;
                    overflow: hidden;
                }
            }

            .ant-tabs-nav::before {
                display: none !important;
            }
        }

        .live-console-bottom-left {
            flex: 0 0 420px;
            position: relative;
            display: flex;
            max-height: 687px;
            flex-direction: column;
            // background-color: #fff;
            border-radius: 6px;
            overflow: auto;

            .ant-message {
                position: absolute;

                .ant-message-notice-content {
                    color: #f7f8f9;
                    background: #4c4c4c;
                    border-radius: 12px;
                }
            }

            .ant-tabs-nav {
                width: 100%;
                // padding: 0 !important;
            }

            &-collapse {
                font-weight: 600;
                color: #333;
                text-shadow: none;
                font-size: 20px;
                padding: 16px 24px;
            }
        }

        .live-card-tabs {
            .ant-tabs-nav {
                padding: 0;
            }

            .live-card-setting-button {
                padding: 0 5px;
                gap: 2px;
                border-radius: 6px;

                .ant-btn-icon {
                    display: inline-flex;
                }
            }
        }

        .live-console-bottom-left-tabs-tabpane {
            display: flex;
            overflow: hidden;
            max-height: 530px;

            &.live-console-bottom-left-tabs-tabpane-hidden {
                display: none;
            }
        }
    }

    .topActiveCard {

        .ant-pro-card,
        .ant-pro-card-body {
            padding: 0px;
            padding-inline: 0px;
        }

        .topActive {
            padding: 0 24px;

            .ant-tabs-nav::before {
                border-bottom: none;
            }

            .ant-tabs-nav {
                margin: 0;
                padding: 0;
            }
        }
    }

    /* 针对 Tabs 或 Button 组件去除焦点样式 */
    .ant-tabs-tab-btn:focus,
    .ant-btn:focus {
        outline: none !important;
        box-shadow: none !important;
    }

    .ant-tabs-nav {
        margin: 0;
        padding: 0 24px;

        &::before {
            background-color: #eeeeee;
            // border-bottom: none;
        }
    }

    .ant-tabs-tab {
        color: #666;
        font-size: 20px;

        &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                font-weight: 600;
                color: #333;
                text-shadow: none;
            }
        }
    }

    .ant-tabs-ink-bar {
        height: 4px !important;
        border-radius: 4px;
        width: 36px !important;
    }

    .ant-collapse-expand-icon {
        margin: auto 0;
    }

    .ant-collapse-item {
        margin-bottom: 10px;
        background: #fff;
    }
}