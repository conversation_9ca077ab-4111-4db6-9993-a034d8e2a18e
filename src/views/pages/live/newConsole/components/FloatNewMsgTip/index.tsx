/**
 * @Owners ljh
 * @Title 新消息提示
 */
import SvgIcon from '@/views/components/common/SvgIcon';
import classNames from 'classnames';

import './index.scss';

export interface FloatNewMsgTipProps {
    unreadMessageCount?: number;
    onClick?(): void;
}

const FloatNewMsgTip = (props: FloatNewMsgTipProps) => {
    const { onClick, unreadMessageCount = 0 } = props;

    return (
        <div
            className={classNames('float-new-message-tip', { 'float-new-message-tip--hidden': !unreadMessageCount })}
            onClick={onClick}
        >
            <SvgIcon name='double-bottom-outlined' />
            <span>{unreadMessageCount}条新消息</span>
        </div>
    );
};

export default FloatNewMsgTip;
