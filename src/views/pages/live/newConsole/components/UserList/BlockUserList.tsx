/**
 * @Owners PGee
 * @Title 拉黑列表
 */

import { type Console } from '@/api/interface/live/console';
import { getBlockUserList, setUserBlock } from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import useStateWithRef from '@/hooks/useStateWithRef';
import Show from '@/views/components/common/Show';
import { Avatar, Button, Divider, Empty, Input, List, Radio, Spin, Tag, message, type RadioChangeEvent } from 'antd';
import modal from 'antd/es/modal';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

import { LevelBadge, Role } from '../LevelBadge';

import { type ChildComponentRef } from './types';

interface IProps {
    liveId: number;
    total: number;
    isLiveEnd: boolean;
    handleGetNum(): void;
}

type DataType = Console.Response.LiveGroupOnlineUsersRespVO;

const { Search } = Input;

const rows = 20;

// 生成20条数据
// const mockData = Array.from({ length: 20 }, (_, index) => ({
//     id: index + 1,
//     nickname: `测试${index + 1}`,
//     avatar: 'https://img.yzcdn.cn/vant/ipad.png',
//     levelIcon: 'https://img.yzcdn.cn/vant/ipad.png',
// }));

export const BlockUserList = forwardRef<ChildComponentRef, IProps>(({ liveId, handleGetNum, isLiveEnd }, ref) => {
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [data, setData] = useState<DataType[]>([]);
    const [total, setTotal] = useState(0);
    const [searchText, setShearchText] = useState<string>();
    const [type, setType, typeRef] = useStateWithRef(1);
    const scrollableDivone = useRef<HTMLDivElement>(null);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const refresh = () => {
        loadMoreData(undefined, true);
        handleScrollToBottom();
        handleStart();
        if (isLiveEnd) handleEnd();
    };

    const reset = () => {
        loadMoreData(undefined, true);
        refresh();
    };

    // 使用 useImperativeHandle 暴露给父组件的属性和方法
    useImperativeHandle(ref, () => ({
        reset,
    }));

    useEffect(() => {
        loadMoreData(undefined, true);
        handleStart();
        return () => {
            handleEnd();
        };
    }, []);

    useEffect(() => {
        if (isLiveEnd) handleEnd();
    }, [isLiveEnd]);

    const handleStart = () => {
        handleEnd();
        intervalRef.current = setInterval(() => {
            loadMoreData(undefined, true);
        }, 30 * 1000);
    };

    const handleEnd = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null; // 清除引用
        }
    };

    const handleChangeType = (e: RadioChangeEvent) => {
        setType(e.target.value);
        setData([]);
        refresh();
    };

    const loadMoreData = (searchText?: string, reset = false) => {
        if (loading) {
            return;
        }
        setLoading(true);
        const _page = reset ? 1 : page;
        getBlockUserList({
            page: _page,
            rows,
            condition: {
                nickname: searchText,
                queryType: typeRef.current,
                liveId,
            },
        })
            .then(body => {
                let newData = [...data, ...(body.data?.dataList || [])];
                if (searchText || reset) {
                    newData = [...(body.data?.dataList || [])];
                }
                setData(newData);
                setLoading(false);
                const newPage = _page + 1;
                setPage(newPage);
                setTotal(body.data?.total || 0);
                if (newPage > 2) {
                    handleEnd();
                }
            })
            .catch(() => {
                setLoading(false);
            });
    };

    const onSearch = (value: string) => {
        setShearchText(value);
        if (!value) return message.error('请输入用户昵称');
        handleEnd();
        loadMoreData(searchText, true);
    };

    /**
     * @description: 拉黑/解禁用户
     */
    const handleToggleBlockMessage = (item: Console.Response.LiveGroupOnlineUsersRespVO) => {
        const userId = item.id;
        if (!userId) return;

        modal.confirm({
            title: '确定要解除拉黑该用户吗？',
            content: '解除拉黑后该用户将可以进入本场直播间。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                setUserBlock({
                    userId,
                    liveId,
                    operateType: typeRef.current === 1 ? 1 : 3,
                }).then(() => {
                    message.success(`已将${item.nickname}解除拉黑`);
                    handleGetNum();
                    const newData = data.filter(i => i.id !== userId);
                    setData(newData);
                    handleEnd();
                    // setTotal(total - 1);
                });
            },
        });
    };

    /**
     * @description: 滚动到最底部
     */
    const handleScrollToBottom = useCallback(() => {
        if (!scrollableDivone.current) return;
        scrollableDivone.current.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }, []);

    return (
        <div className='containerUserList'>
            <Search
                placeholder='搜索用户昵称'
                loading={loading}
                onSearch={onSearch}
                style={{
                    marginTop: '12px',
                    marginBottom: '12px',
                    padding: '0 24px',
                }}
                value={searchText}
                onChange={e => {
                    setShearchText(e.target.value);
                }}
            />
            <div className='filter-radio-group'>
                <Radio.Group
                    value={type}
                    onChange={handleChangeType}
                    options={[
                        { value: 1, label: '当前直播间拉黑用户' },
                        { value: 2, label: '永久拉黑用户' },
                    ]}
                />
            </div>
            <div id='blockUserListScroller' ref={scrollableDivone} style={{ padding: '0 24px' }}>
                <Show
                    when={data.length > 0}
                    fallback={<Empty style={{ marginTop: '136px' }} image={EmptyImg} description='暂无用户' />}
                >
                    <InfiniteScroll
                        dataLength={data.length}
                        next={loadMoreData}
                        hasMore={(page - 1) * rows < total}
                        loader={
                            loading ? (
                                <div
                                    style={{
                                        textAlign: 'center',
                                    }}
                                >
                                    <Spin size='small' />
                                </div>
                            ) : null
                        }
                        endMessage={
                            <Divider plain style={{ color: '#999999' }}>
                                已经到底啦
                            </Divider>
                        }
                        scrollableTarget={'blockUserListScroller'}
                    >
                        <List
                            dataSource={data}
                            split={false}
                            className={'userList'}
                            renderItem={(item, index) => (
                                <List.Item
                                    key={`${item.id}${index}`}
                                    className={'userListItem'}
                                    style={{
                                        padding: '12px',
                                    }}
                                >
                                    <List.Item.Meta
                                        avatar={<Avatar size={32} src={item.avatar} />}
                                        title={
                                            <div className='title'>
                                                <span className='name'>{item.nickname}</span>
                                                <LevelBadge
                                                    icon={item.levelIcon}
                                                    level={item.level}
                                                    theme={Number(item.role) === Role.Vip ? 'gold' : 'green'}
                                                    className='tag'
                                                />
                                                <Show when={type === 2 && item.onlineStatus === 1}>
                                                    <Tag bordered={false} color='cyan'>
                                                        在线
                                                    </Tag>
                                                </Show>
                                            </div>
                                        }
                                        style={{
                                            alignItems: 'center',
                                            alignContent: 'center',
                                        }}
                                        // description={item.email}
                                    />
                                    <div className={`btnHolder ${type === 2 ? 'btnHolderDanger' : ''}`}>
                                        <Button type='default' size='small' block onClick={() => handleToggleBlockMessage(item)}>
                                            <span className='btnHolderText'>{type === 1 ? '已拉黑' : '永久拉黑'}</span>
                                            <span className='btnHolderTextHover'>解除拉黑</span>
                                        </Button>
                                    </div>
                                </List.Item>
                            )}
                        />
                    </InfiniteScroll>
                </Show>
            </div>
        </div>
    );
});
