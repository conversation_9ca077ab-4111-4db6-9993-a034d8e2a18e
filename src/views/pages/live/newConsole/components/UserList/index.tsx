/**
 * @Owners lzy
 * @Title 用户列表
 */

import { type Console } from '@/api/interface/live/console';
import { getLiveImUserNum, liveGroupUsers, setUserBlock, setUserMuted } from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import refreshIcon from '@/assets/images/refreshIcon.png';
import { IMMessageCustomEvent } from '@/consts/cLive';
import { formatNumber } from '@/utils/uNumber';
import { modal } from '@/views/components/UseAppPrompt';
import Show from '@/views/components/common/Show';
import TencentCloudChat from '@tencentcloud/chat';
import { Avatar, Button, Divider, Empty, Input, List, Segmented, Space, Spin, Tag, message } from 'antd';
import React, { forwardRef, useCallback, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

import { LiveContext } from '../../contexts/LiveContext';
import { LevelBadge, Role } from '../LevelBadge';

import { BlockUserList } from './BlockUserList';
import './index.scss';
import { type ChildComponentRef } from './types';

const { Search } = Input;
type DataType = Console.Response.LiveGroupOnlineUsersRespVO;

interface IProps {
    type: number;
    liveId: number;
    groupId: number;
    total: number;
    isLiveEnd: boolean;
    handleGetNum(): void;
}

const rows = 20;

const ListLeft = forwardRef<ChildComponentRef, IProps>(({ type, liveId, groupId, handleGetNum, isLiveEnd }, ref) => {
    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [data, setData] = useState<DataType[]>([]);
    const [total, setTotal] = useState(0);
    const [searchText, setShearchText] = useState<string>();
    const scrollableDivone = useRef<HTMLDivElement>(null);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);

    const { liveDetail } = useContext(LiveContext);

    // 使用 useImperativeHandle 暴露给父组件的属性和方法
    useImperativeHandle(ref, () => ({
        reset: () => {
            loadMoreData(undefined, true);
            setShearchText('');
            handleScrollToBottom();
            handleStart();
            if (isLiveEnd) handleEnd();
        },
    }));

    useEffect(() => {
        if (!groupId) return () => {};
        loadMoreData(undefined, true);
        handleStart();
        return () => {
            handleEnd();
        };
    }, [groupId]);

    useEffect(() => {
        if (isLiveEnd) handleEnd();
    }, [isLiveEnd]);

    const handleStart = () => {
        handleEnd();
        intervalRef.current = setInterval(() => {
            loadMoreData(undefined, true);
        }, 30 * 1000);
    };

    const handleEnd = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null; // 清除引用
        }
    };

    const loadMoreData = (searchText?: string, reset = false) => {
        if (loading) {
            return;
        }
        setLoading(true);
        const _page = reset ? 1 : page;
        liveGroupUsers({
            page: _page,
            rows,
            condition: {
                nickname: searchText,
                queryType: type,
                groupId,
                liveId,
            },
        })
            .then(body => {
                let newData = [...data, ...(body.data?.dataList || [])];
                if (searchText || reset) {
                    newData = [...(body.data?.dataList || [])];
                    // newData = [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}];
                }
                setData(newData);
                setLoading(false);
                const newPage = _page + 1;
                setPage(newPage);
                setTotal(body.data?.total || 0);
                if (newPage > 2) {
                    handleEnd();
                }
            })
            .catch(() => {
                setLoading(false);
            });
    };

    const onSearch = (value: string) => {
        setShearchText(value);
        if (!value) return message.error('请输入用户昵称');
        handleEnd();
        loadMoreData(searchText, true);
    };

    /**
     * @description: 拉灰/解禁用户
     */
    const handleToggleMuteMessage = (item: Console.Response.LiveGroupOnlineUsersRespVO) => {
        const userId = item.id;
        const groupId = item.groupId;
        const nextBan = type === 1;
        const title = nextBan ? '确定要拉灰该用户吗？' : '确定要对该用户解除拉灰吗？';
        const content = nextBan ? '拉灰后该用户将在本场直播间无法评论。' : '解除拉灰后该用户将在本场直播间中恢复评论。';
        const messageText = nextBan ? '拉灰成功' : '解除拉灰成功';

        modal.confirm({
            title,
            content,
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                setUserMuted({
                    userId,
                    groupId,
                    liveId,
                    muteStatus: nextBan ? 1 : 0,
                }).then(() => {
                    message.success(messageText);
                    handleGetNum();
                    const newData = data.filter(i => i.id !== userId);
                    setData(newData);
                    handleEnd();
                    // setTotal(total - 1);
                });
            },
        });
    };

    /**
     * @description: 拉黑/解禁用户
     */
    const handleToggleBlockMessage = (item: Console.Response.LiveGroupOnlineUsersRespVO) => {
        const userId = item.id;
        if (!userId) return;

        modal.confirm({
            title: '确定要拉黑该用户吗？',
            content: '拉黑后该用户将移出本场直播间。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                setUserBlock({
                    userId,
                    liveId,
                    operateType: 0,
                }).then(() => {
                    message.success(`已将${item.nickname}拉黑并移出本场直播`);
                    handleGetNum();
                    const newData = data.filter(i => i.id !== userId);
                    setData(newData);
                    handleEnd();
                    // setTotal(total - 1);
                });
            },
        });
    };

    /**
     * @description: 滚动到最底部
     */
    const handleScrollToBottom = useCallback(() => {
        if (!scrollableDivone.current) return;
        scrollableDivone.current.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }, []);

    return (
        <div className='containerUserList'>
            <Search
                placeholder='搜索用户昵称'
                loading={loading}
                onSearch={onSearch}
                style={{
                    flexShrink: 0,
                    marginTop: '12px',
                    marginBottom: '12px',
                    padding: '0 24px',
                }}
                value={searchText}
                onChange={e => {
                    setShearchText(e.target.value);
                }}
            />
            <div id={`scrollableDivone${type}`} ref={scrollableDivone} style={{ padding: '0 24px' }}>
                <Show
                    when={data.length > 0}
                    fallback={<Empty style={{ marginTop: '136px' }} image={EmptyImg} description='暂无用户' />}
                >
                    <InfiniteScroll
                        dataLength={data.length}
                        next={loadMoreData}
                        hasMore={(page - 1) * rows < total}
                        loader={
                            loading ? (
                                <div
                                    style={{
                                        textAlign: 'center',
                                    }}
                                >
                                    <Spin size='small' />
                                </div>
                            ) : null
                        }
                        endMessage={
                            <Divider plain style={{ color: '#999999' }}>
                                已经到底啦
                            </Divider>
                        }
                        scrollableTarget={`scrollableDivone${type}`}
                    >
                        <List
                            dataSource={data}
                            split={false}
                            className={'userList'}
                            renderItem={(item, index) => {
                                const isNotLiveUser = item.id !== liveDetail?.imUserId;

                                return (
                                    <List.Item
                                        key={`${item.id}${index}`}
                                        className={'userListItem'}
                                        style={{
                                            padding: '12px',
                                        }}
                                    >
                                        <List.Item.Meta
                                            avatar={<Avatar size={32} src={item.avatar} />}
                                            title={
                                                <div className='title'>
                                                    <span className='name'>{item.nickname}</span>
                                                    <LevelBadge
                                                        icon={item.levelIcon}
                                                        level={item.level}
                                                        theme={Number(item.role) === Role.Vip ? 'gold' : 'green'}
                                                        className='tag'
                                                    />
                                                    <Show when={type === 2 && item.onlineStatus === 1}>
                                                        <Tag bordered={false} color='cyan'>
                                                            在线
                                                        </Tag>
                                                    </Show>
                                                </div>
                                            }
                                            style={{
                                                alignItems: 'center',
                                                alignContent: 'center',
                                            }}
                                            // description={item.email}
                                        />
                                        <Show when={isNotLiveUser && type === 1}>
                                            <Space>
                                                <Button
                                                    className='btn'
                                                    type='default'
                                                    size='small'
                                                    onClick={() => handleToggleMuteMessage(item)}
                                                >
                                                    {type === 1 ? '拉灰' : '解除拉灰'}
                                                </Button>
                                                <Button
                                                    className='btn'
                                                    type='default'
                                                    size='small'
                                                    danger
                                                    onClick={() => handleToggleBlockMessage(item)}
                                                >
                                                    拉黑
                                                </Button>
                                            </Space>
                                        </Show>
                                        <Show when={isNotLiveUser && type === 2}>
                                            <div className='btnHolder'>
                                                <Button
                                                    type='default'
                                                    size='small'
                                                    block
                                                    onClick={() => handleToggleMuteMessage(item)}
                                                >
                                                    <span className='btnHolderText'>已拉灰</span>
                                                    <span className='btnHolderTextHover'>解除拉灰</span>
                                                </Button>
                                            </div>
                                        </Show>
                                    </List.Item>
                                );
                            }}
                        />
                    </InfiniteScroll>
                </Show>
            </div>
        </div>
    );
});

type AppProps = {
    groupId: number;
    liveId: number;
    isLiveEnd: boolean;
};

const App: React.FC<AppProps> = ({ liveId, groupId, isLiveEnd }) => {
    const childRef1 = useRef<ChildComponentRef>(null); // 获取子组件的引用
    const childRef2 = useRef<ChildComponentRef>(null); // 获取子组件的引用
    const childRef3 = useRef<ChildComponentRef>(null); // 获取子组件的引用
    const [activeKey, setActiveKey] = useState('1');
    const [userNum, setUserNum] = useState<Console.Response.LiveImUserNumRespDTO>();
    const { chatInited, chat } = useContext(LiveContext);

    useEffect(() => {
        if (groupId) handleGetNum();
    }, [groupId]);

    /**
     * 处理接收到的消息
     */
    const handleReceivedMessage = useCallback((event: { name: string; data: Console.ChatMessage[] }) => {
        const { data } = event;

        data.forEach(msg => {
            const { type, payload } = msg;
            // 只处理自定义事件
            if (type !== TencentCloudChat.TYPES.MSG_CUSTOM) return;

            const customData = payload.data ? JSON.parse(payload.data) : {};
            const customEvent: string = customData.event ?? '';

            if (customEvent === IMMessageCustomEvent.REFRESH_LIVE_USER_NUM) {
                const { onlineNum, muteNum, currentBlockNum } = customData;
                setUserNum({
                    onlineNum,
                    muteNum,
                    currentBlockNum,
                });
            }
        });
    }, []);

    useEffect(() => {
        if (chatInited && chat?.current) {
            chat.current.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
        }

        return () => {
            if (chat?.current) {
                chat.current.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
            }
        };
    }, [chatInited, handleReceivedMessage]);

    const handleGetNum = () => {
        getLiveImUserNum({
            groupId,
            liveId,
        }).then(res => {
            setUserNum(res.data);
        });
    };

    const onChange = (key: string) => {
        setActiveKey(key);
    };

    const handleReloadOutlined = () => {
        childRef1.current?.reset();
        childRef2.current?.reset();
        childRef3.current?.reset();
        handleGetNum();
    };

    const segmentedOptions = useMemo(
        () => [
            {
                value: '1',
                label: `在线(${formatNumber(userNum?.onlineNum || 0)})`,
            },
            {
                value: '2',
                label: `拉灰(${formatNumber(userNum?.muteNum || 0)})`,
            },
            {
                value: '3',
                label: '拉黑',
            },
        ],
        [userNum]
    );

    return (
        <div className='userListContainer'>
            <div className='listContainerHeader'>
                <div className='listContainerHeaderSegmented'>
                    <Segmented options={segmentedOptions} onChange={onChange} block />
                </div>
                <div className='listContainHeaderExtra'>
                    <div className='listContainHeaderExtraBtn' onClick={handleReloadOutlined}>
                        <img className='listContainHeaderExtraBtnImg' src={refreshIcon} alt='' />
                    </div>
                </div>
            </div>
            <div className='listContainerBody'>
                <Show when={activeKey === '1'}>
                    <ListLeft
                        handleGetNum={handleGetNum}
                        total={userNum?.onlineNum || 0}
                        groupId={groupId}
                        liveId={liveId}
                        type={1}
                        ref={childRef1}
                        isLiveEnd={isLiveEnd}
                    />
                </Show>
                <Show when={activeKey === '2'}>
                    <ListLeft
                        handleGetNum={handleGetNum}
                        total={userNum?.muteNum || 0}
                        groupId={groupId}
                        liveId={liveId}
                        type={2}
                        ref={childRef2}
                        isLiveEnd={isLiveEnd}
                    />
                </Show>
                <Show when={activeKey === '3'}>
                    <BlockUserList
                        handleGetNum={handleGetNum}
                        total={userNum?.currentBlockNum || 0}
                        liveId={liveId}
                        ref={childRef3}
                        isLiveEnd={isLiveEnd}
                    />
                </Show>
            </div>
        </div>
    );
};

export default App;
