.containerUserList {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 530px;
    max-height: 530px;

    // height: 100%;
    // background-color: red;
    #scrollableDivone1,
    #scrollableDivone2,
    #blockUserListScroller {
        // height: 500px;
        flex-grow: 1;
        overflow-y: scroll;
        // padding-right: 12px;
        // flex: 1;
        // flex-grow: 1;
        // display: flex;
    }

    .filter-radio-group {
        flex-shrink: 0;
        padding: 0 24px;
        box-sizing: border-box;
    }
}

.userListContainer {
    // flex: 0 0 340px;
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #fff;
    border-radius: 6px;
    // padding: 24px;
    // padding-right: 0px;
    // padding-top: 0px;
    // height: 100%;

    .ant-tabs-tab {
        font-size: 16px !important;
    }

    .ant-tabs-nav {
        padding: 0 24px;

        &::before {
            border-bottom: none;
            // display: none;
        }
    }

    .ant-tabs-ink-bar {
        display: none;
    }

    .listContainerHeader {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 24px 0;
        gap: 19px;
        box-sizing: border-box;

        .listContainerHeaderSegmented {
            flex: 1;

            .ant-segmented {
                background-color: #f5f5f5;
            }
        }

        .listContainHeaderExtra {
            flex-shrink: 0;
            padding: 1px 0;
            height: 30px;

            .listContainHeaderExtraBtn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 30px;
                height: 30px;
                cursor: pointer;
                border-radius: 6px;
                background: #F7F8F9;
                color: #333333;
                font-size: 12px;

                .listContainHeaderExtraBtnImg {
                    width: 16px;
                    height: 16px;
                }
            }
        }
    }

    .listContainerBody {
        flex: 1;
    }

    .userList {
        // height: 400px;
        box-sizing: border-box;
        // margin-right: 24px;
        // padding-right: 24px;
    }

    .userListItem {
        padding: 12px;

        .btn {
            display: none;
        }

        .btnHolder {
            width: 64px;

            .ant-btn-variant-outlined {
                border-color: #eee;
            }

            .btnHolderText {
                display: block;
                color: #999999;
            }

            .btnHolderTextHover {
                display: none;
            }

            &.btnHolderDanger {
                .ant-btn-variant-outlined {
                    border-color: #FFCCC7;
                }

                .btnHolderText {
                    color: #FF4D4F;
                }
            }
        }

        .ant-btn-variant-link {
            color: #666666;
        }

        .ant-list-item-meta-avatar {
            margin-inline-end: 8px !important;
        }

        .ant-list-item-meta-title {
            margin-bottom: 0px !important;
        }

        .tag {
            margin: 0 4px;
        }

        .title {
            // display: flex;
            // align-items: center;

            .name {
                // word-break: break-all;
                // text-overflow: ellipsis;
                // display: -webkit-box;
                // -webkit-box-orient: vertical;
                // -webkit-line-clamp: 2;
                // /* 这里是超出几行省略 */
                // overflow: hidden;

                vertical-align: bottom;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 18px;
            }

            .tag {
                vertical-align: middle;
            }
        }

        .img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
    }

    .userListItem:hover {
        /* 中性色/背景/bg02 */
        background: #f7f8f9;
        border-radius: 6px;

        .title {
            max-width: 184px;
        }

        .btn {
            display: inline-block;
        }

        .btnHolder {
            .btnHolderText {
                display: none;
            }

            .btnHolderTextHover {
                display: block;
            }

            &.btnHolderDanger {
                .ant-btn-variant-outlined {
                    border-color: #eee;
                }

                .btnHolderText {
                    color: #999999;
                }
            }
        }

        .img {
            display: none;
        }
    }
}