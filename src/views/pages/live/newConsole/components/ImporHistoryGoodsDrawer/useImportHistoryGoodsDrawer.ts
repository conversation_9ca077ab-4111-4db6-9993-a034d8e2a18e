/**
 * @Owners ljh
 * @Title 导入往期商品
 */
import { type Console } from '@/api/interface/live/console';
import { batchImportLiveGoods, findLiveGoodsByPage, getLiveHistoryRecordPage } from '@/api/modules/live/console';
import { message, type TableProps } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useMemo, useRef, useState } from 'react';

import { type ImporHistoryGoodsDrawerProps } from './index';

type TableRowSelection<T extends {} = {}> = TableProps<T>['rowSelection'];
type LiveGoodsRecord = Console.Response.FindLiveGoodsByPage['dataList'][number];

export const useImportHistoryGoodsDrawer = (props: ImporHistoryGoodsDrawerProps) => {
    const { liveId, onImport } = props;
    const [visible, setVisible] = useState(false);
    /** 直播历史 */
    const [liveHistoryRecordList, setLiveHistoryRecordList] = useState<Console.Response.LiveHistoryRecord[]>([]);
    /** 直播间商品 */
    const [liveGoodsMap, setLiveGoodsMap] = useState<Record<number, Console.Response.FindLiveGoodsByPage['dataList']>>({});
    const [selectedLiveGoodsMap, setSelectedLiveGoodsMap] = useState<Record<number, number[] | undefined>>({});
    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [importLoading, setImportLoading] = useState(false);
    /** 日期时间段 */
    const liveDateRange = useRef({
        start: '',
        end: '',
    });

    /**
     * 获取直播历史记录
     */
    const fetchLiveHistoryRecord = async (payload: Console.Params.GetLiveHistoryRecordPage) => {
        const res = await getLiveHistoryRecordPage(payload);
        setLiveHistoryRecordList(res.data?.dataList || []);
        setTotal(res.data?.total || 0);
    };

    /**
     * 获取直播间商品
     */
    const fetchLiveGoodsByPage = async (payload: Console.Params.FindLiveGoodsByPage) => {
        const res = await findLiveGoodsByPage(payload);
        setLiveGoodsMap(prev => ({
            ...prev,
            [payload.condition.liveId]: res.data?.dataList || [],
        }));
        if (
            Object.prototype.hasOwnProperty.call(selectedLiveGoodsMap, payload.condition.liveId) &&
            !selectedLiveGoodsMap[payload.condition.liveId]
        ) {
            // 表示select存在这个key，并且是undefined标识全选
            setSelectedLiveGoodsMap(prev => ({
                ...prev,
                [payload.condition.liveId]: res.data?.dataList.map(item => item.spuId),
            }));
        }
    };

    /**
     * 重置数据
     */
    const resetData = () => {
        setLiveHistoryRecordList([]);
        setLiveGoodsMap({});
        setSelectedLiveGoodsMap({});
        setTotal(0);
        setPage(1);
        setPageSize(10);
    };

    /**
     * 打开
     */
    const handleOpen = () => {
        const now = dayjs();
        setVisible(true);
        liveDateRange.current = {
            start: now.subtract(6, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            end: now.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
        };
        fetchLiveHistoryRecord({
            condition: {
                liveStartTimeLeft: liveDateRange.current.start,
                liveStartTimeRight: liveDateRange.current.end,
            },
            page,
            rows: pageSize,
        });
    };

    /**
     * 关闭
     */
    const handleClose = () => {
        setVisible(false);
    };

    /**
     * 关闭弹窗重置数据
     */
    const handleAfterOpenChange = (open: boolean) => {
        if (!open) {
            resetData();
        }
    };

    /**
     * 分页切换
     */
    const handleTableChange: TableProps<Console.Response.LiveHistoryRecord>['onChange'] = pagination => {
        const { current, pageSize } = pagination;
        if (!current || !pageSize) return;
        setPage(current);
        setPageSize(pageSize);
        fetchLiveHistoryRecord({
            condition: {
                liveStartTimeLeft: liveDateRange.current.start,
                liveStartTimeRight: liveDateRange.current.end,
            },
            page: current,
            rows: pageSize,
        });
    };

    /**
     * 展开子表格
     */
    const handleTableExpand = (expanded: boolean, record: Console.Response.LiveHistoryRecord) => {
        if (!expanded) return;
        if (!!liveGoodsMap[record.liveId]) return;
        fetchLiveGoodsByPage({
            condition: {
                liveId: record.liveId,
                type: 1,
            },
            page: 1,
            rows: 9999,
        });
    };

    const rowSelection = useMemo<TableRowSelection<Console.Response.LiveHistoryRecord>>(
        () => ({
            // remark selectedLiveGoodsMap 存在这个 liveId，且为 undefined 或者长度不为0的数组才被认为选中
            selectedRowKeys: Object.keys(selectedLiveGoodsMap)
                .filter(
                    key =>
                        (key in selectedLiveGoodsMap && !selectedLiveGoodsMap[Number(key)]) ||
                        !!selectedLiveGoodsMap[Number(key)]?.length
                )
                .map(Number),
            onSelect: (record, selected) => {
                // 因为可能勾选的时候，没有请求商品数据，因此使用undefined 标识全选
                setSelectedLiveGoodsMap(prev => ({
                    ...prev,
                    [record.liveId]: selected
                        ? !!liveGoodsMap[record.liveId]
                            ? liveGoodsMap[record.liveId].map(item => item.spuId)
                            : undefined
                        : [],
                }));
            },
            hideSelectAll: true,
            getCheckboxProps: record => ({
                // 半选状态
                indeterminate:
                    !!selectedLiveGoodsMap[record.liveId]?.length &&
                    record.goodsCount !== selectedLiveGoodsMap[record.liveId]?.length,
            }),
        }),
        [selectedLiveGoodsMap, liveGoodsMap]
    );

    /**
     * 获取子表格的rowSelection
     */
    const getChildTableRowSelection = useCallback(
        (parentLiveId: number): TableRowSelection<LiveGoodsRecord> => ({
            selectedRowKeys: selectedLiveGoodsMap[parentLiveId] || [],
            onSelect: (record, selected) => {
                setSelectedLiveGoodsMap(prev => {
                    let selectedRowKeys = [...(prev[record.liveId] ?? [])];
                    if (selected) {
                        // 选中
                        if (!selectedRowKeys.includes(record.spuId)) {
                            selectedRowKeys.push(record.spuId);
                        }
                    } else {
                        // 取消选中
                        selectedRowKeys = selectedRowKeys.filter(item => item !== record.spuId);
                    }

                    return {
                        ...prev,
                        [record.liveId]: selectedRowKeys,
                    };
                });
            },
            hideSelectAll: true,
        }),
        [selectedLiveGoodsMap]
    );

    /**
     * 导入商品
     */
    const handleImportGoods = useCallback(
        async (upType: number) => {
            if (importLoading) return;
            setImportLoading(true);
            try {
                await batchImportLiveGoods({
                    importLiveGoods: Object.keys(selectedLiveGoodsMap).map(key => ({
                        liveId: Number(key),
                        spuIds: selectedLiveGoodsMap[Number(key)],
                    })),
                    liveId,
                    upType,
                });
                message.success('导入成功');
                onImport?.();
                handleClose();
            } finally {
                setImportLoading(false);
            }
        },
        [selectedLiveGoodsMap, liveId, importLoading, handleClose, onImport]
    );

    return {
        visible,
        importLoading,
        fetchLiveHistoryRecord,
        liveHistoryRecordList,
        total,
        page,
        pageSize,
        liveGoodsMap,
        handleOpen,
        handleClose,
        handleTableChange,
        handleTableExpand,
        handleAfterOpenChange,
        handleImportGoods,
        rowSelection,
        getChildTableRowSelection,
    };
};
