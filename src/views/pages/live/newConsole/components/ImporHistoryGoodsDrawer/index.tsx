/**
 * @Owners ljh
 * @Title 导入往期商品
 */
import { type Console } from '@/api/interface/live/console';
import { formatDuration } from '@/utils/uNumber';
import { DownOutlined } from '@ant-design/icons';
import { Button, Drawer, Flex, Image, Table, type TableColumnsType } from 'antd';
import { forwardRef, useImperativeHandle, useMemo } from 'react';

import './index.scss';
import { useImportHistoryGoodsDrawer } from './useImportHistoryGoodsDrawer';

type LiveGoodsRecord = Console.Response.FindLiveGoodsByPage['dataList'][number];

export interface ImporHistoryGoodsDrawerProps {
    liveId: number;
    /** 导入后回调 */
    onImport?(): void;
}

export interface ImporHistoryGoodsDrawerRef {
    open(): void;
}

const ImporHistoryGoodsDrawer = forwardRef<ImporHistoryGoodsDrawerRef, ImporHistoryGoodsDrawerProps>((props, ref) => {
    const {
        importLoading,
        visible,
        liveHistoryRecordList,
        page,
        pageSize,
        total,
        liveGoodsMap,
        rowSelection,
        getChildTableRowSelection,
        handleOpen,
        handleClose,
        handleTableChange,
        handleTableExpand,
        handleAfterOpenChange,
        handleImportGoods,
    } = useImportHistoryGoodsDrawer(props);

    useImperativeHandle(ref, () => ({
        open() {
            handleOpen();
        },
    }));

    /**
     * 直播间历史记录table
     */
    const columns: TableColumnsType<Console.Response.LiveHistoryRecord> = useMemo(
        () => [
            { title: '开播时间', dataIndex: 'actualStartTime' },
            {
                title: '直播信息',
                render: (_, record) => (
                    <Flex gap={16} align='center'>
                        <Image className='ihgd-live-cover' width={72} height={53} src={record.coverUrl} />
                        <div className='ihgd-live-info-right'>
                            <div className='ihgd-live-name'>{record.liveName}</div>
                            <div className='ihgd-live-desc'>直播时长：{formatDuration(record.liveDuration / 1000 || 0)}</div>
                        </div>
                    </Flex>
                ),
            },
            {
                title: '橱窗商品',
                key: 'goodsCount',
                render: (text, record) => (
                    <>
                        <div>共{record?.goodsCount ?? 0}件商品</div>
                        <Button type='link' size='small' iconPosition='end' icon={<DownOutlined />}>
                            展开详情
                        </Button>
                    </>
                ),
            },
        ],
        []
    );

    /**
     * 直播间商品记录子表格
     */
    const expandColumns: TableColumnsType<LiveGoodsRecord> = [
        { title: '位置序号', dataIndex: 'date', width: 80 },
        {
            title: '商品名称',
            render: (_, record) => (
                <Flex gap={4}>
                    <Image className='ihgd-goods-cover' width={56} height={56} src={record.mainUrl || ''} />
                    <div className='ihgd-goods-info'>
                        <div className='ihgd-goods-name'>短标题：{record.customSpuName}</div>
                        <div className='ihgd-goods-desc'>{record.spuName}</div>
                        <div className='ihgd-goods-tags'>{record.sellingPoint}</div>
                    </div>
                </Flex>
            ),
        },
        { title: '商品类型', dataIndex: 'twoFrontCategoryName', width: 100 },
    ];

    const renderFooter = useMemo(
        () => (
            <Flex justify='end' gap={8}>
                <Button onClick={handleClose}>取消</Button>
                <Button type='primary' disabled={importLoading} ghost onClick={() => handleImportGoods(1)}>
                    添加到待上架
                </Button>
                <Button type='primary' disabled={importLoading} onClick={() => handleImportGoods(0)}>
                    上架到直播间
                </Button>
            </Flex>
        ),
        [handleImportGoods]
    );

    const expandedRowRender = (record: Console.Response.LiveHistoryRecord) => (
        <Table<LiveGoodsRecord>
            columns={expandColumns}
            dataSource={liveGoodsMap[record.liveId]}
            rowKey='spuId'
            pagination={false}
            rowSelection={getChildTableRowSelection(record.liveId)}
        />
    );

    return (
        <Drawer
            className='impor-history-goods-drawer'
            title='近七天直播商品'
            open={visible}
            width={900}
            footer={renderFooter}
            onClose={handleClose}
            afterOpenChange={handleAfterOpenChange}
            destroyOnClose
        >
            <Table<Console.Response.LiveHistoryRecord>
                columns={columns}
                rowKey='liveId'
                dataSource={liveHistoryRecordList}
                pagination={{
                    current: page,
                    pageSize,
                    total,
                }}
                expandable={{ expandedRowRender, onExpand: handleTableExpand }}
                rowSelection={rowSelection}
                onChange={handleTableChange}
            />
        </Drawer>
    );
});

export default ImporHistoryGoodsDrawer;
