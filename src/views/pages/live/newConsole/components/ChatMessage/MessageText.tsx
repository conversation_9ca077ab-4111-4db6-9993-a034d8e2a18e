/**
 * @Owners ljh
 * @Title 消息文本
 */
import { type Console } from '@/api/interface/live/console';
import { cEmoticon } from '@/consts/cEmoticon';
import { type ReactNode, useCallback, useMemo, useRef } from 'react';

export interface MessageTextProps {
    /** 消息体 */
    data?: Console.ChatMessage;
    /** 测量 */
    measure?(): void;
}

const MessageText = (props: MessageTextProps) => {
    const { data, measure } = props;
    const imgCount = useRef(0);

    const checkIsLoaded = useCallback(() => {
        if (imgCount.current === 0) {
            setTimeout(() => {
                measure?.();
            }, 0);
        }
    }, [measure]);

    const handleImgLoad = useCallback(() => {
        imgCount.current--;
        checkIsLoaded();
    }, []);

    const transformContent = (content: string, key: number | string): ReactNode => {
        if (cEmoticon[content]) {
            return (
                <img
                    src={cEmoticon[content]}
                    className='chat-message-emoticon'
                    draggable={false}
                    onLoad={handleImgLoad}
                    key={key}
                />
            );
        }
        return (
            <span className='chat-message-text' key={key}>
                {content}
            </span>
        );
    };

    const parseContent = (content: string, atUserList?: Console.AtUser[]) => {
        // 创建匹配表情和文本的正则
        const regex = /(\[[^\]]+\])|([^\[]+|\[(?![^\]]*\])[^\[]*)/g;
        const matches = content.match(regex) || [];

        let startIndex = 0;
        let currentAtUserIndex = 0;
        return matches.reduce<ReactNode[]>((acc, item, index) => {
            const endIndex = startIndex + item.length;

            const currentAtUser =
                atUserList && atUserList.length > 0 && currentAtUserIndex < atUserList.length
                    ? atUserList[currentAtUserIndex]
                    : null;
            if (currentAtUser && currentAtUser.startIndex >= startIndex && currentAtUser.startIndex < endIndex) {
                // 处理插入@用户
                const sliceIndex = currentAtUser.startIndex - startIndex;
                const [beforeText, afterText] = [item.slice(0, sliceIndex), item.slice(sliceIndex)];
                if (beforeText) {
                    acc.push(transformContent(beforeText, `${index}-0`));
                }
                acc.push(
                    <span className='chat-message-text chat-message-at-user' key={index}>
                        @{currentAtUser.nickname}{' '}
                    </span>
                );
                if (afterText) {
                    acc.push(transformContent(afterText, `${index}-1`));
                }
                // 更新当前@用户索引
                currentAtUserIndex++;
            } else if (cEmoticon[item]) {
                // 是表情
                imgCount.current++;
                acc.push(transformContent(item, index));
            } else {
                // 是文本
                acc.push(transformContent(item, index));
            }

            startIndex = endIndex;
            return acc;
        }, []);
    };

    const text = useMemo(() => {
        if (!data) {
            checkIsLoaded();
            return '';
        }

        const msgList = parseContent(data.payload.text, data.cloudCustomData?.atUserList as Console.AtUser[] | undefined);
        return msgList;
    }, [data]);

    return <span className='chat-messgae-text'>{text}</span>;
};

export default MessageText;
