/**
 * @Owners ljh
 * @Title 聊天记录项
 */
import { type Console } from '@/api/interface/live/console';
import { IMMessageStatus } from '@/consts/cLive';
import Show from '@/views/components/common/Show';
import { Tag } from 'antd';
import { type CSSProperties } from 'react';

import { LevelBadge, Role } from '../LevelBadge';

import MessageText from './MessageText';
import './index.scss';

export interface ChatMessageProps {
    style?: CSSProperties;
    /** IM消息体 */
    message: Console.ChatMessage;
    /** 是否禁言 */
    isMuted?: boolean;
    /** 测量 */
    measure?(): void;
}

const ChatMessage = (props: ChatMessageProps) => {
    const { message, isMuted = false, measure } = props;

    return (
        <div className='chat-message'>
            <Show when={message.msgStatus === IMMessageStatus.Hide}>
                <Tag className='chat-message-tag chat-message-tag-comment' bordered={false}>
                    隐藏评论
                </Tag>
            </Show>
            <Show when={isMuted}>
                <Tag className='chat-message-tag' color='error' bordered={false}>
                    拉灰
                </Tag>
            </Show>
            {/* <Tag className='chat-user-tag chat-user-tag-streamer' bordered={false}>
                粉丝
            </Tag> */}
            <LevelBadge
                className='chat-user-level-badge'
                level={message?.cloudCustomData?.level as number}
                icon={message?.cloudCustomData?.levelIcon as string}
                theme={message?.cloudCustomData?.role === Role.Vip ? 'gold' : 'green'}
            />
            <span className='chat-user-name'>{message.nick}：</span>
            <MessageText measure={measure} data={message} />
        </div>
    );
};

export default ChatMessage;
