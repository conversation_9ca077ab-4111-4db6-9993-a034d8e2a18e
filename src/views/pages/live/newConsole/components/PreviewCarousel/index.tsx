/**
 * @Owners ljh
 * @Title 预览轮播
 */
import SvgIcon from '@/views/components/common/SvgIcon';
import { Button, Carousel } from 'antd';
import type { CarouselRef } from 'antd/es/carousel';
import { useCallback, useMemo, useRef, useState } from 'react';

import './index.scss';

export interface PreviewCarouselItem {
    /** 图片地址 */
    image?: string;
    /** 标题 */
    title?: string;
}

export interface PreviewCarouselProps {
    /** 标题 */
    title?: string;
    /** 是否自动播放 */
    autoplay?: boolean;
    /** 自动播放间隔 */
    autoplaySpeed?: number;
    /** 轮播图列表 */
    dataSource?: PreviewCarouselItem[];
}

const PreviewCarousel = (props: PreviewCarouselProps) => {
    const { autoplay = true, autoplaySpeed = 5000, dataSource = [] } = props;
    const [currentIndex, setCurrentIndex] = useState(0);
    const carouselRef = useRef<CarouselRef>(null);

    const itemTitle = useMemo(() => dataSource[currentIndex]?.title, [currentIndex, dataSource]);

    const handlePrev = useCallback(() => {
        carouselRef.current?.prev();
    }, []);

    const handleNext = useCallback(() => {
        carouselRef.current?.next();
    }, []);

    const handleAfterChange = useCallback((current: number) => {
        setCurrentIndex(current);
    }, []);

    return (
        <div className='preview-carousel'>
            {/* {!!title && <div className='preview-carousel-title'>{title}</div>} */}
            <div className='preview-carousel-body'>
                <div className='preview-carousel-content'>
                    <Carousel
                        className='preview-carousel'
                        autoplay={autoplay}
                        autoplaySpeed={autoplaySpeed}
                        dots={false}
                        afterChange={handleAfterChange}
                        ref={carouselRef}
                    >
                        {dataSource.map((item, index) => (
                            <img key={index} className='preview-carousel-image' src={item.image} alt={item.title ?? ''} />
                        ))}
                    </Carousel>
                </div>
                <div className='preview-carousel-footer'>
                    <Button
                        className='preview-carousel-indicator'
                        size='small'
                        color='primary'
                        variant='link'
                        icon={<SvgIcon name='left-circle-outlined' />}
                        onClick={handlePrev}
                    />
                    <div className='preview-carousel-text'>{itemTitle}</div>
                    <Button
                        className='preview-carousel-indicator'
                        size='small'
                        color='primary'
                        variant='link'
                        icon={<SvgIcon name='right-circle-outlined' />}
                        onClick={handleNext}
                    />
                </div>
            </div>
        </div>
    );
};

export default PreviewCarousel;
