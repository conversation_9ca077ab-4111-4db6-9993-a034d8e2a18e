.preview-carousel {
    box-sizing: border-box;

    .preview-carousel-title {
        margin-bottom: 16px;
        font-size: 14px;
        font-weight: 600;
        color: #202d40;
    }

    .preview-carousel-body {
        padding: 12px;
        background: #f7f8fa;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
    }

    .preview-carousel-content {
        overflow: hidden;
        width: 100%;
        aspect-ratio: 300 / 546;
        border-radius: 8px;
    }

    .preview-carousel {
        // width: 100%;
        // height: 100%;
        width: 380px;
        height: 546px;
    }

    .preview-carousel-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .preview-carousel-footer {
        margin-top: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0 13px;
    }

    .preview-carousel-text {
        min-width: 126px;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
        color: #5c5c5c;
    }

    .preview-carousel-indicator {
        flex-shrink: 0;

        .ant-btn-icon {
            display: inline-flex;
        }
    }
}
