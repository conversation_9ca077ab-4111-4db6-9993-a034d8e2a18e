/**
 * @Owners lzy
 * @Title AI诊断
 */
import { type Console } from '@/api/interface/live/console';
import { liveAnalysisQuery } from '@/api/modules/live/console';
import analysisIcon from '@/assets/images/analysisIcon.png';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import dsIcon from '@/assets/images/icon-ds-5.png';
import { PushStatusMap } from '@/consts/cLive';
import CommentSummaryLine from '@/views/components/common/CommentSummaryLine';
import { Empty } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import WordCloud, { type ListEntry } from 'wordcloud';

import styles from './index.module.scss';

type Props = {
    liveId: number;
    liveDetail?: Console.Response.LiveDetailInfo;
};

type Item = {
    lable: string;
    color: string;
    background: string;
};
const levelColor: { [key: number]: Item | undefined } = {
    /**
     * 无
     */
    0: undefined,
    /**
     * 低
     */
    1: {
        lable: '低',
        color: '#00B68F',
        background: '#EBFAF6',
    },
    /**
     * 中
     */
    2: {
        lable: '中',
        color: '#FF8F16',
        background: '#FFF7E6',
    },
    /**
     * 高
     */
    3: {
        lable: '高',
        color: '#FA2B19',
        background: '#FFF1F0',
    },
};
const getLevelColor = () => levelColor[Math.floor(Math.random() * 3) + 1 || 1] as Item;

const tagColor = ['#FFF1F0', '#EBFAF6', '#FFF7E6', '#E6F4FF'];
// const getTagColor = () => tagColor[Math.floor(Math.random() * tagColor.length)];

const getEmptyText = (pushStatus?: number) =>
    pushStatus === PushStatusMap.Pushing
        ? '直播进行中，直播结束后为你生产智能，敬请期待！'
        : '智能总结正在努力加载，稍等片刻哟～';
const Sidebar = ({ data, pushStatus }: { data?: Console.Response.LiveAnalysisRespDTO; pushStatus?: number }) => (
    <div className={styles.sidebar}>
        <div className={styles.title}>
            <img src={analysisIcon} className={styles.img} /> 360总结分析
        </div>
        <div className={styles.main}>
            {data?.analysis?.length ? (
                data.analysis.map(item => (
                    <div className={styles.section} key={item.title}>
                        <div className={styles.sectionTitle}>
                            {levelColor[item.level] ? (
                                <span
                                    className={styles.s}
                                    style={{
                                        background: levelColor[item.level]?.background,
                                        color: levelColor[item.level]?.color,
                                    }}
                                >
                                    {levelColor[item.level]?.lable}
                                </span>
                            ) : null}
                            {item.title}
                        </div>
                        <div className={styles.sectionContent}>
                            <span className={styles.lable}>原因分析：</span>
                            <div className={styles.content}>{item.reason}</div>
                        </div>
                        <div className={styles.sectionContent}>
                            <span className={styles.lable}>解决方案：</span>
                            <div className={styles.content}>{item.solution}</div>
                        </div>
                        {item?.commentList?.length ? <CommentSummaryLine comments={item.commentList || []} /> : null}
                    </div>
                ))
            ) : (
                <Empty image={EmptyImg} style={{ margin: '0px auto 24px' }} description={getEmptyText(pushStatus)} />
            )}
        </div>

        <img className={styles.icon} src={dsIcon} alt='' />
    </div>
);

const Sidebar2 = ({ data }: { data?: Console.Response.LiveAnalysisRespDTO }) => (
    <div className={` ${styles.sidebar2}`}>
        <div className={styles.title}>改进建议</div>
        {data?.suggestions?.length ? (
            <div className={styles.section}>
                {data.suggestions.map(item => {
                    const { color, background } = getLevelColor();
                    return (
                        <div key={item.tag} className={styles.sectionContent}>
                            <span
                                className={styles.lable}
                                style={{
                                    background,
                                    color,
                                }}
                            >
                                {item.tag}
                            </span>
                            <span className={styles.t}>{item.content}</span>
                        </div>
                    );
                })}
            </div>
        ) : (
            <Empty image={EmptyImg} style={{ margin: '0px auto', paddingBottom: '20px' }} description='暂无建议' />
        )}

        <img className={styles.icon} src={dsIcon} alt='' />
    </div>
);

function Tags({ data }: { data?: Console.Response.LiveAnalysisRespDTO }) {
    // const words = [
    //     '护肤品',
    //     '浪漫主义',
    //     '清新自然',
    //     '活动赠品',
    //     '运动/活力',
    //     '检测报告',
    //     '年龄段均可',
    //     '时尚前卫',
    //     '2号链接',
    //     '产品用法',
    // ];

    const words = data?.words || [];

    const list: ListEntry[] = _(words)
        .chunk(Math.ceil(words.length / 4))
        .flatMap((chunk, index) => chunk.map(item => [item, 20 - index * 3, index] as ListEntry))
        .value();
    useEffect(() => {
        const el = document.querySelector<HTMLDivElement>('#word-cloud');
        if (!el) {
            return () => {};
        }

        WordCloud(el, {
            list,
            gridSize: 30, // 网格大小（数值越小，词云越紧凑）
            weightFactor: 2, // 字体大小因子
            fontFamily: 'PingFang SC',
            backgroundColor: '#fff', // 背景颜色
            rotateRatio: 0, // 文字不旋转
            drawOutOfBound: false, // 禁止绘制超出边界的文字
            classes: () => styles['word-item'],
        });

        // 观察器的配置（需要观察什么变动）
        const config = { attributes: false, childList: true, subtree: false };

        const callback: MutationCallback = _.debounce(() => {
            el.querySelectorAll('span').forEach(item => {
                const style = getComputedStyle(item);
                const height = Number(style.height.replace('px', ''));
                const width = Number(style.width.replace('px', ''));
                item.style.fontSize = `${height / 2 + 3}px`;
                const textContent = item.textContent?.trim() || '';
                if (textContent.length > 1) {
                    // eslint-disable-next-line @typescript-eslint/tslint/config
                    item.style.width = `${width - 5 * ((item.textContent?.trim().length || 0) - 1)}px`;
                }
                const listItem = list.find(item => item[0] === textContent);
                item.style.backgroundColor = tagColor[listItem?.[2] || 0];
            });
        }, 20);

        // 创建一个观察器实例并传入回调函数
        const observer = new MutationObserver(callback);

        // 以上述配置开始观察目标节点
        observer.observe(el, config);

        return () => {
            WordCloud.stop();
            observer.disconnect();
        };
    }, [words]);

    return (
        <div className={styles.tags}>
            <div className={styles.title}>词云分析</div>
            <div className={styles.main}>
                {list.length ? (
                    <div id={'word-cloud'} style={{ width: '100%', height: '100%' }} />
                ) : (
                    <Empty image={EmptyImg} style={{ marginTop: '160px' }} description='暂无词语分析' />
                )}
            </div>
            <img className={styles.icon} src={dsIcon} alt='' />
        </div>
    );
}

const App: React.FC<Props> = ({ liveId, liveDetail }) => {
    const [data, setData] = useState<Console.Response.LiveAnalysisRespDTO>();
    useEffect(() => {
        liveAnalysisQuery({ liveId }).then(res => {
            setData(res.data);
            // || {
            //     liveId: 36,
            //     analysis: [
            //         {
            //             level: 1,
            //             title: '志制北温',
            //             content: 'nostrud',
            //             reason: 'fugiat elit sunt',
            //             solution: 'incididunt Ut',
            //             commentList: ['incididunt', 'nostrud cillum aliqua consequat cupidatat', 'fugiat esse'],
            //         },
            //         {
            //             level: 2,
            //             title: '记严走变构',
            //             content: 'et ut ea culpa',
            //             reason: 'in nostrud',
            //             solution: 'fugiat dolore',
            //             commentList: ['ut non minim aute magna', 'anim'],
            //         },
            //         {
            //             level: 3,
            //             title: '群行家通极',
            //             content: 'sit',
            //             reason: 'cupidatat proident sed dolore',
            //             solution: 'laboris qui ipsum in',
            //             commentList: ['aliqua sit', 'non voluptate Lorem proident', 'ipsum'],
            //         },
            //     ],
            //     suggestions: [
            //         {
            //             tag: 'culpa officia',
            //             content: 'dolor ullamco',
            //         },
            //         {
            //             tag: 'sed nulla tempor',
            //             content: 'et ut',
            //         },
            //         {
            //             tag: 'non',
            //             content: 'in',
            //         },
            //     ],
            //     words: ['sint', 'anim ea ullamco'],
            // }
        });
    }, []);

    return (
        <div className={styles.layout}>
            <div className={styles.left}>
                <Sidebar data={data} pushStatus={liveDetail?.pushStatus} />
                <Sidebar2 data={data} />
            </div>
            <Tags data={data} />
        </div>
    );
};
export default App;
