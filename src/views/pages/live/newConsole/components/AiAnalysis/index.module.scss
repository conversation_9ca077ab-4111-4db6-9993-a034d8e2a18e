/* App.module.scss */

/* 配置颜色变量 */
$primary-color: #4a90e2;
$tag-background-color: #ffffff;
$tag-hover-background-color: #b4d9f2;
$tag-text-color: #3a7ce2;
$sidebar-background-color: #ffffff;
$section-title-color: #333;
$section-content-color: #333;

.layout {
    display: flex;
    // min-height: 100vh;
    gap: 16px;
    padding-bottom: 40px;
    // background-color: #f3f4f7;

    .left {
        flex: 1;
    }

    .icon {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 53.5px;
    }

    .sidebar {
        background-color: $sidebar-background-color;
        box-sizing: border-box;
        box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.1);
        position: relative;
        border-radius: 6px;



        .title {
            margin-bottom: 20px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 22px;

            display: flex;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #eeeeee;

            .img {
                width: 21px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .main {
            display: flex;
            flex-wrap: wrap;
            padding: 0px 24px;
            gap: 20px;

            /* 设置元素之间的间距 */
            .section {
                width: calc(50% - 10px);

                margin-bottom: 20px;

                .sectionTitle {
                    margin-bottom: 12px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 18px;
                    color: $section-title-color;
                    display: flex;

                    .s {
                        width: 18px;
                        height: 18px;
                        border-radius: 4px 4px 4px 4px;
                        margin-right: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        font-family: PingFang SC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #fa2b19;
                    }
                }

                .sectionContent {
                    margin-bottom: 8px;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 18px;
                    color: $section-content-color;
                    display: flex;

                    .lable {
                        color: #999999;
                        flex-shrink: 0;
                    }

                    .content {
                        flex: 1;
                        line-height: 1.5;
                    }
                }

                .commentList {
                    background: rgba(247, 248, 249, 0.6);
                    border-radius: 6px 6px 6px 6px;
                    padding: 8px 12px;

                    .item {
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                    }
                }
            }
        }
    }

    .sidebar2 {
        margin-top: 16px;
        background-color: $sidebar-background-color;
        box-sizing: border-box;
        box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.1);
        border-radius: 6px;
        position: relative;

        .title {
            margin-bottom: 20px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 22px;

            display: flex;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #eeeeee;

            .img {
                width: 21px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .section {
            padding: 0px 24px;
            padding-bottom: 24px;

            display: flex;
            flex-wrap: wrap;
            gap: 12px;

            /* 设置元素之间的间距 */
            .sectionContent {
                width: calc(50% - 6px);
                display: flex;

                .lable {
                    height: 18px;
                    padding: 0px 4px;
                    margin-right: 8px;
                    border-radius: 4px 4px 4px 4px;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    background: #fff1f0;
                    color: #fa2b19;
                }

                .t {
                    flex: 1;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #3d3d3d;
                    line-height: 18px;
                }
            }
        }
    }

    .tags {
        flex: 0 0 420px;
        background-color: #fff;
        box-sizing: border-box;
        border-radius: 6px;
        box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.1);
        display: flex;
        flex-direction: column;
        position: relative;

        .title {
            margin-bottom: 20px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 22px;

            display: flex;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #eeeeee;
            flex-shrink: 0;

            .img {
                width: 21px;
                height: 24px;
                margin-right: 4px;
            }
        }

        .main {
            flex: auto;
            position: relative;
            padding: 20px 10px;

            #word-cloud {
                width: 100%;
                height: 100%;
            }

            .word-item {
                border-radius: 4px;
                color: #333333 !important;
                text-align: center;
                // padding: 8px;
                // box-sizing: border-box;

            }

            // display: flex;
            // flex-wrap: wrap;
            // justify-content: center;
            // gap: 16px;
            // .tag {
            //     border-radius: 16px 16px 16px 16px;
            //     height: 36px;
            //     padding: 0px 12px;
            //     background: #ebfaf6;

            //     display: flex;
            //     align-items: center;
            //     justify-content: center;

            //     font-family: PingFang SC, PingFang SC;
            //     font-weight: 400;
            //     font-size: 16px;
            //     color: #333333;
            // }
        }
    }
}