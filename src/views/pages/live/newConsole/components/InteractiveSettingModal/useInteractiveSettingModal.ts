/**
 * @Owners ljh
 * @Title 连麦设置弹窗
 */
import { type Console } from '@/api/interface/live/console';
import { getInteractiveConfig, saveInteractiveConfig } from '@/api/modules/live/console';
import { useCallback, useRef, useState } from 'react';

export const useInteractiveSettingModal = () => {
    const [loading, setLoading] = useState(false);
    const [saveLoading, setSaveLoading] = useState(false);
    const requestId = useRef<number>(0);

    /**
     * 获取连麦配置
     * @param liveId 直播间id
     */
    const fetchInteractiveConfig = useCallback(async (liveId: number) => {
        setLoading(true);
        requestId.current++;
        const currentRequestId = requestId.current;
        try {
            const res = await getInteractiveConfig({ liveId });
            return res.data;
        } catch (error) {
            throw error;
        } finally {
            if (currentRequestId === requestId.current) {
                setLoading(false);
            }
        }
    }, []);

    /**
     * 保存连麦配置
     * @param params 连麦配置
     */
    const fetchSaveInteractiveConfig = useCallback(
        async (params: Console.Params.SaveInteractiveConfig) => {
            if (saveLoading) return;
            setSaveLoading(true);
            try {
                await saveInteractiveConfig(params);
            } catch (error) {
                throw error;
            } finally {
                setSaveLoading(false);
            }
        },
        [saveLoading]
    );

    return {
        fetchInteractiveConfig,
        fetchSaveInteractiveConfig,
        loading,
        saveLoading,
    };
};
