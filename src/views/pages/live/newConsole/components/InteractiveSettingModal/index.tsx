/**
 * @Owners ljh
 * @Title 连麦设置弹窗
 */
import { type Console } from '@/api/interface/live/console';
import { Form, Modal, Radio, Switch, message } from 'antd';
import { isNull, isUndefined } from 'lodash';
import { forwardRef, useCallback, useImperativeHandle, useState } from 'react';

import './index.scss';
import { useInteractiveSettingModal } from './useInteractiveSettingModal';

export interface InteractiveSettingModalRef {
    open(liveId: number): void;
}

export interface InteractiveSettingModalProps {}

/** 默认媒体 */
enum DefaultMedia {
    /** 音频 */
    AUDIO = 1,
    /** 视频 */
    VIDEO = 2,
}

/** 连麦模式 */
enum LinkMicMode {
    /** 默认静音 */
    DEFAULT_MUTE = 1,
    /** 默认音频 */
    DEFAULT_AUDIO = 2,
    /** 默认视频 */
    DEFAULT_VIDEO = 3,
}

/** 观众连线申请 */
const ApplyLinkMicOptions = [
    {
        label: '需要申请',
        value: 1,
    },
    {
        label: '不需要申请',
        value: 0,
    },
];

/** 连麦模式 */
const LinkMicModeOptions = [
    {
        label: '默认静音',
        value: LinkMicMode.DEFAULT_MUTE,
    },
    {
        label: '默认音频',
        value: LinkMicMode.DEFAULT_AUDIO,
    },
    {
        label: '默认视频',
        value: LinkMicMode.DEFAULT_VIDEO,
    },
];

const InteractiveSettingModal = forwardRef<InteractiveSettingModalRef, InteractiveSettingModalProps>((props, ref) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    const { fetchInteractiveConfig, fetchSaveInteractiveConfig, saveLoading, loading } = useInteractiveSettingModal();
    /** 连麦配置 */
    const [interactiveConfig, setInteractiveConfig] = useState<Console.Response.GetInteractiveConfig | null>(null);

    useImperativeHandle(ref, () => ({
        open: async liveId => {
            const config = await fetchInteractiveConfig(liveId);
            if (config) {
                setInteractiveConfig(config);

                const formData: Record<string, unknown> = {
                    enableMic: Boolean(config.enableMic),
                    auditStrategy: config.auditStrategy,
                    allowCamera: Boolean(config.allowCamera),
                };

                if (config.defaultMedia === DefaultMedia.AUDIO) {
                    formData.defaultLinkMicMode = config.autoMute ? LinkMicMode.DEFAULT_MUTE : LinkMicMode.DEFAULT_AUDIO;
                } else if (config.defaultMedia === DefaultMedia.VIDEO) {
                    formData.defaultLinkMicMode = LinkMicMode.DEFAULT_VIDEO;
                }

                form.setFieldsValue(formData);
            }
            setVisible(true);
        },
    }));

    const isUndefinedOrNull = useCallback((value: unknown) => isUndefined(value) || isNull(value), []);

    const handleCancel = useCallback(() => {
        setVisible(false);
    }, []);

    const handleOk = useCallback(async () => {
        const formData = form.getFieldsValue();
        const payload: Console.Params.SaveInteractiveConfig = {
            enableMic: isUndefinedOrNull(formData.enableMic) ? formData.enableMic : Number(formData.enableMic),
            auditStrategy: isUndefinedOrNull(formData.auditStrategy) ? formData.auditStrategy : Number(formData.auditStrategy),
            allowCamera: isUndefinedOrNull(formData.allowCamera) ? formData.allowCamera : Number(formData.allowCamera),
            liveId: interactiveConfig?.liveId,
        };

        // remark 因为 观众连线 拆分了两个字段
        if (formData.defaultLinkMicMode === LinkMicMode.DEFAULT_MUTE) {
            payload.autoMute = 1;
            payload.defaultMedia = DefaultMedia.AUDIO;
        } else if (formData.defaultLinkMicMode === LinkMicMode.DEFAULT_AUDIO) {
            payload.autoMute = 0;
            payload.defaultMedia = DefaultMedia.AUDIO;
        } else if (formData.defaultLinkMicMode === LinkMicMode.DEFAULT_VIDEO) {
            payload.autoMute = 0;
            payload.defaultMedia = DefaultMedia.VIDEO;
        }

        await fetchSaveInteractiveConfig(payload);
        message.success('保存成功');
        handleCancel();
    }, [form, interactiveConfig?.liveId, handleCancel]);

    const handleAfterClose = useCallback(() => {
        form.resetFields();
        setInteractiveConfig(null);
    }, [form]);

    return (
        <Modal
            className='interactive-setting-modal'
            open={visible}
            title='连麦设置'
            width={560}
            onOk={handleOk}
            onCancel={handleCancel}
            confirmLoading={loading || saveLoading}
            cancelButtonProps={{ disabled: saveLoading }}
            afterClose={handleAfterClose}
        >
            <Form
                form={form}
                className='interactive-setting-modal-form'
                colon={false}
                labelCol={{ span: 12 }}
                labelAlign='left'
                disabled={loading}
            >
                <Form.Item
                    label={
                        <span>
                            允许观众连线
                            <span className='interactive-setting-modal-custom-label-second'>
                                （最多{interactiveConfig?.maxUsers}人可同时连线）
                            </span>
                        </span>
                    }
                    name='enableMic'
                >
                    <Switch />
                </Form.Item>
                <Form.Item label='观众连线申请' name='auditStrategy'>
                    <Radio.Group
                        className='interactive-setting-modal-radio-group'
                        buttonStyle='solid'
                        optionType='button'
                        options={ApplyLinkMicOptions}
                    />
                </Form.Item>
                <Form.Item label='观众连线' name='defaultLinkMicMode'>
                    <Radio.Group
                        className='interactive-setting-modal-radio-group'
                        buttonStyle='solid'
                        optionType='button'
                        options={LinkMicModeOptions}
                    />
                </Form.Item>
                <Form.Item label='允许观众开启摄像头' name='allowCamera'>
                    <Switch />
                </Form.Item>
            </Form>
        </Modal>
    );
});

export default InteractiveSettingModal;
