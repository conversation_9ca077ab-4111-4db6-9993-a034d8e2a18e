.interactive-setting-modal {
    .interactive-setting-modal-form {
        .ant-form-item {
            margin-bottom: 0;
            box-shadow: inset 0px -0.5px 0px 0px #eeeeee;
        }

        .interactive-setting-modal-custom-label-second {
            font-size: 14px;
            color: #999;
        }

        .ant-form-item-no-colon {
            height: 56px;
            font-size: 16px;
        }

        .ant-form-item-control {
            display: inline-flex;
            justify-content: center;
        }
    }

    .ant-form-item-control-input-content {
        text-align: end;
    }

    .interactive-setting-modal-radio-group {
        padding: 2px;
        color: #333;
        background: #f5f5f5;
        border-radius: 4px;

        .ant-radio-button-wrapper {
            padding-inline: 8px;
            height: 20px;
            line-height: 20px;
            font-size: 12px;
            border-radius: 2px;
            border: none;
            background: transparent;

            &:first-child {
                border-inline-start: none;
                border-start-start-radius: 2px;
                border-end-start-radius: 2px;
            }

            &:last-child {
                border-start-end-radius: 2px;
                border-end-end-radius: 2px;
            }

            &::before {
                display: none;
            }
        }

        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
            color: #333;
            background: #fff;
            box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.03), 0px 1px 6px -1px rgba(0, 0, 0, 0.02),
                0px 2px 4px 0px rgba(0, 0, 0, 0.02);
        }
    }
}
