/**
 * @Owners ljh
 * @Title 高阶聊天记录项（主要赋予额外的操作）
 */
import { type Console } from '@/api/interface/live/console';
import CommentUpperWallIcon from '@/assets/images/btn-comment-upper-wall.png';
import CommentIcon from '@/assets/images/btn-comment.png';
import MoreOperateIcon from '@/assets/images/more-operate.png';
import { IMMessageStatus } from '@/consts/cLive';
import { Button, Dropdown, Tooltip, type MenuProps } from 'antd';
import { forwardRef, useCallback, useContext, useMemo, useState } from 'react';

import { LiveContext } from '../../contexts/LiveContext';
import ChatMessage, { type ChatMessageProps } from '../ChatMessage';

import './index.scss';
export interface HOCChatMessageProps extends ChatMessageProps {
    /** 隐藏按钮点击 */
    onToggleHide?(message: Console.ChatMessage): void;
    /** 禁言按钮点击 */
    onToggleBan?(message: Console.ChatMessage): void;
    /** 回复按钮点击 */
    onReplyMessage?(message: Console.ChatMessage): void;
    /** 评论上墙按钮点击 */
    onSetUpperWall?(message: Console.ChatMessage): void;
    /** 拉黑按钮点击 */
    onToggleBlock?(message: Console.ChatMessage): void;
}

const HOCChatMessage = forwardRef<HTMLDivElement, HOCChatMessageProps>((props: HOCChatMessageProps, ref) => {
    const { message, isMuted, style, onToggleHide, onToggleBan, onReplyMessage, onSetUpperWall, onToggleBlock, ...restProps } =
        props;
    const { liveDetail } = useContext(LiveContext);

    const [open, setOpen] = useState(false);

    /** 隐藏按钮提示 */
    const hideBtnTooltip = useMemo(
        () => (message.msgStatus === IMMessageStatus.Hide ? '显示评论' : '隐藏评论'),
        [message.msgStatus]
    );

    /** 禁言按钮提示 */
    const banBtnTooltip = useMemo(() => (isMuted ? '解除拉灰' : '拉灰'), [isMuted]);

    const toggleHideMessage = useCallback(() => {
        onToggleHide && onToggleHide(message);
    }, [onToggleHide, message]);

    const toggleBanMessage = useCallback(() => {
        onToggleBan && onToggleBan(message);
    }, [onToggleBan, message]);

    const toggleBlockMessage = useCallback(() => {
        onToggleBlock && onToggleBlock(message);
    }, [onToggleBlock, message]);

    const handleReplyMessage = useCallback(() => {
        onReplyMessage && onReplyMessage(message);
    }, [onReplyMessage, message]);

    const handleSetUpperWall = useCallback(() => {
        onSetUpperWall && onSetUpperWall(message);
    }, [onSetUpperWall, message]);

    const menuItems: MenuProps['items'] = useMemo(() => {
        const defaultItems: MenuProps['items'] = [
            {
                label: '回复',
                key: 'reply',
            },
            {
                label: '评论上墙',
                key: 'wall',
            },
            {
                label: hideBtnTooltip,
                key: 'hide',
            },
        ];

        if (message.from !== String(liveDetail?.imUserId)) {
            // 拉灰
            defaultItems.push({
                label: banBtnTooltip,
                key: 'mute',
            });

            // 拉黑
            defaultItems.push({
                label: '拉黑',
                key: 'block',
            });
        }

        return defaultItems;
    }, [message, hideBtnTooltip, banBtnTooltip]);

    const handleMenuClick: MenuProps['onClick'] = useCallback(
        ({ key }: { key: string }) => {
            switch (key) {
                case 'reply':
                    handleReplyMessage();
                    break;
                case 'wall':
                    handleSetUpperWall();
                    break;
                case 'hide':
                    toggleHideMessage();
                    break;
                case 'mute':
                    toggleBanMessage();
                    break;
                case 'block':
                    toggleBlockMessage();
            }
        },
        [handleReplyMessage, toggleHideMessage, toggleBanMessage, toggleBlockMessage]
    );

    return (
        <div className={`hoc-chat-message ${open ? 'open' : ''}`} data-id={message.ID} ref={ref} style={style}>
            <ChatMessage message={message} isMuted={isMuted} {...restProps} />

            <div className='hoc-chat-message-actions'>
                <Tooltip title='回复'>
                    <Button
                        className='hoc-chat-message-actions-btn'
                        type='link'
                        size='small'
                        icon={<img src={CommentIcon} />}
                        onClick={handleReplyMessage}
                    />
                </Tooltip>

                <Tooltip title='评论上墙'>
                    <Button
                        className='hoc-chat-message-actions-btn'
                        type='link'
                        size='small'
                        icon={<img src={CommentUpperWallIcon} />}
                        onClick={handleSetUpperWall}
                    />
                </Tooltip>
                <Dropdown
                    overlayClassName='hoc-chat-message-dropdown'
                    menu={{ items: menuItems, onClick: handleMenuClick }}
                    placement='bottomCenter'
                    onOpenChange={setOpen}
                    arrow={false}
                >
                    <Button
                        className='hoc-chat-message-actions-btn'
                        type='link'
                        size='small'
                        icon={<img src={MoreOperateIcon} alt='more-operate' />}
                    />
                </Dropdown>
            </div>
        </div>
    );
});

export default HOCChatMessage;
