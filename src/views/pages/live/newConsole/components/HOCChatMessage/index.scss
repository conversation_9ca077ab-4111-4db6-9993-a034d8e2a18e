.ant-select-dropdown {
    .ant-select-item.ant-select-item-option {
        &:hover {
            color: #00B68F;
        }
    }
}

.hoc-chat-message-dropdown {
    min-width: 118px !important;
    text-align: center;
}

.hoc-chat-message {
    position: relative;
    overflow: hidden;
    padding: 8px;
    border-radius: 4px;
    transition: background 0.5s;
    cursor: pointer;

    .hoc-chat-message-actions {
        position: absolute;
        top: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 12px;
        height: 100%;
        padding: 0 12px 0 54px;
        background: linear-gradient(-90deg, #f7f8f9 65%, rgba(247, 248, 249, 0) 100%);
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.5s;

        .ant-btn {
            width: 16px;
        }

        .ant-btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ant-btn-variant-link {
            color: #666666;
        }

        img {
            width: 16px;
            height: 16px;
        }
    }

    &.open,
    &:hover {
        background: #f7f8f9;

        .hoc-chat-message-actions {
            opacity: 1;
            visibility: visible;
        }
    }
}