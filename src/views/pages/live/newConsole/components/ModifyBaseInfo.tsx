/**
 * @Owners xj
 * @Title 修改直播间信息
 */

import { type Console } from '@/api/interface/live/console';
import { updateLive } from '@/api/modules/live/console';
import MJUpload, { type UploadFile } from '@/views/components/common/MJUpload/index';
import { ModalForm, ProFormDateTimePicker, ProFormRadio, ProFormText, type ProFormInstance } from '@ant-design/pro-components';
import { cLive } from '@consts';
import { Form, message } from 'antd';
import dayjs from 'dayjs';
import { type FC, useEffect, useRef, useState } from 'react';

import { type Dataitem } from '../index';

type IModalProps = Console.Params.UpdateLive;

interface IProps {
    showModel: boolean;
    dataSource?: Dataitem;
    onModelClose(): void;
    afterSubmit(): void;
}

const maxFileSize = 5 * 1024 * 1024;

const ModifyBaseInfo: FC<IProps> = props => {
    const { showModel, dataSource, onModelClose, afterSubmit } = props;
    const formRef = useRef<ProFormInstance>();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [liveImage, setLiveImage] = useState<UploadFile[]>([]);

    const onFinish = async (values: IModalProps) => {
        const { pushType, liveTheme, preStartTime, liveName } = values;
        if (fileList.length === 0 || !fileList[0].url) {
            message.error('直播间封面为必填项，请完善后再保存');
            return;
        }
        await updateLive({
            pushType,
            liveTheme,
            preStartTime,
            liveName,
            coverUrl: fileList[0].url,
            liveCoverUrl: liveImage[0].url,
            id: dataSource?.id,
        });
        message.success('修改成功');
        afterSubmit();
        setFileList([]);
        formRef.current?.resetFields();
    };

    useEffect(() => {
        if (dataSource && showModel) {
            const { liveTheme, pushType, coverUrl, preStartTime, liveName, liveCoverUrl } = dataSource;
            formRef.current?.setFieldsValue({
                liveTheme,
                coverUrl,
                pushType,
                preStartTime,
                liveName,
                liveCoverUrl,
            });
            coverUrl && setFileList([{ url: coverUrl, uid: '', name: '' }]);
            liveCoverUrl && setLiveImage([{ url: liveCoverUrl, uid: '', name: '' }]);
        }
    }, [showModel]);

    return (
        <ModalForm
            formRef={formRef}
            title='修改基础信息'
            layout='horizontal'
            open={showModel}
            submitTimeout={1000}
            onFinish={onFinish}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 14 }}
            modalProps={{
                centered: true,
                onCancel: () => {
                    onModelClose();
                    formRef.current?.resetFields();
                },
            }}
        >
            <ProFormText
                label='直播名称'
                name='liveName'
                width='md'
                fieldProps={{
                    maxLength: 50,
                }}
            />
            <ProFormText
                label='直播主题'
                name='liveTheme'
                width='md'
                fieldProps={{
                    maxLength: 50,
                }}
            />
            <Form.Item
                labelCol={{ span: 6 }}
                name='coverUrl'
                label='直播间分享封面'
                rules={[{ required: true, message: '请上传直播间分享封面' }]}
            >
                <MJUpload
                    name='file'
                    fileList={fileList}
                    maxFileLength={1}
                    acceptExt={'image/*'}
                    maxFileSize={maxFileSize}
                    maxFileSizeTips='图片超过5M,请重新上传'
                    listType='picture-card'
                    tipDesc='建议尺寸 ：5:4；分辨率：1000*800图片；大小：2M以下'
                    onChange={(_fileList: UploadFile[]) => {
                        setFileList(_fileList);
                    }}
                />
            </Form.Item>
            <Form.Item
                labelCol={{ span: 6 }}
                name='liveCoverUrl'
                label='直播间封面'
                rules={[{ required: true, message: '请上传直播间封面' }]}
            >
                <MJUpload
                    name='file'
                    fileList={liveImage}
                    maxFileLength={1}
                    acceptExt={'image/*'}
                    maxFileSize={maxFileSize}
                    maxFileSizeTips='图片超过5M,请重新上传'
                    listType='picture-card'
                    tipDesc='建议尺寸：1280*720 或 5:4 比例的横图'
                    onChange={(_fileList: UploadFile[]) => {
                        setLiveImage(_fileList);
                    }}
                />
            </Form.Item>
            <ProFormRadio.Group
                label='开播设备'
                name='pushType'
                rules={[{ required: true, message: '开播设备为必填项，请完善后再保存' }]}
                options={[
                    {
                        label: '外设直播',
                        value: 1,
                    },
                    {
                        label: '手机直播',
                        value: 2,
                    },
                ]}
            />
            {[cLive.PushStatusMap.NoLive, cLive.PushStatusMap.NoStart].includes(dataSource?.pushStatus as unknown as number) && (
                <ProFormDateTimePicker
                    label='直播时间'
                    name='preStartTime'
                    fieldProps={{
                        disabledDate: current =>
                            current && (current < dayjs().subtract(1, 'days') || current > dayjs().add(2, 'months')),
                    }}
                />
            )}
        </ModalForm>
    );
};

export default ModifyBaseInfo;
