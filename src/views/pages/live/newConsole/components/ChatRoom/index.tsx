/**
 * @Owners ljh
 * @Title 聊天室
 */
import { type Console } from '@/api/interface/live/console';
import {
    sendComment,
    sendCommentAndUpperWall,
    setCommentStatus,
    setUpperWall,
    setUserBlock,
    setUserMuted,
} from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import { IMMessageStatus, PushStatusMap } from '@/consts/cLive';
import { modal } from '@/views/components/UseAppPrompt';
import Show from '@/views/components/common/Show';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Empty, Input, Space, Spin, message } from 'antd';
import classNames from 'classnames';
import { type CSSProperties, type ChangeEvent, type KeyboardEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
    AutoSizer,
    CellMeasurer,
    CellMeasurerCache,
    List,
    type ListProps,
    type ListRowRenderer,
    type ScrollParams,
} from 'react-virtualized';

import { useChatHistory } from '../../hooks/useChatHistory';
import { useMessage } from '../../hooks/useMessage';
import FloatNewMsgTip from '../FloatNewMsgTip';
import HOCChatMessage from '../HOCChatMessage';

import './index.scss';

const { TextArea } = Input;

/** 停止隐藏评论的直播状态 */
const STOP_HIDE_LIVE_STATUS = [PushStatusMap.NoPush, PushStatusMap.StopPush, PushStatusMap.Expired];

export interface ChatRoomProps {
    className?: string;
    style?: CSSProperties;
    /** 群组id */
    groupId: number;
    /** 直播状态 */
    liveStatus?: PushStatusMap;
    /** 空白时是否显示占位符 */
    showPlaceholderWhenBlank?: boolean;
    /** 用户信息 */
    userInfo?: {
        level?: number;
        levelIcon?: string;
        role?: number;
        imUserId?: number;
        imNickname?: string;
        imAvatar?: string;
    };
}

const ChatRoom = (props: ChatRoomProps) => {
    const { className, style, groupId, userInfo, showPlaceholderWhenBlank = true, liveStatus } = props;
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId'));
    const chatRoomWindowRef = useRef<HTMLDivElement>(null); // 聊天室窗口
    const { loading, fetchChatHistory } = useChatHistory(groupId);
    const [chatContent, setChatContent] = useState(''); // 输入框内容
    /** at 用户列表 */
    const atUserList = useRef<Omit<Console.AtUser, 'startIndex'>[]>([]);
    /** 当前回复的消息 */
    const [replyMessage, setReplyMessage] = useState<Console.ChatMessage | null>(null);
    /** 输入框 ref */
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const classes = classNames('chat-room', className);

    const {
        messageList,
        setMessageList,
        userMutedList,
        chat,
        scrollToIndex,
        setScrollToIndex,
        stopRenderIndex,
        autoScrollBottom,
        unreadMessageCount,
        setUnreadMessageCount,
        isFirstRender,
    } = useMessage({
        groupId,
    });

    const messageRowCacheRef = useRef(
        new CellMeasurerCache({
            fixedWidth: true,
            minHeight: 40, // 最小高度
            defaultHeight: 40, // 默认高度
        })
    );

    /**
     * @description: 滚动到最底部
     */
    const handleScrollToBottom = useCallback(() => {
        setUnreadMessageCount(0);
        setScrollToIndex(messageList.length - 1);
    }, [messageList]);

    /**
     * @description: 加载更多聊天记录
     */
    const handleLoadMoreChatHistory = useCallback(async () => {
        const messages = await fetchChatHistory();
        if (!messages) return undefined;
        // messages 旧 -> 新
        setMessageList(prev => [...messages, ...prev], true);
        return messages;
    }, [fetchChatHistory, setMessageList]);

    useEffect(() => {
        handleLoadMoreChatHistory();
    }, []);

    const handleTextareaChange = useCallback((e: ChangeEvent<HTMLTextAreaElement>) => {
        const value = e.target.value;
        if (!value && atUserList.current && atUserList.current.length > 0) {
            atUserList.current = [];
        }

        setChatContent(value);
    }, []);

    /**
     * @description: 隐藏/显示评论
     */
    const handleToggleHideMessage = (targetMessage: Console.ChatMessage) => {
        if (!liveStatus || STOP_HIDE_LIVE_STATUS.includes(liveStatus)) {
            message.warning('当前直播间未开播，无法隐藏评论');
            return;
        }
        // 1 开启隐藏 0 关闭隐藏
        const nextHideCode = targetMessage.msgStatus === IMMessageStatus.Hide ? 0 : 1;
        const title = nextHideCode ? '确定要隐藏该评论吗？' : '确定要显示该评论吗？';
        const content = nextHideCode ? '隐藏后该评论将在C端不可见。' : '显示后该评论将在C端恢复展示。';
        const messageText = nextHideCode ? '该评论已隐藏' : '该评论已显示';

        modal.confirm({
            title,
            content,
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await setCommentStatus({
                    imMsgId: targetMessage.ID,
                    groupId,
                    controlType: 1,
                    controlOperate: nextHideCode,
                });
                setMessageList(prev =>
                    prev.map(msg => {
                        if (msg.ID === targetMessage.ID) {
                            // 找到目标消息，设置状态
                            msg.msgStatus = nextHideCode ? IMMessageStatus.Hide : IMMessageStatus.Normal;
                        }
                        return msg;
                    })
                );
                message.success(messageText);
            },
        });
    };

    /**
     * @description: 回复消息
     */
    const handleReplyMessage = useCallback((targetMessage: Console.ChatMessage) => {
        setChatContent(`@${targetMessage.nick} `);
        setReplyMessage(targetMessage);
        atUserList.current = [
            {
                id: targetMessage.from,
                nickname: targetMessage.nick,
            },
        ];
        textareaRef.current?.focus();
    }, []);

    /**
     * @description: 评论上墙
     */
    const handleSetUpperWall = useCallback((targetMessage: Console.ChatMessage) => {
        setUpperWall({
            liveId,
            message: targetMessage.payload?.text,
            nickName: targetMessage.nick,
        });
    }, []);

    /**
     * @description: 取消回复
     */
    const handleCancelReply = useCallback(() => {
        setReplyMessage(null);
        setChatContent('');
    }, []);

    /**
     * @description: 禁言/解禁用户
     */
    const handleToggleBanMessage = (targetMessage: Console.ChatMessage) => {
        const userId = Number(targetMessage.from);
        const nextBan = !userMutedList.includes(userId);
        const title = nextBan ? '确定要拉灰该用户吗？' : '确定要对该用户解除拉灰吗？';
        const content = nextBan ? '拉灰后该用户将在本场直播间发送的评论不显示。' : '解除拉灰后该用户将在本场直播间中恢复评论。';
        const messageText = nextBan ? '拉灰成功' : '拉灰成功';

        modal.confirm({
            title,
            content,
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await setUserMuted({
                    userId,
                    groupId,
                    liveId,
                    muteStatus: nextBan ? 1 : 0,
                });

                message.success(messageText);
            },
        });
    };

    /**
     * @description: 拉黑/解除拉黑用户
     */
    const handleToggleBlockMessage = useCallback((targetMessage: Console.ChatMessage) => {
        const userId = Number(targetMessage.from);

        modal.confirm({
            title: '确定要拉黑该用户吗？',
            content: '拉黑后该用户将移出本场直播间。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await setUserBlock({
                    userId,
                    liveId,
                    operateType: 0,
                });

                message.success(`已将${targetMessage.nick}拉黑并移出本场直播间`);
            },
        });
    }, []);

    /**
     * @description: 发送消息
     */
    const performSendMessage = useCallback(
        async (type: 'send' | 'sendAndUpperWall' = 'send') => {
            if (!chat?.current || !groupId || !chatContent) {
                return;
            }

            let value = chatContent;
            // 处理 @ 用户
            let atUserListRes: Console.AtUser[] = [];
            if (atUserList.current && atUserList.current.length > 0) {
                // 获取 @ 用户列表 并替换命中内容
                const matchUserList = atUserList.current
                    .reduce<(Console.AtUser & { length: number })[]>((acc, item) => {
                        const pattern = `@${item.nickname.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*`;
                        const regex = new RegExp(pattern);
                        const match = regex.exec(value);
                        if (!match) return acc;
                        value = value.replace(match[0], '');

                        return acc.concat({
                            ...item,
                            startIndex: match.index,
                            length: match[0].length,
                        });
                    }, [])
                    .sort((a, b) => a.startIndex - b.startIndex);

                // 根据最终的消息内容，重新计算 @ 用户列表 startIndex
                let diffIndex = 0;
                atUserListRes = matchUserList.map(item => {
                    const atUserItem = {
                        id: item.id,
                        nickname: item.nickname,
                        startIndex: item.startIndex - diffIndex,
                    };
                    diffIndex += item.length;
                    return atUserItem;
                });
            }

            const sendFn = type === 'send' ? sendComment : sendCommentAndUpperWall;
            await sendFn({
                groupId,
                level: userInfo?.level,
                levelIcon: userInfo?.levelIcon,
                msgContentText: value || ' ',
                liveId,
                role: userInfo?.role,
                senderId: userInfo?.imUserId,
                senderNickname: userInfo?.imNickname,
                atUserList: atUserListRes.length > 0 ? atUserListRes : undefined,
            });

            setChatContent('');
            setReplyMessage(null);
            atUserList.current = [];
        },
        [chatContent, userInfo, groupId]
    );

    /**
     * @description: 发送消息
     */
    const handleSendMessage = useCallback(async () => {
        if (chatContent.trim() === '') {
            message.warning('请输入评论内容');
            return;
        }
        performSendMessage('send');
    }, [performSendMessage, chatContent]);

    /**
     * @description: 发送消息并上墙
     */
    const handleSendMessageAndUpperWall = useCallback(async () => {
        if (chatContent.trim() === '') {
            message.warning('请输入评论内容');
            return;
        }
        performSendMessage('sendAndUpperWall');
    }, [performSendMessage, chatContent]);

    /**
     * @description: 监听文本框按下事件
     */
    const handleTextAreaKeyDown = useCallback(
        (event: KeyboardEvent<HTMLTextAreaElement>) => {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                handleSendMessage();
            }
        },
        [handleSendMessage]
    );

    /**
     * 行渲染
     */
    const rowRenderer: ListRowRenderer = useCallback(
        ({ index, key, parent, style }) => {
            const msg = messageList[index];

            return (
                <CellMeasurer key={key} cache={messageRowCacheRef.current} parent={parent} columnIndex={0} rowIndex={index}>
                    {({ measure, registerChild }) => (
                        <HOCChatMessage
                            ref={registerChild}
                            style={style}
                            key={msg.ID}
                            message={msg}
                            isMuted={userMutedList.includes(Number(msg.from))}
                            onToggleHide={handleToggleHideMessage}
                            onToggleBan={handleToggleBanMessage}
                            onReplyMessage={handleReplyMessage}
                            onSetUpperWall={handleSetUpperWall}
                            onToggleBlock={handleToggleBlockMessage}
                            measure={measure}
                        />
                    )}
                </CellMeasurer>
            );
        },
        [messageList, handleToggleHideMessage, handleToggleBanMessage, handleReplyMessage]
    );

    /**
     * @description: 监听聊天记录滚动事件
     */
    const handleListScroll = useCallback(
        (event: ScrollParams) => {
            const { scrollTop } = event;
            if (scrollTop < 80 && !isFirstRender.current) {
                handleLoadMoreChatHistory().then(messages => {
                    if (messages?.length) {
                        setScrollToIndex(messages.length - 1);
                        messageRowCacheRef.current.clearAll();
                    }
                });
            } else {
                setScrollToIndex(undefined);
            }
        },
        [handleLoadMoreChatHistory]
    );

    const handleRowsRendered = useCallback<Required<ListProps>['onRowsRendered']>(
        info => {
            const { stopIndex } = info;
            stopRenderIndex.current = stopIndex;

            if (!autoScrollBottom.current) {
                autoScrollBottom.current = stopIndex >= messageList.length - 1;
                if (autoScrollBottom.current) {
                    setUnreadMessageCount(0);
                }
            }
        },
        [messageList]
    );

    return (
        <div className={classes} style={style}>
            {/* S 聊天记录 */}
            <div
                className={classNames('chat-room-window', { 'chat-room-window--empty': messageList.length === 0 })}
                id='chat-room-window'
                ref={chatRoomWindowRef}
            >
                <Show
                    when={messageList.length > 0}
                    fallback={showPlaceholderWhenBlank ? <Empty image={EmptyImg} description='暂无评论' /> : null}
                >
                    <Show when={loading}>
                        <div className='chat-room-window-loading'>
                            <Spin size='small' />
                        </div>
                    </Show>
                    <div className='chat-room-window-inner'>
                        <AutoSizer>
                            {({ width, height }: { width: number; height: number }) => (
                                <List
                                    className='chat-room-window-list'
                                    width={width}
                                    height={height}
                                    rowCount={messageList.length}
                                    rowHeight={messageRowCacheRef.current.rowHeight}
                                    rowRenderer={rowRenderer}
                                    deferredMeasurementCache={messageRowCacheRef.current}
                                    overscanRowCount={10}
                                    scrollToIndex={scrollToIndex}
                                    onScroll={handleListScroll}
                                    onRowsRendered={handleRowsRendered}
                                />
                            )}
                        </AutoSizer>
                    </div>
                </Show>

                <FloatNewMsgTip unreadMessageCount={unreadMessageCount} onClick={handleScrollToBottom} />
            </div>
            {/* E 聊天记录 */}
            <div className='chat-room-footer'>
                <div className='chat-room-input-wrapper'>
                    <Show when={!!replyMessage}>
                        <div className='chat-room-input-reply-message'>
                            <span className='cri-reply-message-text'>
                                {replyMessage?.nick}：{replyMessage?.payload?.text}
                            </span>
                            <Button
                                className='cri-reply-message-close'
                                color='default'
                                variant='text'
                                size='small'
                                icon={<CloseOutlined />}
                                onClick={handleCancelReply}
                            />
                        </div>
                    </Show>
                    <TextArea
                        className='chat-room-input'
                        ref={textareaRef}
                        rows={4}
                        maxLength={300}
                        value={chatContent}
                        placeholder='主播直接发评论'
                        onChange={handleTextareaChange}
                        onKeyDown={handleTextAreaKeyDown}
                    />
                    <div className='chat-room-send-btn-wrapper'>
                        <Space>
                            <Button
                                className='chat-room-send-upper-wall-btn'
                                type='default'
                                color='primary'
                                variant='outlined'
                                onClick={handleSendMessageAndUpperWall}
                            >
                                发送并上墙
                            </Button>
                            <Button className='chat-room-send-btn' type='primary' onClick={handleSendMessage}>
                                发送
                            </Button>
                        </Space>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ChatRoom;
