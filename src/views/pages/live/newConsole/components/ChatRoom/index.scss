.chat-room {
    height: 100%;
    display: flex;
    flex-direction: column;

    .chat-room-window {
        position: relative;
        flex-grow: 1;
        // padding: 0 24px;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;

        &.chat-room-window--empty {
            justify-content: center;
            align-items: center;
        }
    }

    .chat-room-window-loading {
        padding: 8px 0;
        text-align: center;
    }

    .chat-room-window-inner {
        flex: 1 1 auto;
    }

    .chat-room-window-list {
        // display: flex;
        // flex-direction: column-reverse;
        // width: 100%;
        box-sizing: border-box;
        padding: 0 24px;

        &::-webkit-scrollbar {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(241, 238, 238, 0.6);
        }
    }

    .chat-room-footer {
        position: relative;
        flex-shrink: 0;
        padding: 0 24px 24px;
    }

    .chat-room-input-wrapper {
        position: relative;
        overflow: hidden;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #d9d9d9;
    }

    .chat-room-input-reply-message {
        display: flex;
        align-items: center;
        border-radius: 4px;
        background: #f7f8f9;
        padding: 4px 8px;
        margin-bottom: 4px;
    }

    .cri-reply-message-text {
        flex-grow: 1;
        overflow: hidden;
        color: #666666;
        font-size: 14px;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .cri-reply-message-close {
        flex-shrink: 0;
        color: #999;
    }

    .chat-room-input {
        padding: 0;
        resize: none;
        border: none;
        box-shadow: none;
    }

    .chat-room-send-btn-wrapper {
        position: absolute;
        right: 12px;
        bottom: 12px;

        .ant-btn {
            box-shadow: none;
        }
    }
}