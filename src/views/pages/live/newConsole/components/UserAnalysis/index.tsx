/**
 * @Owners lzy
 * @Title 用户列表
 */
import React, { useEffect, useState } from 'react';

import Top50 from './components/Top50';
import UserData from './components/UserData';
import style from './index.module.scss';

type Props = {
    isLiveEnd?: boolean;
    liveId: number;
};

const App: React.FC<Props> = ({ liveId }) => {
    const [_activeKey, _setActiveKey] = useState('1');
    useEffect(() => {
        // console.log('isLiveEnd, liveId:', isLiveEnd, liveId);
    }, []);

    return (
        <div className={style.container}>
            <UserData liveId={liveId} />
            <Top50 liveId={liveId} />
        </div>
    );
};
export default App;
