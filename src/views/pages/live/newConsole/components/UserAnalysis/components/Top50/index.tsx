/**
 * @Owners lzy
 * @Title 用户列表
 */
import { type Console } from '@/api/interface/live/console';
import { getBuy, getInteraction, getInvite, getTime } from '@/api/modules/live/console';
import rank1 from '@/assets/rank-icon/1.png';
import rank2 from '@/assets/rank-icon/2.png';
import rank3 from '@/assets/rank-icon/3.png';
import rankOther from '@/assets/rank-icon/other.png';
import { formatDuration, uNumber } from '@/utils/uNumber';
import { Avatar, Table, Tabs, type TabsProps } from 'antd';
import { type ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

import { LevelBadge, Role } from '../../../LevelBadge';

import style from './index.module.scss';
type Props = {
    isLiveEnd?: boolean;
    liveId: number;
};

export enum TabsEnum {
    /** 邀请榜TOP20 */
    Invite = '1',
    /** 互动榜TOP20 */
    Interaction = '2',
    /** 时长榜TOP20 */
    Time = '3',
    /** 购买榜TOP20 */
    Buy = '4',
}

const items: TabsProps['items'] = [
    {
        key: TabsEnum.Invite,
        label: '邀请榜TOP20',
    },
    {
        key: TabsEnum.Interaction,
        label: '互动榜TOP20',
    },
    {
        key: TabsEnum.Time,
        label: '时长榜TOP20',
    },
    // {
    //     key: TabsEnum.Buy,
    //     label: '购买榜TOP20',
    // },
];

const RankItem = ({ index }: { index: number }) => {
    const rank = index + 1;
    return (
        <div
            className={style.rankItem}
            style={{
                backgroundImage: `url(${rank === 1 ? rank1 : rank === 2 ? rank2 : rank === 3 ? rank3 : rankOther})`,
            }}
        >
            {rank > 3 ? <span>{rank}</span> : null}
        </div>
    );
};

const UserAvatar = ({ recor }: { recor: Console.Response.LiveUserInviteTopDataDTO }) => (
    <div className={style.user}>
        <Avatar src={recor.avatar} className={style.avatar} />
        <span className={style.name}>{recor.nickname}</span>
        <LevelBadge
            icon={recor.levelIcon}
            level={recor.level}
            theme={Number(recor.role) === Role.Vip ? 'gold' : 'green'}
            className='tag'
        />
    </div>
);

const columns = {
    [TabsEnum.Invite]: [
        {
            title: '排序',
            dataIndex: 'index',
            key: 'index',
            width: '65px',
            render: (text: string, record: unknown, index: number) => <RankItem index={index} />,
        },
        {
            title: '用户',
            // minWidth: '216px',
            dataIndex: 'user',
            key: 'user',

            render: (text, recor) => <UserAvatar recor={recor} />,
        },
        {
            title: '邀请人数',
            dataIndex: 'inviteUsers',
            key: 'inviteUsers',
            minWidth: '84px',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
    ] as ColumnsType<Console.Response.LiveUserInviteTopDataDTO>,
    [TabsEnum.Interaction]: [
        {
            title: '排序',
            dataIndex: 'index',
            key: 'index',
            width: '65px',
            render: (text: string, record: unknown, index: number) => <RankItem index={index} />,
        },
        {
            title: '用户',
            // minWidth: '216px',
            dataIndex: 'user',
            key: 'user',
            render: (text, recor) => <UserAvatar recor={recor} />,
        },
        {
            title: '关注',
            dataIndex: 'followValue',
            key: 'followValue',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
        {
            title: '评论',
            dataIndex: 'commentValue',
            key: 'commentValue',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
        {
            title: '点赞',
            dataIndex: 'likeValue',
            key: 'likeValue',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
        {
            title: '分享',
            dataIndex: 'shareValue',
            key: 'shareValue',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
        {
            title: '互动',
            dataIndex: 'interactValue',
            key: 'interactValue',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
    ] as ColumnsType<Console.Response.LiveUserCommentTopDataDTO>,
    [TabsEnum.Time]: [
        {
            title: '排序',
            dataIndex: 'index',
            key: 'index',
            width: '65px',
            render: (text: string, record: unknown, index: number) => <RankItem index={index} />,
        },
        {
            title: '用户',
            // minWidth: '216px',
            dataIndex: 'watchTimes',
            key: 'watchTimes',
            render: (text, recor) => <UserAvatar recor={recor} />,
        },
        {
            title: '在线时长',
            dataIndex: 'watchTimes',
            key: 'watchTimes',
            render: text => <span>{formatDuration(text)}</span>,
        },
    ] as ColumnsType<Console.Response.LiveUserTimeDataDTO>,
    [TabsEnum.Buy]: [
        {
            title: '排序',
            dataIndex: 'index',
            key: 'index',
            width: '65px',
            render: (text: string, record: unknown, index: number) => <RankItem index={index} />,
        },
        {
            title: '用户',
            // minWidth: '216px',
            dataIndex: 'user',
            key: 'user',
            render: (text, recor) => <UserAvatar recor={recor} />,
        },
        {
            title: '购买金额',
            dataIndex: 'totalGmv',
            key: 'totalGmv',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
        {
            title: '下单次数',
            dataIndex: 'buyNums',
            key: 'buyNums',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
        {
            title: '订单数',
            dataIndex: 'totalOrders',
            key: 'totalOrders',
            render: text => <span>{uNumber.formattToThousands(text || 0)}</span>,
        },
    ] as ColumnsType<Console.Response.LiveUserBuyTopDataDTO>,
};

const App: React.FC<Props> = ({ liveId }) => {
    const [activeKey, setActiveKey] = useState(TabsEnum.Invite);
    const [goodsList, setGoodsList] = useState<Console.Response.LiveUserInviteTopDataDTO[]>([]);
    useEffect(() => {
        if (activeKey === TabsEnum.Invite) {
            getInvite({
                liveId,
                topN: 20,
            }).then(res => {
                setGoodsList(
                    res.data?.length
                        ? res.data
                        : [
                              //   {
                              //       liveId: 18,
                              //       userId: 10,
                              //       nickname: '丁敏',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       inviteUsers: 40,
                              //       level: 9,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 63,
                              //   },
                              //   {
                              //       liveId: 87,
                              //       userId: 68,
                              //       nickname: '程刚',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       inviteUsers: 52,
                              //       level: 17,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 60,
                              //   },
                              //   {
                              //       liveId: 67,
                              //       userId: 14,
                              //       nickname: '周涛',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       inviteUsers: 57,
                              //       level: 63,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 89,
                              //   },
                              //   {
                              //       liveId: 68,
                              //       userId: 142,
                              //       nickname: '周涛',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       inviteUsers: 57,
                              //       level: 64,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 89,
                              //   },
                          ]
                );
            });
        } else if (activeKey === TabsEnum.Interaction) {
            getInteraction({
                liveId,
                topN: 20,
            }).then(res => {
                setGoodsList(
                    res.data?.length
                        ? res.data
                        : [
                              //   {
                              //       liveId: 48,
                              //       userId: 38,
                              //       nickname: '钱秀兰',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       interactValue: 100,
                              //       followValue: 64,
                              //       commentValue: 54,
                              //       likeValue: 78,
                              //       shareValue: 29,
                              //       level: 68,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 82,
                              //   },
                              //   {
                              //       liveId: 71,
                              //       userId: 27,
                              //       nickname: '吕静',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       interactValue: 85,
                              //       followValue: 34,
                              //       commentValue: 50,
                              //       likeValue: 91,
                              //       shareValue: 71,
                              //       level: 42,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 11,
                              //   },
                          ]
                );
            });
        } else if (activeKey === TabsEnum.Time) {
            getTime({
                liveId,
                topN: 20,
            }).then(res => {
                setGoodsList(
                    res.data?.length
                        ? res.data
                        : [
                              //   {
                              //       liveId: 21,
                              //       userId: 60,
                              //       nickname: '何秀兰',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       watchTimes: 1526557781005,
                              //       level: 73,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 43,
                              //   },
                              //   {
                              //       liveId: 19,
                              //       userId: 19,
                              //       nickname: '郝霞',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       watchTimes: 1469594618870,
                              //       level: 71,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 70,
                              //   },
                              //   {
                              //       liveId: 9,
                              //       userId: 95,
                              //       nickname: '彭平',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       watchTimes: 248817506957,
                              //       level: 77,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 8,
                              //   },
                          ]
                );
            });
        } else if (activeKey === TabsEnum.Buy) {
            getBuy({
                liveId,
                topN: 20,
            }).then(res => {
                setGoodsList(
                    res.data?.length
                        ? res.data
                        : [
                              //   {
                              //       liveId: 20,
                              //       userId: 78,
                              //       liveType: 59,
                              //       nickname: '刘杰',
                              //       avatar: 'http://dummyimage.com/100x100',
                              //       totalGmv: 15,
                              //       buyNums: 20,
                              //       totalOrders: 69,
                              //       level: 16,
                              //       levelIcon: 'http://dummyimage.com/100x100',
                              //       role: 69,
                              //   },
                          ]
                );
            });
        }
    }, [activeKey]);

    const onChange = (key: string) => {
        setActiveKey(key as TabsEnum);
    };

    return (
        <div className={style.container}>
            <Tabs className={style.tab} defaultActiveKey={activeKey} items={items} onChange={onChange} />

            <Table
                // rowKey={'nickname'}
                pagination={false}
                dataSource={goodsList}
                columns={columns[activeKey]}
                className={style.tables}
                scroll={{ x: 300, y: 282 }}
                bordered={false}
                tableLayout={'auto'}
            />
        </div>
    );
};
export default App;
