.container {
    flex: 2;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.1);
    border-radius: 6px 6px 6px 6px;
    .tab {
        width: 100%;
    }
    .tables {
        padding: 24px;
        padding-bottom: 0px;
        flex: 1;
        overflow: auto;

        :global {
            .ant-table-tbody > tr > td {
                border-bottom: none !important; /* 去掉每行的下边框 */
            }
        }
    }
}

.user {
    display: flex;
    align-items: center;
    .avatar {
        flex: 0 0 32px;
        width: 32px;
        height: 32px;
    }
    .name {
        margin-left: 8px;
        margin-right: 4px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 18px;
    }
}
.rankItem {
    display: inline-block;
    width: 22px;
    height: 23px;
    display: flex;
    color: #666666;
    font-size: 12px;
    line-height: 23px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
}
