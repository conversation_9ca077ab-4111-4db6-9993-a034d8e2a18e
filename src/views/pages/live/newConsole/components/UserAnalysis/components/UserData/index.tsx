/**
 * @Owners lzy
 * @Title 用户列表
 */
import { type Console } from '@/api/interface/live/console';
import { userPortraitsData } from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import { Empty, Tabs, type TabsProps } from 'antd';
import * as echarts from 'echarts';
import React, { useEffect, useRef, useState } from 'react';

import style from './index.module.scss';
type Props = {
    isLiveEnd?: boolean;
    liveId: number;
};

export enum TabsEnum {
    /** 成交画像 */
    Invite = 'order',
    /** 用户画像 */
    Interaction = 'view',
}

const items: TabsProps['items'] = [
    {
        key: TabsEnum.Invite,
        label: '成交用户',
    },
    {
        key: TabsEnum.Interaction,
        label: '浏览用户',
    },
];

const items2: TabsProps['items'] = [
    {
        key: TabsEnum.Invite,
        label: '城市成交用户',
    },
    {
        key: TabsEnum.Interaction,
        label: '城市浏览用户',
    },
];
// 配置项
const colors = ['#FFD321', '#FF8F16', '#52C41A', '#4096FF', '#FF7875'];

const App: React.FC<Props> = ({ liveId }) => {
    const [activeKey, setActiveKey] = useState(TabsEnum.Invite);
    const [activeKey2, setActiveKey2] = useState(TabsEnum.Invite);
    const [data, setData] = useState<Console.Response.LiveUserPortraitsRespVO>();
    const [data2, setData2] = useState<Console.Response.LiveUserPortraitsRespVO>();
    const myChartRef = useRef<echarts.EChartsType | null>(null);
    const myChartRef2 = useRef<echarts.EChartsType | null>(null);

    useEffect(() => {
        handlegetUserPortraitsData(TabsEnum.Invite, 0);
    }, []);

    const handlegetUserPortraitsData = (tabType: Console.Params.UserPortraitsDataRequest['tabType'], type: 0 | 1 | 2) => {
        userPortraitsData({
            liveId,
            tabType,
        }).then(_res => {
            const newData = _res.data;
            if (type === 0) {
                setData(newData);
                setData2(newData);
            } else if (type === 1) {
                setData(newData);
            } else if (type === 2) {
                setData2(newData);
            }
        });
    };

    const handleSetEchart = () => {
        if (!data) return;
        if (!myChartRef.current) {
            myChartRef.current = echarts.init(document.getElementById('echatOrder'));
        }
        const categories = () => {
            if (activeKey === TabsEnum.Invite) {
                return ['新用户', '老用户', '新会员', '老会员'];
            }
            return ['新用户', '老用户', '新会员', '老会员', '游客'];
        };
        const categoriesNames = categories();

        // 处理 newUsers
        const newUsers =
            activeKey === TabsEnum.Interaction
                ? data?.livePersonaUserTypeDataVO?.newUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.liveType === 1
                ? data?.livePersonaUserOrderTypeDataVO?.fullNewUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.pureNewUsersRatio;

        // 处理 oldUsers
        const oldUsers =
            activeKey === TabsEnum.Interaction
                ? data?.livePersonaUserTypeDataVO?.oldUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.liveType === 1
                ? data?.livePersonaUserOrderTypeDataVO?.fullOldUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.pureOldUsersRatio;

        // 处理 newDerUsers
        const newDerUsers =
            activeKey === TabsEnum.Interaction
                ? data?.livePersonaUserTypeDataVO?.newDerUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.liveType === 1
                ? data?.livePersonaUserOrderTypeDataVO?.fullNewDerUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.pureNewDerUsersRatio;

        // 处理 oldDerUsers
        const oldDerUsers =
            activeKey === TabsEnum.Interaction
                ? data?.livePersonaUserTypeDataVO?.oldDerUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.liveType === 1
                ? data?.livePersonaUserOrderTypeDataVO?.fullOldDerUsersRatio
                : data?.livePersonaUserOrderTypeDataVO?.pureOldDerUsersRatio;

        // 处理 visitors
        const visitors = data?.livePersonaUserTypeDataVO?.visitorsRatio;

        const chartData = [
            Math.floor((newUsers || 0) * 10000) / 100,
            Math.floor((oldUsers || 0) * 10000) / 100,
            Math.floor((newDerUsers || 0) * 10000) / 100,
            Math.floor((oldDerUsers || 0) * 10000) / 100,
            ...(activeKey === TabsEnum.Interaction ? [Math.floor((visitors || 0) * 10000) / 100] : []),
        ];

        const option = {
            // backgroundColor: 'transparent', // 背景颜色
            title: {
                text: '',
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: { type: 'shadow' },
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                borderWidth: 0,
                textStyle: {
                    color: '#fff',
                    fontSize: 24,
                    lineHeight: 48,
                },
                formatter: (
                    params: [
                        {
                            axisValue: unknown;
                            value: unknown;
                        }
                    ]
                ) => {
                    const [i] = params;
                    const str = `<span style="display:inline-block;margin-right:4px;border-radius:7px;width:7px;height:7px;background-color:${
                        colors[i.dataIndex]
                    };">
                  </span><span style="margin-left:4px;color:#fff;font-size: 12px;">${i.axisValue}：${i.value}%<span/>`;
                    return str;
                },
            },
            legend: {
                data: categoriesNames,
                top: '10px',
                right: '10px',
                icon: 'circle',
                itemGap: 48,
                textStyle: {
                    color: 'rgba(255, 255, 255, 0.70)',
                    fontSize: 28,
                },
                selectedMode: false,
            },
            grid: {
                left: '24',
                right: '24',
                bottom: '24',
                top: '24',
                containLabel: true, // 确保内容不被裁剪
            },
            xAxis: {
                type: 'category',
                data: categoriesNames,
                axisLine: { lineStyle: { color: '#819bb0' } }, // 轴线颜色
                axisLabel: { color: '#333333', fontSize: '14px', margin: 16 }, // 标签颜色
                nameTextStyle: {
                    color: 'rgba(255, 255, 255, 0.50)', // 设置标题颜色
                    fontSize: 14, // 设置字体大小
                },
            },
            yAxis: {
                type: 'value',
                max: 100,
                axisLine: { lineStyle: { color: '#819bb0' } }, // 轴线颜色
                axisLabel: {
                    formatter: '{value}%',
                    color: '#333333',
                    fontSize: '14px',
                },
                nameTextStyle: {
                    color: 'rgba(255, 255, 255, 0.50)', // 设置标题颜色
                    fontSize: 28, // 设置字体大小
                },
                splitLine: { lineStyle: { color: '#E6E6E6', type: 'dashed' } }, // 网格线颜色
            },
            series: [
                // {
                //     name: categoriesNames[0], // 新用户
                //     type: 'bar',
                //     barWidth: 64,
                //     data: [chartData[0]],
                //     itemStyle: {
                //         color: colors[0],
                //     },
                //     label: {
                //         show: true,
                //         position: 'top',
                //         color: '#fff',
                //         fontSize: 28,
                //         formatter: '{c}%',
                //     },
                // },
                // {
                //     name: categoriesNames[1], // 老用户
                //     type: 'bar',
                //     barWidth: 64,
                //     data: [chartData[1]],
                //     itemStyle: {
                //         color: colors[1],
                //     },
                //     label: {
                //         show: true,
                //         position: 'top',
                //         color: '#fff',
                //         fontSize: 28,
                //         formatter: '{c}%',
                //     },
                // },
                // {
                //     name: categoriesNames[2], // 新会员
                //     type: 'bar',
                //     barWidth: 64,
                //     data: [chartData[2]],
                //     itemStyle: {
                //         color: colors[2],
                //     },
                //     label: {
                //         show: true,
                //         position: 'top',
                //         color: '#fff',
                //         fontSize: 28,
                //         formatter: '{c}%',
                //     },
                // },
                // {
                //     name: categoriesNames[3], // 老会员
                //     type: 'bar',
                //     barWidth: 64,
                //     data: [chartData[3]],
                //     itemStyle: {
                //         color: colors[3],
                //     },
                //     label: {
                //         show: true,
                //         position: 'top',
                //         color: '#fff',
                //         fontSize: 28,
                //         formatter: '{c}%',
                //     },
                // },
                // ...(activeKey === TabsEnum.Interaction
                //     ? [
                //           {
                //               name: categoriesNames[4], // 游客
                //               type: 'bar',
                //               barWidth: 64,
                //               data: [chartData[4]],
                //               itemStyle: {
                //                   color: colors[4],
                //               },
                //               label: {
                //                   show: true,
                //                   position: 'top',
                //                   color: '#fff',
                //                   fontSize: 28,
                //                   formatter: '{c}%',
                //               },
                //           },
                //       ]
                //     : []),
                {
                    name: '用户占比',
                    type: 'bar',
                    barWidth: 32,
                    data: chartData,
                    // itemStyle: { color: '#4096FF' },
                    itemStyle: {
                        color(params: { dataIndex: number }) {
                            // 根据数据索引返回不同颜色
                            return colors[params.dataIndex];
                        },
                    },

                    label: {
                        show: true, // 显示标签
                        position: 'top', // 标签位置在顶部
                        color: '#333333', // 标签颜色
                        fontSize: 14, // 标签字体大小
                        formatter: '{c}%',
                    },
                },
                // {
                //     name: '老用户',
                //     type: 'bar',
                //     stack: '用户类型',
                //     data: [Math.floor((oldUsers || 0) * 100), 0],
                //     itemStyle: { color: '#FF8F16' },
                // },
                // {
                //     name: '新用户',
                //     type: 'bar',
                //     stack: '用户类型',
                //     data: [Math.floor((newUsers || 0) * 100), 0],
                //     barWidth: 64,
                //     itemStyle: { color: '#FFD321' },
                // },
                // {
                //     name: '老会员',
                //     type: 'bar',
                //     stack: '用户类型',
                //     data: [0, Math.floor((oldDerUsers || 0) * 100)],
                //     itemStyle: { color: '#4096FF' },
                // },
                // {
                //     name: '新会员',
                //     type: 'bar',
                //     stack: '用户类型',
                //     data: [0, Math.floor((newDerUsers || 0) * 100)],
                //     itemStyle: { color: '#52C41A' },
                // },
            ],
        };
        // myChartRef?.current?.clear();
        myChartRef?.current?.setOption(option);
        // 如果容器大小发生变化，手动调整图表大小
        // myChartRef.current?.resize();
    };
    const handleSetEchart2 = () => {
        if (!data2?.livePersonaUserProvinceDataVOList?.length) return;
        if (!myChartRef2.current) {
            myChartRef2.current = echarts.init(document.getElementById('echatView'));
        }
        const xAxisData = (data2?.livePersonaUserProvinceDataVOList || []).map(i => i.province);
        const cityData = (data2?.livePersonaUserProvinceDataVOList || []).map(i => Math.floor((i.userNumsRatio || 0) * 100));
        const option = {
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0, 0, 0, 0.60)',
                borderWidth: 0,
                borderRadius: 12,
                padding: 24,
                textStyle: {
                    color: '#fff',
                    fontSize: 12,
                    lineHeight: 24,
                },
                confine: true,
                axisPointer: {
                    type: 'none',
                },
                // triggerOn: 'none',
                alwaysShowContent: false,
                formatter: (
                    params: [
                        {
                            axisValue: unknown;
                            value: unknown;
                        }
                    ]
                ) => {
                    const [i] = params;
                    const str = `<span style="display:inline-block;margin-right:4px;border-radius:13px;width:7px;height:7px;background-color:#0EC17C;">
                      </span><span style="margin-left:4px;color:#fff;font-size: 12px;">${i.axisValue}：${i.value}%<span/>`;
                    return str;
                },
            },
            legend: {
                // data: ['GMV', '客单价'],
                data: [],
                icon: 'circle',
                bottom: 0,
                itemHeight: 14,
                textStyle: {
                    color: ['#0EC17C', '#F27629'],
                    fontSize: 24,
                    padding: 6,
                },
                itemGap: 12,
            },
            color: ['#0EC17C', '#F27629'],
            grid: {
                containLabel: true,
                top: '24',
                left: xAxisData.length > 0 ? '24' : 66,
                // left: '24',
                right: '24',
                bottom: '24',
            },
            xAxis: {
                type: 'category',
                data: xAxisData,
                axisLine: { lineStyle: { color: '#819bb0' } }, // 轴线颜色
                axisLabel: {
                    color: '#333333',
                    fontSize: '14px',
                    margin: 16,
                }, // 标签颜色
                axisTick: {
                    show: false,
                },
            },
            yAxis: [
                {
                    // name: '城市(TOP10)',
                    type: 'value',
                    max: 100,
                    axisLine: { lineStyle: { color: '#819bb0' } }, // 轴线颜色
                    axisLabel: {
                        formatter: '{value}%',
                        color: '#333333',
                        fontSize: '14px',
                    },
                    nameTextStyle: {
                        color: '#333333', // 设置标题颜色
                        fontSize: 14, // 设置字体大小
                    },
                    splitLine: { lineStyle: { color: '#E6E6E6', type: 'dashed' } }, // 网格线颜色
                },
            ],
            series: [
                {
                    name: '单数',
                    type: 'bar',
                    barWidth: 20,
                    data: cityData,
                    itemStyle: {
                        color: '#52C41A',
                    },
                    label: {
                        show: true, // 显示标签
                        position: 'top', // 标签位置在顶部
                        color: '#333333', // 标签颜色
                        fontSize: 14, // 标签字体大小
                        formatter: '{c}%',
                    },
                },
            ],
        };
        myChartRef2?.current?.setOption(option);
    };

    useEffect(() => {
        handleSetEchart();
    }, [data]);
    useEffect(() => {
        handleSetEchart2();
    }, [data2]);

    const onChange = (key: string) => {
        const newKey = key as Console.Params.UserPortraitsDataRequest['tabType'];
        setActiveKey(newKey as TabsEnum);
        handlegetUserPortraitsData(newKey, 1);
    };
    const onChange2 = (key: string) => {
        const newKey = key as Console.Params.UserPortraitsDataRequest['tabType'];
        setActiveKey2(newKey as TabsEnum);
        handlegetUserPortraitsData(newKey, 2);
    };

    return (
        <>
            <div className={style.container}>
                <Tabs className={style.tab} defaultActiveKey={activeKey} items={items} onChange={onChange} />
                <Empty
                    style={{ marginTop: '160px', display: !data ? 'block' : 'none' }}
                    image={EmptyImg}
                    description='暂无数据'
                />
                <div id='echatOrder' style={{ width: '100%', height: '358px', display: data ? 'block' : 'none' }} />
            </div>
            <div className={style.container}>
                <Tabs
                    className={style.tab}
                    defaultActiveKey={activeKey2}
                    items={items2}
                    onChange={onChange2}
                    tabBarExtraContent={'TOP10'}
                />
                <Empty
                    style={{ marginTop: '160px', display: !data2?.livePersonaUserProvinceDataVOList?.length ? 'block' : 'none' }}
                    image={EmptyImg}
                    description='暂无数据'
                />
                <div
                    id='echatView'
                    style={{
                        width: '100%',
                        height: '358px',
                        display: data2?.livePersonaUserProvinceDataVOList?.length ? 'block' : 'none',
                    }}
                />
            </div>
        </>
    );
};
export default App;
