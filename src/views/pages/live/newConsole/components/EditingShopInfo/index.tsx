/**
 * @Owners lzy
 * @Title 编辑商品信息
 */

import { type Console } from '@/api/interface/live/console';
import { batchEditLiveGoods, getAllGoodsCategory } from '@/api/modules/live/console';
// import touchIcon from '@/assets/images/touchIcon.png';
import { modal } from '@/views/components/UseAppPrompt';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Button, Drawer, Form, Input, Select, message } from 'antd';
import { useEffect, useState } from 'react';

import { type GoodsItem } from '../Shop/LiveGoodsList';

import style from './index.module.scss';

const initData = {
    categoryName: '全部',
    id: 0,
    sort: 0,
};
type LiveGoodsCategory = Console.Response.LiveGoodsCategoryVO;

type _Props = {
    open: boolean;
    item: GoodsItem;
    liveId: number;
    setOpen(): void;
    onDone(): void;
};

const Index: React.FC<_Props> = ({ open, setOpen, onDone, item, liveId }) => {
    const [loading, setLoading] = useState(false);
    const [goodsCategory, setGoodsCategory] = useState<LiveGoodsCategory[]>([initData]);
    const [customerCategoryId, setCustomerCategoryId] = useState<number>();
    const [sellingPoint, setSellingPoint] = useState<string>();
    const [customSpuName, setCustomSpuName] = useState<string>();

    useEffect(() => {
        handleReset();
        handleGetAllGoodsCategory();
    }, [item]);

    const handleGetAllGoodsCategory = () => {
        getAllGoodsCategory({ liveId }).then(res => {
            setGoodsCategory([initData, ...(res.data || [])]);
            setLoading(false);
        });
    };

    const handleReset = () => {
        setLoading(false);
        setCustomerCategoryId(item.customerCategoryId);
        setCustomSpuName(item.customSpuName);
        setSellingPoint(item.sellingPoint);
    };

    const handleOnClose = () => {
        if (
            item.customerCategoryId !== customerCategoryId ||
            item.customSpuName !== customSpuName ||
            item.sellingPoint !== sellingPoint
        ) {
            return modal.confirm({
                // title: '是否保存当前话术？',
                icon: <ExclamationCircleFilled />,
                content: '确定退出编辑？退出后已修改的内容将不保存',
                onOk() {
                    // console.log('OK');
                    // handleSubmit();
                    setOpen();
                },
                onCancel() {},
                okText: '确认',
                cancelText: '取消',
            });
        }
        setOpen();
    };

    const handleOnchange = (value: number) => {
        setCustomerCategoryId(value);
    };

    const handleSubmit = () => {
        if (!customerCategoryId && customerCategoryId !== 0) {
            return message.error('商品类型不能为空');
        }
        setLoading(true);
        const spuId = item.spuId;
        batchEditLiveGoods({
            liveGoods: [
                {
                    customSpuName,
                    sellingPoint,
                    customerCategoryId,
                    liveId,
                    spuId,
                },
            ],
            type: 1,
        })
            .then(() => {
                setOpen();
                onDone();
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <Drawer title='编辑商品信息' onClose={handleOnClose} open={open} width={900} className={`${style.container}`}>
            <div className={style.goodsInfo}>
                <img src={item.mainUrl || ''} className={style.img} />
                <span>{item.spuName}</span>
            </div>
            <Form labelCol={{ span: 4 }} wrapperCol={{ span: 14 }} layout='horizontal' className={style.main}>
                <Form.Item label='短标题'>
                    <Input.TextArea
                        onChange={e => {
                            setCustomSpuName(e.target.value);
                        }}
                        value={customSpuName}
                        maxLength={50}
                        showCount
                    />
                </Form.Item>

                <Form.Item label='卖点'>
                    <Input.TextArea
                        onChange={e => {
                            setSellingPoint(e.target.value);
                        }}
                        value={sellingPoint}
                        maxLength={50}
                        showCount
                    />
                </Form.Item>

                <Form.Item label='商品类型' required>
                    <Select value={customerCategoryId} onChange={handleOnchange}>
                        {goodsCategory.map(item => (
                            <Select.Option value={item.id} key={item.id}>
                                {item.categoryName}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            <div className={style.flotter}>
                <Button loading={loading} color='default' variant='outlined' disabled={loading} onClick={handleOnClose}>
                    取消
                </Button>
                <Button
                    onClick={handleSubmit}
                    loading={loading}
                    type='primary'
                    disabled={loading}
                    // icon={<img src={touchIcon} className={style.img} />}
                >
                    确认
                </Button>
            </div>
        </Drawer>
    );
};

export default Index;
