.goods-analysis-module {
    .goods-analysis-content {
        padding-bottom: 24px;
    }

    .goods-analysis-good-item {
        padding: 24px;
        margin-bottom: 16px;
        background-color: #ffffff;
    }

    .goods-analysis-table {
        .good-sort-box {
            position: relative;

            .sort-icon {
                width: 20px;
                height: 23px;
            }

            .sortNumber {
                color: #333333;
                font-weight: 400;
                font-size: 12px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                margin-top: -2px;
            }
        }

        .good-name-box {
            display: flex;
        }

        .good-spuName {
            margin-left: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            /* 限制文本为2行 */
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
        }
    }

    .good-comment-box {
        padding: 24px 0 16px 0;

        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 24px 12px;
        grid-auto-flow: row dense;

        &.one {
            grid-template-columns: 1fr;
        }

        &.two {
            grid-template-columns: repeat(2, 1fr);
        }

        .good-comment-item {
            display: flex;
            flex-direction: column;
            overflow: auto;

            .good-AI-comment-box {
                display: flex;
            }

            .level-item {
                padding: 0 3px;
                height: 18px;
                color: #fa2b19;
                background: #fff1f0;
                font-size: 12px;
                margin-right: 4px;
            }

            .height-level {
                background: #fff1f0;
                color: #fa2b19;
            }

            .middle-level {
                background: #fff7e6;
                color: #ff8f16;
            }

            .low-level {
                background: #ebfaf6;
                color: #00b68f;
            }

            .good-comment-title {
                font-weight: 600;
                font-size: 14px;
                color: #333333;
                margin-right: 4px;
            }

            .good-comment-content {
                flex: 1;
                white-space: wrap;
                color: #666666;
            }

            .good-user-comment-box {
                margin-top: 10px;
                width: 100%;

                .good-user-comment-item {
                    margin-top: 12px;
                    padding: 8px 12px;
                    background: rgba(247, 248, 249, 0.6);
                    border-radius: 6px 6px 6px 6px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #999999;
                    display: flex;
                    flex-wrap: nowrap;

                    .good-user-comment-text {
                        flex: 1;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
    }

    .summarizing-box {
        background-color: #ffffff;
        padding: 24px;
        min-height: 569px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .ai-summarizing-icon {
            width: 110px;
            height: 110px;
            margin-bottom: 12px;
        }

        .ai-summarizing-text {
            font-weight: 400;
            font-size: 14px;
            color: #333333;
        }
    }
}