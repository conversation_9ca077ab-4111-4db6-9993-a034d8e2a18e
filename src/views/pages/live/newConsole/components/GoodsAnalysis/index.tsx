/**
 * @Owners mzh
 * @Title 商品分析
 */
import { type Console } from '@/api/interface/live/console';
import { getGoodsAnalysisData } from '@/api/modules/live/console';
import GoodsAnalysisAiSummarize from '@/assets/images/goods-analysis-ai-summarize.png';
import goodsAnalysisSort1 from '@/assets/rank-icon/1.png';
import goodsAnalysisSort2 from '@/assets/rank-icon/2.png';
import goodsAnalysisSort3 from '@/assets/rank-icon/3.png';
import goodsAnalysisSort4 from '@/assets/rank-icon/other.png';
import { uNumber } from '@/utils/uNumber';
import CommentSummaryLine from '@/views/components/common/CommentSummaryLine';
import { cLive } from '@consts';
import { Image, Pagination, Row, Table } from 'antd';
import classNames from 'classnames';
import { memo, useEffect, useMemo, useState } from 'react';

import './index.scss';

type DataItem = Console.Response.GoodsAnalysisRes['dataList'][number];

type Props = {
    isEnd?: boolean;
    liveId: number;
    showReward?: boolean;
};

type AlignType = 'center' | 'left' | 'right';

const goodCommentLevelMap: { [key: number]: string } = {
    0: '',
    1: '低',
    2: '中',
    3: '高',
};

const GoodsAnalysis = memo((props: Props) => {
    const { liveId } = props;

    const [dataSource, setDataSource] = useState<DataItem[]>([]);

    const [total, setTotal] = useState<number>(0);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(20);

    useEffect(() => {
        getGoodsAnalysisDataFromApi();
    }, [currentPage, pageSize]);

    const getGoodsAnalysisDataFromApi = async () => {
        const params = {
            liveId,
            rows: pageSize,
            page: currentPage,
        };
        const res = await getGoodsAnalysisData(params);
        res.data?.dataList?.forEach((dl, index) => (dl.sortIndex = index + 1));
        setDataSource(res.data?.dataList || []);
        setTotal(res.data?.total || 0);
    };

    const goodColumns = useMemo(
        () => [
            {
                title: '排名',
                width: 80,
                align: 'center' as AlignType,
                render: (_: unknown, record: DataItem) => {
                    const sortNumber = pageSize * (currentPage - 1) + record?.sortIndex;
                    if (sortNumber === 1) {
                        return (
                            <div className='good-sort-box'>
                                <img className='sort-icon' src={goodsAnalysisSort1} alt='' />
                            </div>
                        );
                    } else if (sortNumber === 2) {
                        return (
                            <div className='good-sort-box'>
                                <img className='sort-icon' src={goodsAnalysisSort2} alt='' />
                            </div>
                        );
                    } else if (sortNumber === 3) {
                        return (
                            <div className='good-sort-box'>
                                <img className='sort-icon' src={goodsAnalysisSort3} alt='' />
                            </div>
                        );
                    }

                    return (
                        <div className='good-sort-box'>
                            <img className='sort-icon' src={goodsAnalysisSort4} alt='' />
                            <span className='sortNumber'>{sortNumber}</span>
                        </div>
                    );
                },
            },
            {
                title: '商品名称',
                dataIndex: 'spuName',
                render: (_: string, record: DataItem) => (
                    <div className='good-name-box'>
                        <Image src={record?.spuPic} style={{ width: '48px', height: '48px' }} />
                        <div className='good-spuName'>{record?.spuName}</div>
                    </div>
                ),
            },
            {
                title: '价格',
                dataIndex: 'price',
                width: 120,
                align: 'center' as AlignType,
                render: (val: number) => `¥ ${uNumber.centToYuan(val)}`,
            },
            {
                title: '成交人数',
                dataIndex: 'fullOrderUsers',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record?.fullOrderUsers : record.pureOrderUsers,
            },
            {
                title: '成交件数',
                dataIndex: 'fullOrderSkuNumbers',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record.fullOrderSkuNumbers : record.pureOrderSkuNumbers,
            },
            {
                title: '成交金额',
                dataIndex: 'fullOrderTotalGmv',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record.fullOrderTotalGmv : record.pureOrderTotalGmv,
            },
            {
                title: '首单用户数',
                dataIndex: 'fullFirstOrderUsers',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record.fullFirstOrderUsers : record.pureFirstOrderUsers,
            },
            {
                title: '复购用户数',
                dataIndex: 'fullRepayOrderUsers',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record.fullRepayOrderUsers : record.pureRepayOrderUsers,
            },
            {
                title: '库存数',
                dataIndex: 'numberOfInventories',
                width: 120,
                align: 'center' as AlignType,
            },
            {
                title: '浏览人数',
                dataIndex: 'pureViewUsers',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record.fullViewUsers : record.pureViewUsers,
            },
            {
                title: '加购人数',
                dataIndex: 'fullAddcarUsers',
                width: 120,
                align: 'center' as AlignType,
                render: (_: number, record: DataItem) =>
                    record?.liveType === cLive.LiveTypeMap.PUBLIC ? record.fullAddcarUsers : record.pureAddcarUsers,
            },
            ...(props.showReward
                ? [
                      {
                          title: '预估直播奖励',
                          dataIndex: 'pureRewardAmt',
                          width: 130,
                          align: 'center' as AlignType,
                          render: (_: number, record: DataItem) => record?.pureRewardAmt,
                      },
                  ]
                : []),
        ],
        []
    );

    const onPaginationChange = (page: number, _pageSize: number) => {
        if (page !== currentPage) {
            setCurrentPage(page);
        }

        if (_pageSize !== pageSize) {
            setPageSize(_pageSize);
        }
    };

    return (
        <div className='goods-analysis-module'>
            {dataSource.length > 0 && (
                <div className='goods-analysis-content'>
                    {dataSource?.map(goodItem =>
                        [goodItem]?.map(good => (
                            <div key={goodItem.spuId} className='goods-analysis-good-item'>
                                {Boolean(dataSource.length) && (
                                    <Table
                                        rowKey={'spuId'}
                                        dataSource={[good]}
                                        className='goods-analysis-table'
                                        columns={goodColumns}
                                        pagination={false}
                                    />
                                )}

                                <div
                                    className={`good-comment-box ${
                                        good?.goodsAnalysisList?.length === 1
                                            ? 'one'
                                            : good?.goodsAnalysisList?.length === 2
                                            ? 'two'
                                            : ''
                                    }`}
                                >
                                    {good?.goodsAnalysisList?.map((gaItem, index) => (
                                        <div key={index} className='good-comment-item'>
                                            <div className='good-AI-comment-box'>
                                                {Boolean(gaItem?.level) && (
                                                    <div
                                                        className={classNames('level-item', {
                                                            'low-level': gaItem.level === 1,
                                                            'middle-level': gaItem.level === 2,
                                                            'height-level': gaItem.level === 3,
                                                        })}
                                                    >
                                                        {goodCommentLevelMap[gaItem?.level]}
                                                    </div>
                                                )}
                                                <div className='good-comment-title'>{gaItem.title}</div>
                                                <div className='good-comment-content'>{gaItem.content}</div>
                                            </div>

                                            <div className='good-user-comment-box'>
                                                <CommentSummaryLine comments={gaItem?.commentList || []} key={index} />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))
                    )}

                    {dataSource?.length > 0 && (
                        <Row justify={'center'}>
                            <Pagination
                                showSizeChanger
                                pageSize={pageSize}
                                current={currentPage}
                                total={total}
                                onChange={onPaginationChange}
                                showTotal={total => `共${total}条`}
                            />
                        </Row>
                    )}
                </div>
            )}
            {dataSource.length === 0 && (
                <div className='summarizing-box'>
                    <img className='ai-summarizing-icon' src={GoodsAnalysisAiSummarize} alt='' />
                    <div className='ai-summarizing-text'>AI总结中，直播结束后会生成分析</div>
                </div>
            )}
        </div>
    );
});

export default GoodsAnalysis;
