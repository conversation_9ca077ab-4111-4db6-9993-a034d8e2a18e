/**
 * @Owners mzh
 * @Title 中控台-直播间信息
 */
import { type Console } from '@/api/interface/live/console';
import { PushStatusMap } from '@/consts/cLive';
import { useSelector } from '@/redux';
import { cLive } from '@consts';
import { Image, Tag } from 'antd';
import React, { memo } from 'react';

import './index.scss';

type Props = {
    liveDetail?: Console.Response.LiveDetailInfo;
};

const HeadderLiveInfo: React.FC<Props> = memo(({ liveDetail }) => {
    const { userInfo } = useSelector(state => state.global);

    return (
        <div className='head-live-info'>
            <Image className='live-coverUrl' src={liveDetail?.coverUrl} alt='' />
            <div className='live-info'>
                <div className='live-name-box'>
                    <span className='live-name'>{liveDetail?.liveName}</span>
                    <Tag color={cLive.PushStatusTextMap[liveDetail?.pushStatus || 1].color}>
                        {liveDetail?.pushStatus === PushStatusMap.Pushing ? (
                            <img
                                src='https://applet.ifengqun.com/fq-mall/prod/discover/discover-loading.gif'
                                style={{
                                    width: '12px',
                                    height: '12px',
                                    marginRight: '4px',
                                }}
                                alt=''
                            />
                        ) : null}
                        {cLive.PushStatusTextMap[liveDetail?.pushStatus || 1].text}
                    </Tag>
                </div>
                <div className='live-theme-box'>
                    <img className='live-user-avatar' src={liveDetail?.userInfo?.avatar} alt='' />
                    <span>{userInfo?.nickname}</span>
                    <div className='live-level-box'>
                        <img className='live-level-icon' src={liveDetail?.userInfo?.levelIcon} alt='' />
                        <span className='live-level'>{liveDetail?.userInfo?.level}</span>
                    </div>
                </div>
                <div className='live-time-box'>
                    <div className='live-time'>
                        直播时间：
                        {`${liveDetail?.actualStartTime || ''} ${
                            liveDetail?.actualEndTime ? `- ${liveDetail?.actualEndTime}` : ''
                        }`}
                    </div>
                    {liveDetail?.pushStatus === cLive.PushStatusMap.NoStart && (
                        <div>直播时长：{liveDetail?.liveSummary?.totalLiveTimeStr || ''}</div>
                    )}
                </div>
            </div>
        </div>
    );
});

export default HeadderLiveInfo;
