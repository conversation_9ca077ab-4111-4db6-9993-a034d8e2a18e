/**
 * @Owners lzy
 * @Title 编辑话术
 */
import { getLiveGoodsPromotionWords, liveEpigraphTextQuery, liveEpigraphTextUpdate } from '@/api/modules/live/console';
import AI_Icon2 from '@/assets/images/AI_Icon2.png';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import dsIcon from '@/assets/images/icon-ds-5.png';
import refreshIcon from '@/assets/images/refreshIcon.png';
import refreshIconActive from '@/assets/images/refreshIconActive.png';
import touchIcon from '@/assets/images/touchIcon.png';
import { message, modal } from '@/views/components/UseAppPrompt';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Button, Drawer, Empty, Input, Tabs, type TabsProps } from 'antd';
import { type SetStateAction, useEffect, useState } from 'react';

import { type GoodsItem } from '../Shop/LiveGoodsList';

import style from './index.module.scss';

const { TextArea } = Input;

type _Props = {
    open: boolean;
    item: GoodsItem;
    scriptTabType?: ScriptTabsEnum;
    setOpen(): void;
};

export enum ScriptTabsEnum {
    /** 商品介绍 */
    Invite = '1',
    /** 案例分析 */
    Interaction = '2',
}
let cancelRequest: () => null | void;
const Index: React.FC<_Props> = ({ open, setOpen, item, scriptTabType = ScriptTabsEnum.Invite }) => {
    const [hover, setHover] = useState(false);
    const [isUnSaved, setIsUnSaved] = useState(false);
    const [loading, setLoading] = useState(false);
    const [activeKey, setActiveKey] = useState<ScriptTabsEnum>(ScriptTabsEnum.Invite);
    const [editText, setEditText] = useState(false);
    const [epigraphText, setEpigraphText] = useState('');
    const [ininLastData, setLastIninData] = useState('');

    const items: TabsProps['items'] = [
        {
            key: ScriptTabsEnum.Invite,
            label: '商品介绍',
            disabled: loading ? true : false,
        },
        {
            key: ScriptTabsEnum.Interaction,
            label: '案例分析',
            disabled: loading ? true : false,
        },
    ];

    useEffect(() => {
        setActiveKey(scriptTabType ? scriptTabType : ScriptTabsEnum.Invite);
    }, [scriptTabType]);

    useEffect(() => {
        handleReset();
        const newKey = scriptTabType;
        handleGetLiveEpigraphTextQuery(Number(newKey));
    }, [item]);

    const handleReset = () => {
        setHover(false);
        setIsUnSaved(false);
        setLoading(false);
        const newKey = scriptTabType;
        setActiveKey(newKey);
        setEpigraphText('');
        setLastIninData('');
    };

    const handleGetLiveEpigraphTextQuery = (type: number) => {
        const { liveId, spuId } = item;
        liveEpigraphTextQuery({
            liveId,
            spuId,
            type,
        }).then(res => {
            setEpigraphText(res.data?.epigraphText || '');
        });
    };

    const onClose = () => {
        if (isUnSaved) {
            setIsUnSaved(false);
            modal.confirm({
                title: '是否保存当前话术？',
                icon: <ExclamationCircleFilled />,
                content: '您未保存所选话术，点击【使用话术】可保存并使用',
                onOk() {
                    // console.log('OK');
                    handleSubmit();
                },
                onCancel() {
                    // console.log('Cancel');
                    setEditText(false);
                    cancelRequest && cancelRequest();
                    setOpen();
                },
                okText: '使用话术',
                cancelText: '暂不保存',
            });
            return;
        }
        setEditText(false);
        cancelRequest && cancelRequest();
        setOpen();
    };

    const onChange = (key: string) => {
        const newKey = key as ScriptTabsEnum;
        setActiveKey(newKey);
        handleGetLiveEpigraphTextQuery(Number(newKey));
    };
    const handltOnMouseEnter = () => {
        setHover(true);
    };
    const handltOnMouseLeave = () => {
        setHover(false);
    };
    const handleOnChange = (e: { target: { value: SetStateAction<string> } }) => {
        // console.log(e.target.value);
        setEpigraphText(e.target.value);
    };

    const handleEditText = () => {
        setLastIninData(epigraphText);
        setEditText(!editText);
        setIsUnSaved(true);
    };
    const handleCancleEditText = () => {
        setEpigraphText(ininLastData);
        setEditText(false);
    };

    const handleChangeText = () => {
        setIsUnSaved(true);
        const { liveId, spuId } = item;
        let newEpigraphText = '';
        setLoading(true);
        cancelRequest = getLiveGoodsPromotionWords(
            {
                liveId,
                spuId,
                type: Number(activeKey),
            },
            data => {
                // console.error('data:', data);
                if (data.action === 'all-down') {
                    setEpigraphText(data.answer);
                    setLoading(false);
                } else if (data.action === null) {
                    newEpigraphText += data.answer;
                    setEpigraphText(newEpigraphText);
                    setLoading(false);
                } else {
                    setLoading(false);
                }
            },
            _err => {
                // showMessage({ message: err.message });
                // isAsking.value = false;
                setLoading(false);
            },
            () => {
                setLoading(false);
            }
        );
    };

    const handleSubmit = () => {
        if (!epigraphText) {
            message.error('请输入话术');
            return;
        }
        setLoading(true);
        const { liveId, spuId } = item;
        liveEpigraphTextUpdate({
            liveId,
            spuId,
            type: Number(activeKey),
            epigraphText,
        })
            .then(() => {
                setLastIninData(epigraphText);
                setEditText(false);
                setIsUnSaved(false);
                setOpen();
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <Drawer title='编辑话术' onClose={onClose} open={open} width={900} className={`${style.container}`}>
            <div className={style.goodsInfo}>
                <img src={item.mainUrl || ''} className={style.img} />
                <span>{item.spuName}</span>
            </div>

            <div className={style.titles}>
                <div className={style.left}>
                    <img src={AI_Icon2} className={style.img} />
                    推荐话术
                </div>

                <Button
                    color='default'
                    variant='link'
                    className={`${style.right} ${hover && style.rightHover}`}
                    onClick={handleChangeText}
                    onMouseEnter={handltOnMouseEnter}
                    onMouseLeave={handltOnMouseLeave}
                    loading={loading}
                    disabled={loading}
                >
                    <img src={hover ? refreshIconActive : refreshIcon} className={style.img2} />
                    换一换
                </Button>
            </div>

            <div className={`${style.main} ${editText && style.active}`}>
                <img className={style.icon} src={dsIcon} alt='' />
                <Tabs className={style.tab} activeKey={activeKey} items={items} onChange={onChange} />

                {editText ? (
                    <TextArea value={epigraphText} placeholder='' className={style.content} onChange={handleOnChange} />
                ) : (
                    <div className={style.content}>
                        {!epigraphText ? (
                            <Empty style={{ marginTop: '160px' }} image={EmptyImg} description='暂无数据' />
                        ) : (
                            epigraphText
                        )}
                    </div>
                )}
                <div className={style.flotter}>
                    <Button
                        loading={loading}
                        color='default'
                        variant='outlined'
                        disabled={loading}
                        onClick={editText ? handleCancleEditText : handleEditText}
                    >
                        {editText ? '取消' : '编辑'}
                    </Button>
                    <Button
                        onClick={handleSubmit}
                        loading={loading}
                        type='primary'
                        disabled={loading}
                        icon={<img src={touchIcon} className={style.img} />}
                    >
                        使用话术
                    </Button>
                </div>
            </div>

            <div className={style.description}>
                您选择使用话术前，需要清晰了解并理解：话术内容仅供参考，请您自行选择使用，平台仅提供技术服务，不对话术内容进行任何保证。您需要自行对话术内容的
                使用行为和后果承担责任，若你使用话术内容出现违反平台协议和规则的情形，平台有权按照平台协议和规则对您进行处置。
            </div>
        </Drawer>
    );
};

export default Index;
