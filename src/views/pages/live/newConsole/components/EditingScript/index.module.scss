.container {
    .goodsInfo {
        background: #f7f8f9;
        border-radius: 6px 6px 6px 6px;
        display: flex;
        padding: 12px;
        margin-bottom: 24px;

        .img {
            width: 65px;
            height: 65px;
            margin-right: 10px;
        }
    }

    .titles {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .img {
            width: 18px;
            height: 18px;
            margin-right: 4px;
        }

        .left {
            display: flex;
            align-items: center;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
        }

        .right {
            display: flex;
            align-items: center;
            text-align: right;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 18px;

            .img2 {
                width: 18px;
                height: 18px;
            }
        }

        .rightHover {
            color: #00b68f;
        }
    }

    .main {
        background: #f7f8f9;
        border-radius: 6px 6px 6px 6px;
        border: 1px solid #eeeeee;
        padding: 12px;
        padding-top: 0px;
        margin-bottom: 12px;
        position: relative;

        .icon {
            position: absolute;
            top: -1px;
            right: -1px;
            width: 53.5px;
        }

        .content {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 14px;
            color: #333333;
            line-height: 22px;
            text-align: left;
            white-space: pre-wrap;
            height: 440px;
            overflow-y: auto;

            margin-bottom: 12px;
        }

        .flotter {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 12px;

            .img {
                width: 16px;
                height: 16px;
            }
        }
    }

    .active {
        background: #ffffff;
    }

    .description {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 18px;
    }

    :global {
        .ant-tabs-tab {
            color: #666;
            font-size: 14px;

            &.ant-tabs-tab-active {
                .ant-tabs-tab-btn {
                    font-weight: 600;
                    color: #333;
                    text-shadow: none;
                }
            }
        }

        .ant-tabs-ink-bar {
            height: 4px !important;
            border-radius: 4px;
            width: 36px !important;
        }

        .ant-input-outlined {
            border: none;
            padding: 0px;
        }
    }
}
