/**
 * @Owners ljh
 * @Title 连麦用户项
 */
import Show from '@/views/components/common/Show';
import { cLive } from '@consts';
import { Avatar, Divider, Tag } from 'antd';
import { type CSSProperties, type ReactNode, useMemo } from 'react';

import { LevelBadge, Role } from '../LevelBadge';

import './index.scss';

export interface InteractiveUserItemProps {
    style?: CSSProperties;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    item?: any;
    /** 观看时间 */
    viewTime?: ReactNode;
    /** 右侧内容 */
    right?: ReactNode;
}

const InteractiveUserItem = (props: InteractiveUserItemProps) => {
    const { style, right, item, viewTime } = props;

    const liveMicHistoryText = useMemo(() => {
        if (item.liveMicHistoryType === cLive.LiveMicHistoryType.Invited) {
            return '刚邀请过';
        }

        if (item.liveMicHistoryType === cLive.LiveMicHistoryType.Rejected) {
            return '刚拒绝过';
        }

        if (item.liveMicHistoryType === cLive.LiveMicHistoryType.ConnectedToday) {
            return '今日连线过';
        }

        return '';
    }, [item?.liveMicHistoryType]);

    const nickname = useMemo(
        () => (item.nickname && item.nickname.length > 10 ? item.nickname.slice(0, 9) + '...' : item.nickname),
        [item.nickname]
    );

    /** 格式化观看时间 */
    // const formatViewTime = useMemo(() => {
    //     const viewTimeInSeconds = item.viewTime;
    //     if (!viewTimeInSeconds || viewTimeInSeconds < 60) {
    //         return '刚刚';
    //     } else if (viewTimeInSeconds < 3600) {
    //         return `${Math.floor(viewTimeInSeconds / 60)}分钟`;
    //     }
    //     const hours = Math.floor(viewTimeInSeconds / 3600);
    //     const minutes = Math.floor((viewTimeInSeconds % 3600) / 60);
    //     return `已观看${hours}小时${minutes}分钟`;
    // }, [item?.viewTime]);

    return (
        <div className='interactive-user-item' style={style}>
            <Avatar className='interactive-user-item-avatar' src={item.avatar} />
            <div className='interactive-user-item-info'>
                <div className='interactive-user-item-info-top'>
                    <span className='interactive-user-item-info-name'>{nickname}</span>
                    <LevelBadge level={item.level} icon={item.levelIcon} theme={item.role === Role.Vip ? 'gold' : 'green'} />
                    <Show when={item.isFollow}>
                        <Tag className='interactive-user-item-info-tag' bordered={false}>
                            粉丝
                        </Tag>
                    </Show>
                </div>
                <div className='interactive-user-item-info-bottom'>
                    <Show when={!!viewTime}>
                        <span>{viewTime}</span>
                    </Show>
                    <Show when={!!viewTime && !!liveMicHistoryText}>
                        <Divider type='vertical' />
                    </Show>
                    <Show when={!!liveMicHistoryText}>
                        <span>{liveMicHistoryText}</span>
                    </Show>
                </div>
            </div>
            {right && <div className='interactive-user-item-right'>{right}</div>}
        </div>
    );
};

export default InteractiveUserItem;
