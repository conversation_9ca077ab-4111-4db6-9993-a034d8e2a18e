.interactive-user-item {
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 12px;
    height: 60px;
    border-radius: 6px;
    transition: background 0.3s;

    &:hover {
        background: linear-gradient(-90deg, #f7f8f9 24.4%, rgba(247, 248, 249, 0) 37.6%);
    }

    .interactive-user-item-avatar {
        flex-shrink: 0;
        margin-right: 6px;
    }

    .interactive-user-item-info-top {
        display: flex;
        align-items: center;
    }

    .interactive-user-item-info-name {
        margin-right: 4px;
        font-size: 14px;
        color: #333;
    }

    .interactive-user-item-info-tag {
        margin-inline-start: 4px;
        padding-inline: 2px;
        font-size: 10px;
        line-height: 12px;
        color: #00bcb0;
        border-radius: 2px;
        background: #e5f8f7;
    }

    .interactive-user-item-info-bottom {
        font-size: 10px;
        color: rgba(0, 0, 0, 0.5);
    }

    .interactive-user-item-right {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
    }
}
