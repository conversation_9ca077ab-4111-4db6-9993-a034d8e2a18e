/**
 * @Owners ljh
 * @Title 直播卡片
 */
import { type Console } from '@/api/interface/live/console';
import { setGlobalMute } from '@/api/modules/live/console';
import { modal } from '@/views/components/UseAppPrompt';
import { cLive } from '@consts';
import TencentCloudChat from '@tencentcloud/chat';
import type { RadioChangeEvent } from 'antd';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { LiveContext } from '../../contexts/LiveContext';
import { PushType, useLivePlayer } from '../../hooks/useLivePlayer';

export enum LivePlayerMode {
    Live = 'live',
    Comment = 'comment',
}

export const useLiveCard = ({ mode = LivePlayerMode.Comment, videoId }: { mode?: LivePlayerMode; videoId: string }) => {
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId'));

    const { liveDetail, getLiveDetail, chatInited, chat, tabActiveKey } = useContext(LiveContext);
    /** 播放器模式 */
    const [playerMode, setPlayerMode] = useState<LivePlayerMode>(mode);
    /** 是否全局禁言 */
    const [isGlobalMute, setIsGlobalMute] = useState(false);
    /** 直播状态 */
    const [liveStatus, setLiveStatus] = useState<cLive.PushStatusMap>();

    const {
        createTCPlayer,
        playerInstance,
        paused,
        errorInfo,
        clearError,
        toggleMuted,
        isMuted,
        handleChangePushStatus,
        isFullscreen,
        togglePlayerFullscreen,
        videoContainerRef,
    } = useLivePlayer(videoId, liveId);

    /** 是否为直播播放器模式 */
    const isLivePlayerMode = useMemo(() => playerMode === LivePlayerMode.Live, [playerMode]);

    /** 是否显示播放器 */
    const isShowPlayer = useMemo(
        () => liveStatus && [cLive.PushStatusMap.Pushing].includes(liveStatus) && isLivePlayerMode,
        [liveStatus, isLivePlayerMode]
    );

    useEffect(() => {
        if (isShowPlayer) {
            if (tabActiveKey === cLive.ConsoleInteractiveTabs.Live && liveDetail) {
                if (!playerInstance.current) {
                    setTimeout(() => {
                        createTCPlayer(liveDetail.playUrl.webRtc);
                    }, 1000);
                } else {
                    // 重新设置后，它会自动重新拉流和自动播放
                    playerInstance.current?.src(liveDetail.playUrl.webRtc);
                }
            } else {
                playerInstance.current?.unload();
            }
        }
    }, [tabActiveKey, liveDetail, isShowPlayer]);

    useEffect(() => {
        // 初始化全局禁言状态
        if (liveDetail?.isMute !== undefined) {
            setIsGlobalMute(!!liveDetail.isMute);
        }
    }, [liveDetail?.isMute]);

    useEffect(() => {
        // 初始化推流状态
        if (liveDetail?.pushStatus !== undefined) {
            setLiveStatus(liveDetail.pushStatus);
        }
    }, [liveDetail?.pushStatus]);

    const handleCustomMessage = useMemo<Record<string, (msg: Console.ChatMessage) => void>>(
        () => ({
            /** 房间状态 */
            [cLive.IMMessageCustomEvent.RoomStatus]: (msg: Console.ChatMessage) => {
                const { data } = msg.payload;

                // 不是播放器时，不处理事件
                if (isLivePlayerMode && !errorInfo) {
                    switch (data.status) {
                        case cLive.PushStatusMap.Pushing:
                            if (!liveDetail?.playUrl) {
                                console.warn('【直播详情缺失】');
                                return;
                            }
                            if (!playerInstance.current) {
                                console.log('【直播播创建】');
                                createTCPlayer(liveDetail.playUrl.webRtc);
                            } else {
                                console.log('【直播继续】');
                                playerInstance.current.src(liveDetail.playUrl.webRtc);
                            }
                            break;
                        case cLive.PushStatusMap.PausePush:
                            if (playerInstance.current && !paused) {
                                console.log('【直播暂停】');
                                playerInstance.current.unload();
                            }
                            break;
                        case cLive.PushStatusMap.StopPush:
                            if (playerInstance.current) {
                                console.log('【直播结束】');
                                playerInstance.current.unload();
                            }
                    }
                }

                setLiveStatus(data.status);
                if (getLiveDetail) {
                    getLiveDetail();
                }
            },
        }),
        [liveDetail, isLivePlayerMode, getLiveDetail, paused]
    );

    /**
     * 处理接收到的消息
     */
    const handleReceivedMessage = useCallback(
        (event: { name: string; data: Console.ChatMessage[] }) => {
            const { data } = event;

            data.forEach(msg => {
                const { type, payload } = msg;
                // 只处理自定义事件
                if (type !== TencentCloudChat.TYPES.MSG_CUSTOM) return;

                const customData = payload.data ? JSON.parse(payload.data) : {};
                const customEvent: string = customData.event ?? '';
                handleCustomMessage[customEvent] &&
                    handleCustomMessage[customEvent]({
                        ...msg,
                        payload: {
                            ...msg.payload,
                            data: customData,
                        },
                    });
            });
        },
        [handleCustomMessage]
    );

    useEffect(() => {
        if (chatInited && chat?.current) {
            chat.current.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
        }

        return () => {
            if (chat?.current) {
                chat.current.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
            }
        };
    }, [chatInited, handleReceivedMessage]);

    /**
     * @description: 切换全局禁言
     */
    const handleToggleGlobalMute = async (checked: boolean) => {
        if (!liveId) {
            throw new Error('直播id不存在');
        }

        await setGlobalMute({ liveId, muteStatus: checked ? 1 : 0 });
        setIsGlobalMute(checked);
    };

    /**
     * @description: 切换播放器显示状态
     */
    const togglePlayerMode = useCallback(
        (event: RadioChangeEvent) => {
            const value = event.target.value;
            setPlayerMode(event.target.value);

            if (value === LivePlayerMode.Live && liveDetail && liveStatus === cLive.PushStatusMap.Pushing) {
                if (!playerInstance.current) {
                    createTCPlayer(liveDetail.playUrl.webRtc);
                } else {
                    // 重新设置后，它会自动重新拉流和自动播放
                    playerInstance.current?.src(liveDetail.playUrl.webRtc);
                }
            }

            if (value !== LivePlayerMode.Live && playerInstance.current) {
                playerInstance.current.unload();
            }
        },
        [liveDetail, isMuted, liveStatus]
    );

    /**
     * @description: 暂停直播
     */
    const handleLivePause = () => {
        modal.confirm({
            title: '确定要暂停直播吗？',
            content: '暂停后直播将显示主播暂时离开，点击继续后重新开启直播。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await handleChangePushStatus(PushType.PausePush);
            },
        });
    };

    /**
     * @description: 关闭直播
     */
    const handleLiveClose = () => {
        modal.confirm({
            title: '确定要关闭直播吗？',
            content: '关闭后直播将结束，需要重新发起直播。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await handleChangePushStatus(PushType.EndPush);
            },
        });
    };

    /**
     * @description: 恢复直播
     */
    const handleLiveResume = async () => {
        await handleChangePushStatus(PushType.ResumePush);
    };

    /**
     * @description: 刷新直播
     */
    const handleLiveRefresh = async () => {
        clearError();
        if ((liveDetail?.liveCategory === cLive.LiveCategoryMap.NOW && !playerInstance.current) || !liveDetail) return;
        getLiveDetail?.().then(res => {
            if (res?.pushStatus === cLive.PushStatusMap.PausePush) {
                playerInstance.current?.src(liveDetail.playUrl.webRtc);
            }
        });
    };

    return {
        videoContainerRef,
        liveStatus,
        liveDetail,
        playerMode,
        isLivePlayerMode,
        isShowPlayer,
        isGlobalMute,
        isMuted,
        errorInfo,
        toggleMuted,
        togglePlayerMode,
        isFullscreen,
        togglePlayerFullscreen,
        handleLivePause,
        handleLiveClose,
        handleToggleGlobalMute,
        handleLiveResume,
        handleLiveRefresh,
    };
};
