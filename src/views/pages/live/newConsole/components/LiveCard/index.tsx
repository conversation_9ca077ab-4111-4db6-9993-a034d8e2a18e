/**
 * @Owners ljh
 * @Title 直播播放器
 */
import EmptyImg from '@/assets/images/empty-placeholder.png';
import liveLogo from '@/assets/images/live-logo.png';
import Show from '@/views/components/common/Show';
import SvgIcon from '@/views/components/common/SvgIcon';
import { ExpandOutlined, PauseCircleOutlined, PoweroffOutlined } from '@ant-design/icons';
import { cLive } from '@consts';
import { Avatar, Button, Empty, Flex, Radio, Space, Switch, Tag } from 'antd';
import classNames from 'classnames';
import 'tcplayer.js/dist/tcplayer.min.css';

import ChatRoom from '../ChatRoom';
import { LevelBadge, Role } from '../LevelBadge';

import './index.scss';
import { LivePlayerMode, useLiveCard } from './useLiveCard';

const LivePlayerModeOptions = [
    {
        label: '评论',
        value: 'comment',
    },
    {
        label: '直播',
        value: 'live',
    },
];

const LiveCard = () => {
    const {
        videoContainerRef,
        liveDetail,
        liveStatus,
        isShowPlayer,
        isLivePlayerMode,
        playerMode,
        isGlobalMute,
        isMuted,
        errorInfo,
        toggleMuted,
        togglePlayerMode,
        isFullscreen,
        togglePlayerFullscreen,
        handleLivePause,
        handleLiveClose,
        handleToggleGlobalMute,
        handleLiveResume,
        handleLiveRefresh,
    } = useLiveCard({ mode: LivePlayerMode.Comment, videoId: 'live-player' });

    return (
        <div className={classNames('live-card-wrap', { 'live-card-wrap-dark': isLivePlayerMode })}>
            <div className='live-player-container' ref={videoContainerRef}>
                {/* S 视频头部信息条 */}
                <Show when={isFullscreen}>
                    <div className='live-player-top'>
                        <img className='live-player-top-logo' src={liveLogo} alt='远方的梦想' draggable={false} />
                        <div className='live-player-top-info'>
                            <div className='live-player-user-info'>
                                <div className='live-player-user-info-avatar'>
                                    <Avatar src={liveDetail?.coverUrl} />
                                    <LevelBadge
                                        className='live-player-user-info-badge'
                                        level={liveDetail?.userInfo.level}
                                        icon={liveDetail?.userInfo.levelIcon}
                                        theme={liveDetail?.userInfo.role === Role.Vip ? 'gold' : 'green'}
                                    />
                                </div>
                                <span className='live-player-user-info-name'>{liveDetail?.liveTheme}</span>
                                {!!liveDetail?.pushStatus && (
                                    <Tag
                                        className='live-player-live-status-tag'
                                        color={cLive.PushStatusTextMap[liveDetail.pushStatus].color}
                                        bordered
                                    >
                                        {cLive.PushStatusTextMap[liveDetail.pushStatus].text}
                                    </Tag>
                                )}
                            </div>
                            <Show when={!!liveDetail?.actualStartTime}>
                                <div className='live-player-top-info-time'>开播时间：{liveDetail?.actualStartTime}</div>
                            </Show>
                            <Button
                                className='live-player-exit-fullscreen-btn'
                                variant='solid'
                                color='default'
                                size='small'
                                icon={<SvgIcon name='exit-expand-outlined' />}
                                onClick={togglePlayerFullscreen}
                            >
                                退出
                            </Button>
                        </div>
                    </div>
                </Show>
                {/* E 视频头部信息条 */}
                <div
                    className={classNames('live-player-video-area', {
                        'live-player-video-area--hidden': !isShowPlayer || !!errorInfo,
                    })}
                >
                    <video
                        className='live-player-video'
                        id='live-player'
                        preload='auto'
                        autoPlay
                        playsInline
                        webkit-playsinline='true'
                    />
                </div>
            </div>

            <div className='live-card-context'>
                <div className='live-card-context-header'>
                    <Radio.Group
                        options={LivePlayerModeOptions}
                        optionType='button'
                        value={playerMode}
                        onChange={togglePlayerMode}
                        className='live-card-mode-radio-group'
                    />
                    <div className='live-card-context-header-extra'>
                        <Space className='live-card-muted-switch'>
                            <span>全员禁言</span>
                            <Switch size='small' checked={isGlobalMute} onChange={handleToggleGlobalMute} />
                        </Space>
                    </div>
                </div>

                <Show when={isLivePlayerMode}>
                    <div className='live-card-context-video-info'>
                        <Flex className='live-card-control-bar' gap={8} justify='end'>
                            <Show when={isShowPlayer}>
                                <Button
                                    size='small'
                                    icon={<SvgIcon name={isMuted ? 'muted-outlined' : 'sound-outlined'} />}
                                    onClick={toggleMuted}
                                />
                            </Show>

                            <Show when={isShowPlayer}>
                                <Button size='small' icon={<ExpandOutlined />} onClick={togglePlayerFullscreen}>
                                    全屏
                                </Button>
                            </Show>

                            <Show when={isShowPlayer}>
                                <Button size='small' icon={<PauseCircleOutlined />} onClick={handleLivePause}>
                                    暂停
                                </Button>
                            </Show>

                            <Show
                                when={
                                    !!liveStatus &&
                                    ![cLive.PushStatusMap.StopPush, cLive.PushStatusMap.Expired].includes(liveStatus)
                                }
                            >
                                <Button size='small' icon={<PoweroffOutlined />} danger onClick={handleLiveClose}>
                                    关闭
                                </Button>
                            </Show>
                        </Flex>

                        {/* S 直播预告状态展示 */}
                        <Show when={liveStatus === cLive.PushStatusMap.NoStart}>
                            <div className='live-card-context-preview-status'>
                                直播预告
                                <br />
                                直播时间：{liveDetail?.preStartTime}
                            </div>
                        </Show>
                        {/* E 直播预告状态展示 */}
                        {/* S 暂停推流状态展示 */}
                        <Show when={!errorInfo && liveStatus === cLive.PushStatusMap.PausePush}>
                            <div className='live-card-context-pause-status'>
                                <div className='live-card-context-pause-status-text'>您已暂停直播</div>
                                <Button type='primary' icon={<SvgIcon name='play-circel-outlined' />} onClick={handleLiveResume}>
                                    继续直播
                                </Button>
                            </div>
                        </Show>
                        {/* E 暂停推流状态展示 */}
                        {/* S 未直播状态展示 */}
                        <Show
                            when={
                                !!liveStatus && [cLive.PushStatusMap.Expired, cLive.PushStatusMap.StopPush].includes(liveStatus)
                            }
                        >
                            <Empty className='live-card-context-live-empty' image={EmptyImg} description='暂未开直播' />
                        </Show>
                        {/* E 未直播状态展示 */}
                        <Show when={!!errorInfo}>
                            <div className='live-card-context-pause-status'>
                                <div className='live-card-context-pause-status-text'>播放出错了</div>
                                <Button type='primary' icon={<SvgIcon name='play-circel-outlined' />} onClick={handleLiveRefresh}>
                                    刷新重试
                                </Button>
                            </div>
                        </Show>
                    </div>
                </Show>

                <div className='live-card-context-body'>
                    <Show when={liveDetail && liveDetail.imGroupId}>
                        <ChatRoom
                            className='live-chat-room'
                            groupId={liveDetail?.imGroupId as number}
                            liveStatus={liveStatus}
                            showPlaceholderWhenBlank={!playerMode}
                            userInfo={{
                                level: liveDetail?.userInfo.level,
                                levelIcon: liveDetail?.userInfo.levelIcon,
                                role: liveDetail?.userInfo.role,
                                imAvatar: liveDetail?.userInfo.avatar,
                                imUserId: liveDetail?.imUserId,
                            }}
                        />
                    </Show>
                </div>
            </div>
        </div>
    );
};

export default LiveCard;
