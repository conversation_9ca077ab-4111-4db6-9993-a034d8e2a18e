.live-card-wrap {
    position: relative;
    // flex: 0 0 420px;
    width: 100%;
    height: 530px;

    .live-player-container {
        position: relative;
        width: 100%;
        height: 100%;
        background: #fff;
        transition: background 0.3s;
    }

    .live-player-top {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 56px;
        box-sizing: border-box;
        color: #fff;
        z-index: 10;
    }

    .live-player-top-logo {
        width: 272px;
    }

    .live-player-top-info {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        align-items: center;
        width: 30%;
        min-width: 540px;
        color: rgba(255, 255, 255, 0.7);
    }

    .live-player-user-info {
        display: flex;
        align-items: center;
    }

    .live-player-user-info-avatar {
        position: relative;
        margin-right: 8px;
    }

    .live-player-user-info-badge {
        position: absolute;
        bottom: -2px;
        left: 50%;
        transform: translateX(-50%);
    }

    .live-player-user-info-name {
        overflow: hidden;
        max-width: 120px;
        text-overflow: ellipsis;
    }

    .live-player-live-status-tag {
        margin-inline-start: 8px;
        padding-inline: 4px;
        color: #fff;
        line-height: 16px;
    }

    .live-player-top-info-time {
        margin-inline-start: 26px;
    }

    .live-player-exit-fullscreen-btn {
        margin-inline-start: auto;
        color: #fff;
        background: rgba(255, 255, 255, 0.4);
    }

    .live-player-video-area {
        position: relative;
        width: 100%;
        height: 100%;
        object-fit: cover;

        &.live-player-video-area--hidden {
            display: none;
        }

        &::after {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(0, 0, 0, 0.3);
        }
    }

    .live-player-video {
        display: block;
        width: 100%;
        height: 100%;
        background: #000;
    }
}

.live-card-context {
    position: absolute;
    inset: 0;
    display: flex;
    flex-direction: column;

    .live-card-mode-radio-group {
        padding: 2px;
        background: #f5f5f5;
        border-radius: 4px;

        .ant-radio-button-wrapper {
            height: 24px;
            line-height: 22px;
            padding: 0 10px;
            color: #333;
            font-size: 16px;
            background: transparent;
            border: none;

            &:not(:first-child)::before {
                display: none;
            }
        }

        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
            font-weight: 600;
            background: #fff;
            border-radius: 4px;
        }
    }

    .live-card-context-header {
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 24px;
        height: 50px;
    }

    .live-card-context-title {
        font-size: 16px;
        font-weight: 600;
        transition: color 0.2s;
    }

    .live-card-muted-switch {
        color: #666666;
        transition: color 0.2s;
    }

    .live-card-block-keywords-btn {
        color: #666666;
        background: #f5f5f5;
        border-radius: 6px 6px 6px 6px;
        padding: 0px 8px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .live-card-context-header-extra {
        flex-shrink: 0;
        display: flex;
        gap: 8px;
    }

    .live-card-context-video-info {
        flex-shrink: 0;
        height: 204px;
    }

    .live-card-control-bar {
        margin-bottom: 24px;
        padding: 0 24px;
    }

    .live-card-context-preview-status {
        margin-top: 44px;
        color: #fff;
        font-size: 16px;
        text-align: center;
    }

    .live-card-context-pause-status {
        color: #fff;
        font-size: 16px;
        text-align: center;

        height: 45vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .live-card-context-pause-status-text {
        margin-bottom: 16px;
    }

    .live-card-context-live-empty {
        margin-top: 44px;

        .ant-empty-description {
            color: #fff;
        }
    }

    .live-card-context-body {
        flex-grow: 1;
        overflow: hidden;
        position: relative;
    }
}

// 暗黑主题
.live-card-wrap-dark {
    .live-player-container {
        background: linear-gradient(141deg, #004637 12%, #362367 94%);
    }

    .live-card-context-header {
        box-shadow: none;
    }

    .live-card-context-title {
        color: #fff;
    }

    .live-card-muted-switch {
        color: #fff;
    }

    .live-card-hide-switch {
        &.ant-btn-variant-filled {
            color: #00b68f;
            background: #fff;

            &:not(:disabled):hover {
                background: #fff;
            }
        }
    }

    .live-chat-room {
        .chat-room-window {
            justify-content: flex-start;
        }

        .hoc-chat-message {
            &:hover {
                background: rgba(0, 0, 0, 0.2);
            }
        }

        .hoc-chat-message-actions {
            background: linear-gradient(270deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0) 97%);

            .ant-btn-variant-link {
                color: #fff;
            }
        }

        .float-new-message-tip {
            color: #fff;
            background: rgba(0, 0, 0, 0.6);
            box-shadow: 0px 2px 8px 0px rgba(99, 99, 99, 0.2);
        }

        .chat-room-input {
            color: #ffffff;
            background: transparent;
            border-color: rgba(230, 230, 230, 0.5);

            &::placeholder {
                color: #bfbfbf;
            }

            &:hover {
                border-color: rgba(230, 230, 230, 0.7);
            }
        }

        .chat-message-text {
            color: #ffffff;
        }

        .chat-room-send-btn {
            color: #333333;
            background: #ffffff;

            &:not(:disabled):hover {
                color: #333333;
                background: #ffffff;
            }

            &:disabled {
                border-color: transparent;
                background: rgba(255, 255, 255, 0.5);
            }
        }

        .chat-room-send-upper-wall-btn {
            color: #333333;
            background: #ffffff;
            border-color: transparent;

            &:not(:disabled):hover {
                color: #333333;
                background: #ffffff;
                border-color: transparent;
            }
        }
    }
}

.live-card-setting-tooltip {
    max-width: 100%;

    .ant-tooltip-inner {
        padding: 16px;
    }
}