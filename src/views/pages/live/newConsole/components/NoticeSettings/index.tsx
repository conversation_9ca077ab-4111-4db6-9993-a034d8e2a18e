/**
 * @Owners ljh
 * @Title 消息通知
 */
import { Button, Input, Switch } from 'antd';
import classNames from 'classnames';

import LivePreviewSettings from './components/LivePreviewSettings';
import './index.scss';
import { useNoticeSettings } from './useNoticeSettings';

const TextArea = Input.TextArea;

const NoticeSettings = () => {
    const {
        announcementLoading,
        prevAnnouncement,
        announcement,
        broadcastLoading,
        broadcast,
        prevBroadcast,
        handleNoticeChange,
        handleBroadcastChange,
        handleSave,
        nextLiveTime,
        isOpenNextLive,
    } = useNoticeSettings();

    return (
        <div className='notice-settings'>
            <div className='notice-settings-section'>
                <div className='notice-settings-section-title'>公告</div>
                <div className='notice-settings-section-content'>
                    <TextArea
                        className={classNames('notice-settings-section-textarea', {
                            'notice-settings-section-textarea--loading': announcementLoading,
                        })}
                        value={announcement}
                        maxLength={50}
                        autoSize={{
                            minRows: 4,
                        }}
                        showCount
                        placeholder='请输入公告内容，若清空，则直播间不展示'
                        onChange={handleNoticeChange}
                    />
                    <Button
                        className='notice-settings-section-button'
                        type='primary'
                        loading={announcementLoading}
                        onClick={handleSave.notice}
                        disabled={announcement === prevAnnouncement.current}
                    >
                        保存
                    </Button>
                </div>
            </div>
            <div className='notice-settings-section'>
                <div className='notice-settings-section-title'>广播</div>
                <div className='notice-settings-section-content notice-settings-section-content--row'>
                    <TextArea
                        className={classNames('notice-settings-section-textarea', {
                            'notice-settings-section-textarea--loading': broadcastLoading,
                        })}
                        rows={1}
                        maxLength={20}
                        showCount
                        placeholder='请输入广播内容'
                        value={broadcast}
                        onChange={handleBroadcastChange}
                    />
                    <Button
                        className='notice-settings-section-button'
                        type='primary'
                        loading={broadcastLoading}
                        onClick={handleSave.broadcast}
                        disabled={broadcast === prevBroadcast.current}
                    >
                        保存
                    </Button>
                </div>
            </div>
            <LivePreviewSettings />
            <div className='notice-settings-section'>
                <div>
                    <div className='notice-settings-section-title'>直播预告推送</div>
                    <div>开启后将在直播间展示直播预告预约小窗，同一时刻仅支持开启1场直播预告的推送开关</div>
                </div>
                <div className='flex-wrap'>
                    <div className='sub-text'>开启后将在直播间展示下场直播预告信息｜下场直播预约{nextLiveTime}</div>
                    <Switch value={isOpenNextLive} onClick={handleSave.pushLiveReservation} />
                </div>
            </div>
        </div>
    );
};

export default NoticeSettings;
