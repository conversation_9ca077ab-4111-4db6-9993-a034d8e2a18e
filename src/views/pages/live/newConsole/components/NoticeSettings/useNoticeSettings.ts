/**
 * @Owners ljh
 * @Title 消息通知
 */
import { getLiveReservationPushMsg, pushLiveReservation, updateBroadcast, updateNotice } from '@/api/modules/live/console';
import { message } from 'antd';
import { type ChangeEvent, useContext, useEffect, useMemo, useRef, useState } from 'react';

import { LiveContext } from '../../contexts/LiveContext';

export const useNoticeSettings = () => {
    const { liveDetail } = useContext(LiveContext);
    const [announcement, setAnnouncement] = useState(''); // 公告
    const [broadcast, setBroadcast] = useState(''); // 广播
    const [isOpenNextLive, setIsOpenNextLive] = useState(false); // 是否预约下场直播
    const [nextLiveTime, setNextLiveTime] = useState<string>(); // 下场直播时间
    const [nextLiveId, setNextLiveId] = useState<number>(); // 下场直播ID
    const [announcementLoading, setAnnouncementLoading] = useState(false); // 公告loading
    const [broadcastLoading, setBroadcastLoading] = useState(false); // 广播loading
    const prevAnnouncement = useRef(''); // 公告前值
    const prevBroadcast = useRef(''); // 广播前值

    useEffect(() => {
        if (liveDetail) {
            prevAnnouncement.current = liveDetail.announcement;
            setAnnouncement(liveDetail.announcement || '');
            prevBroadcast.current = liveDetail.broadcastContent;
            setBroadcast(liveDetail.broadcastContent || '');
        }
    }, [liveDetail?.announcement, liveDetail?.broadcastContent]);

    useEffect(() => {
        getNextLiveTime();
    }, []);

    const getNextLiveTime = async () => {
        if (liveDetail?.id) {
            const res = await getLiveReservationPushMsg({
                liveId: liveDetail.id,
            });
            setNextLiveTime(res.data?.preStartTime);
            setNextLiveId(res.data?.liveId);
            setIsOpenNextLive(res.data?.pushSwitch === 1);
        }
    };

    const handleNoticeChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        setAnnouncement(e.target.value);
    };

    const handleBroadcastChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        setBroadcast(e.target.value);
    };

    const handleSave = useMemo(
        () => ({
            notice: async () => {
                if (!liveDetail?.id) return;
                if (announcementLoading) return;
                try {
                    setAnnouncementLoading(true);
                    await updateNotice({
                        liveId: liveDetail.id,
                        announcement,
                    });
                    prevAnnouncement.current = announcement;
                    message.success('公告保存成功');
                } catch (error) {
                    message.error('公告保存失败');
                } finally {
                    setAnnouncementLoading(false);
                }
            },
            broadcast: async () => {
                if (!liveDetail?.id) return;
                if (broadcastLoading) return;
                try {
                    setBroadcastLoading(true);
                    await updateBroadcast({
                        liveId: liveDetail.id,
                        broadcastContent: broadcast,
                    });
                    prevBroadcast.current = broadcast;
                    message.success('广播保存成功');
                } catch (error) {
                    message.error('广播保存失败');
                } finally {
                    setBroadcastLoading(false);
                }
            },
            pushLiveReservation: async (val: boolean) => {
                if (!liveDetail?.id) {
                    message.warning('缺少直播id');
                    return;
                }
                if (!nextLiveId) {
                    message.warning('暂无预告可展示');
                    return;
                }
                setIsOpenNextLive(val);
                try {
                    await pushLiveReservation({
                        liveId: liveDetail.id,
                        pushSwitch: val ? 1 : 0,
                    });
                    message.success(`操作成功，直播间已${val ? '展示' : '隐藏'}下场直播预告`);
                } catch (error) {
                    message.error('保存失败');
                }
            },
        }),
        [announcement, broadcast, announcementLoading, broadcastLoading, liveDetail?.id, nextLiveId]
    );

    return {
        announcementLoading,
        broadcastLoading,
        prevAnnouncement,
        prevBroadcast,
        announcement,
        broadcast,
        handleNoticeChange,
        handleBroadcastChange,
        handleSave,
        nextLiveTime,
        isOpenNextLive,
    };
};
