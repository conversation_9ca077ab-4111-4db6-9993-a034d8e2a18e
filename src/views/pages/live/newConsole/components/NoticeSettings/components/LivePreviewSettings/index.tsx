/**
 * @Owners ljh
 * @Title 直播预告设置
 */
import { Button, Input, Space, Switch, Table, type TableProps } from 'antd';

import './index.scss';

interface DataType {
    key: string;
    name: string;
    age: number;
    address: string;
    tags: string[];
}

const data: DataType[] = [
    {
        key: '1',
        name: '<PERSON>',
        age: 32,
        address: 'New York No. 1 Lake Park',
        tags: ['nice', 'developer'],
    },
    {
        key: '2',
        name: '<PERSON>',
        age: 42,
        address: 'London No. 1 Lake Park',
        tags: ['loser'],
    },
    {
        key: '3',
        name: '<PERSON>',
        age: 32,
        address: 'Sydney No. 1 Lake Park',
        tags: ['cool', 'teacher'],
    },
];

const LivePreviewSettings = () => {
    console.log('1');

    const columns: TableProps<DataType>['columns'] = [
        {
            title: '直播信息',
            dataIndex: 'name',
            key: 'name',
            render: text => <a>{text}</a>,
        },
        {
            title: '预约开播时间',
            dataIndex: 'age',
        },
        {
            title: '预约人数',
            dataIndex: 'address',
        },
        {
            title: '操作',
            render: () => <Switch />,
        },
    ];

    return (
        <div>
            <div>
                <div>直播预告推送</div>
                <div>开启后将在直播间展示直播预告预约小窗，同一时刻仅支持开启1场直播预告的推送开关</div>
            </div>
            <Space>
                <Input placeholder='请输入直播标题/主播姓名' />
                <Button>搜索</Button>
            </Space>
            <Table dataSource={data} columns={columns} />
        </div>
    );
};

export default LivePreviewSettings;
