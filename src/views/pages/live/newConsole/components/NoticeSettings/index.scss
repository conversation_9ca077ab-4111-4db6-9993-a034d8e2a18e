.notice-settings {
    padding: 14px 24px;
    background-color: white;

    .notice-settings-section {
        margin-bottom: 32px;
    }

    .notice-settings-section-title {
        margin-bottom: 16px;
        color: #333333;
        font-size: 16px;
        font-weight: 600;
    }

    .flex-wrap {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .sub-text {
            height: 18px;
            line-height: 18px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            text-align: left;
            font-style: normal;
            text-transform: none;
        }
    }

    .notice-settings-section-content {
        position: relative;

        &.notice-settings-section-content--row {
            .ant-input-data-count {
                bottom: 16px;
            }

            .notice-settings-section-button {
                bottom: 50%;
                transform: translateY(50%);
            }
        }
    }

    .notice-settings-section-textarea {
        textarea {
            padding: 16px;
            resize: none;
        }

        .ant-input-data-count {
            bottom: 20px;
            right: 88px;
            transition: right 0.2s;
        }
    }

    .notice-settings-section-textarea--loading {
        .ant-input-data-count {
            bottom: 20px;
            right: 110px;
        }
    }

    .notice-settings-section-button {
        z-index: 5;
        position: absolute;
        bottom: 16px;
        right: 16px;
    }
}