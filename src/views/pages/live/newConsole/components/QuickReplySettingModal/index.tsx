/**
 * @Owners ljh
 * @Title 快速评论设置
 */

import { getLiveManualComment, updateLiveManualComment } from '@/api/modules/live/console';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Input, Modal, message } from 'antd';
import { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';

import './index.scss';

interface KeywordItem {
    id: number;
    text: string;
}

export interface QuickReplySettingModalProps {
    liveId?: number;
}

export interface QuickReplySettingModalRef {
    open(): void;
}

const QuickReplySettingModal = forwardRef<QuickReplySettingModalRef, QuickReplySettingModalProps>((props, ref) => {
    const { liveId } = props;
    const [visible, setVisible] = useState<boolean>(false);
    /** 是否正在获取数据 */
    const [selecting, setSelecting] = useState(false);
    /** 是否正在更新 */
    const [updating, setUpdating] = useState(false);
    /** 全局关键词 */
    const [globalKeywordList, setGlobalKeywordList] = useState<string[]>([]);
    /** 自定义关键词 */
    const [customKeywordList, setCustomKeywordList] = useState<KeywordItem[]>([]);
    /** 自定义关键词id */
    const nextKeywordId = useRef<number>(0);
    /** 输入框值 */
    const [inputVal, setInputVal] = useState<string>('');

    useImperativeHandle(ref, () => ({
        open: () => {
            fetchQuickReplyList();
            setVisible(true);
        },
    }));

    const fetchQuickReplyList = useCallback(async () => {
        if (!liveId) return;
        if (selecting) return;
        setSelecting(true);
        try {
            const res = await getLiveManualComment({ liveId });

            setGlobalKeywordList(res.data?.globalPreComment.split(',') ?? []);

            const list =
                res.data?.manualPreComment
                    .replaceAll('\\,', ',')
                    .split(',')
                    .reduce((acc, item, index) => {
                        if (item.trim()) {
                            acc.push({ id: index, text: item });
                        }
                        return acc;
                    }, [] as KeywordItem[]) ?? [];
            setCustomKeywordList(list);
            nextKeywordId.current = list.length;
        } finally {
            setSelecting(false);
        }
    }, [selecting, liveId]);

    /** 重置自定义关键词 */
    const handleResetKeywords = useCallback(() => {
        setCustomKeywordList([]);
    }, []);

    /** 添加自定义关键词 */
    const handleAddKeywords = useCallback(() => {
        const text = inputVal.trim();
        if (text === '') return;
        setCustomKeywordList(prev => [...prev, { id: nextKeywordId.current++, text }]);
        setInputVal('');
    }, [inputVal]);

    /** 删除自定义关键词 */
    const handleDelKeyword = (itemId: number) => {
        setCustomKeywordList(prev => prev.filter(item => item.id !== itemId));
    };

    /** 确定 */
    const handleOk = useCallback(async () => {
        if (!liveId) return;
        if (updating) return;
        try {
            setUpdating(true);
            const customKeywordStr = customKeywordList.map(item => item.text.replaceAll(',', '\\,')).join(',');
            await updateLiveManualComment({
                liveId,
                manualComment: customKeywordStr,
            });
            message.success('更新成功');
        } finally {
            setUpdating(false);
            setVisible(false);
        }
    }, [customKeywordList, liveId]);

    /** 取消 */
    const handleCancel = useCallback(() => {
        setVisible(false);
    }, []);

    const handleAfterClose = useCallback(() => {
        setInputVal('');
        setGlobalKeywordList([]);
        setCustomKeywordList([]);
    }, []);

    return (
        <Modal
            title='快速评论短语配置'
            wrapClassName='quick-reply-setting-modal'
            open={visible}
            okButtonProps={{ disabled: selecting, loading: updating }}
            cancelButtonProps={{ disabled: updating }}
            onOk={handleOk}
            onCancel={handleCancel}
            afterClose={handleAfterClose}
            width={560}
            maskClosable={false}
        >
            <div className='qrsm-explain'>此功能控制直播间中预置快速评论短语，便于用户快速发送评论</div>
            <Input
                className='qrsm-input'
                placeholder='请输入您要增加的快速评论短语，限制10个字以内'
                allowClear
                value={inputVal}
                onChange={e => {
                    setInputVal(e.target.value);
                }}
                onPressEnter={handleAddKeywords}
                maxLength={10}
                suffix={
                    <Button onClick={handleAddKeywords} className='qrsm-add-btn' type='link'>
                        添加
                    </Button>
                }
            />
            <div className='qrsm-section'>
                <div className='qrsm-section-header'>
                    <div className='qrsm-section-title'>系统快速评论短语</div>
                </div>
                <div className='qrsm-section-desc qrsm-explain'>系统快速评论短语由平台统一维护，暂不支持修改</div>
                <div className='qrsm-keyword-list'>
                    {globalKeywordList.map((item, index) => (
                        <div className='qrsm-keyword' key={index}>
                            {item}
                        </div>
                    ))}
                </div>
            </div>
            <div className='qrsm-section'>
                <div className='qrsm-section-header'>
                    <div className='qrsm-section-title'>当前直播间快速评论短语</div>
                    <div className='qrsm-reset-btn' onClick={handleResetKeywords}>
                        重置
                    </div>
                </div>
                <div className='qrsm-keyword-list'>
                    {customKeywordList.map(item => (
                        <div className='qrsm-keyword' key={item.id}>
                            {item.text}
                            <CloseOutlined className='qrsm-del-btn' onClick={() => handleDelKeyword(item.id)} />
                        </div>
                    ))}
                </div>
            </div>
        </Modal>
    );
});

export default QuickReplySettingModal;
