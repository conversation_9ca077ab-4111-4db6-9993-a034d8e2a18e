.quick-reply-setting-modal {
    .qrsm-explain {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
    }

    .qrsm-input {
        margin: 12px 0;

        .qrsm-add-btn {
            padding-left: 5px;
            padding-right: 0px;
        }
    }

    .qrsm-section {
        margin-bottom: 12px;
    }

    .qrsm-section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
    }

    .qrsm-section-title {
        font-weight: 600;
        color: #333333;
    }

    .qrsm-reset-btn {
        color: #666666;
        cursor: pointer;
    }

    .qrsm-section-desc {
    }

    .qrsm-keyword-list {
        margin-top: 8px;
        display: flex;
        flex-wrap: wrap;
        gap: 12px 8px;
        max-height: 200px;
        overflow-y: auto;
    }

    .qrsm-keyword {
        padding: 5px 8px;
        font-size: 14px;
        color: #333333;
        background: #e5f8f7;
        border-radius: 6px;
    }

    .qrsm-del-btn {
        margin-left: 4px;
        color: #999;
        font-size: 12px;
        cursor: pointer;
    }
}
