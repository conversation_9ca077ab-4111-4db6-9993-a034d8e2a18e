/**
 * @Owners ljh
 * @Title 连麦
 */
import InteractiveLiveClosed from '@/assets/images/interactive-live-closed.png';
import Show from '@/views/components/common/Show';
import SvgIcon from '@/views/components/common/SvgIcon';
import { PoweroffOutlined } from '@ant-design/icons';
import { Button, Empty, Flex, Tabs, type TabsProps } from 'antd';
import { useCallback, useContext, useMemo, useRef } from 'react';

import { LiveContext } from '../../contexts/LiveContext';
import CoStreamPanel from '../CoStreamPanel';
import InteractiveSettingModal, { type InteractiveSettingModalRef } from '../InteractiveSettingModal';
import InviteLinkPanel from '../InviteLinkPanel';

import './index.scss';
import { useInteractiveLive } from './useInteractiveLive';

enum InteractiveLiveTab {
    /** 连麦列表 */
    CoStream = '1',
    /** 邀请连线 */
    InviteLink = '2',
}

interface InteractiveLiveEmptyProps {
    onClick?(): void;
}

/**
 * 连麦空状态
 */
const InteractiveLiveEmpty = (props: InteractiveLiveEmptyProps) => {
    const { onClick } = props;

    return (
        <Empty
            className='interactive-live-empty'
            image={InteractiveLiveClosed}
            description={
                <>
                    <div className='interactive-live-empty-title'>直播间观众连线</div>
                    <div className='interactive-live-empty-desc'>1-4人连线</div>
                    <Button type='primary' onClick={onClick}>
                        开启连麦
                    </Button>
                </>
            }
        />
    );
};

const InteractiveLive = () => {
    const { liveId, liveDetail } = useContext(LiveContext);
    const { activeTab, handleTabChange, handleOpenInteractive, handleCloseInteractive } = useInteractiveLive();
    /** 连麦设置弹窗 */
    const interactiveSettingModalRef = useRef<InteractiveSettingModalRef>(null);

    /** 邀请连线 */
    const handleNavigateToInvite = useCallback(() => {
        handleTabChange(InteractiveLiveTab.InviteLink);
    }, [handleTabChange]);

    /** tab */
    const items = useMemo<TabsProps['items']>(
        () => [
            {
                key: InteractiveLiveTab.CoStream,
                label: '连麦列表',
                children: <CoStreamPanel onInvite={handleNavigateToInvite} />,
            },
            {
                key: InteractiveLiveTab.InviteLink,
                label: '邀请连线',
                children: <InviteLinkPanel />,
            },
        ],
        [handleNavigateToInvite]
    );

    /** 连麦设置弹窗 */
    const handleSetting = useCallback(() => {
        if (!liveId) return;
        interactiveSettingModalRef.current?.open(liveId);
    }, [liveId]);

    return (
        <div className='interactive-live'>
            <Show when={!!liveDetail?.enableMic} fallback={<InteractiveLiveEmpty onClick={handleOpenInteractive} />}>
                <Tabs
                    className='interactive-live-tabs'
                    items={items}
                    activeKey={activeTab}
                    onChange={handleTabChange}
                    destroyInactiveTabPane
                    tabBarExtraContent={{
                        right: (
                            <Flex gap={8}>
                                <Button
                                    className='interactive-live-tabs-action'
                                    size='small'
                                    color='default'
                                    variant='filled'
                                    icon={<SvgIcon name='setting-outlined' />}
                                    onClick={handleSetting}
                                />
                                <Button
                                    className='interactive-live-tabs-action interactive-live-tabs-action--close'
                                    size='small'
                                    color='default'
                                    variant='filled'
                                    icon={<PoweroffOutlined />}
                                    onClick={handleCloseInteractive}
                                />
                            </Flex>
                        ),
                    }}
                />
                {/* 连麦设置弹窗 */}
                <InteractiveSettingModal ref={interactiveSettingModalRef} />
            </Show>
        </div>
    );
};

export default InteractiveLive;
