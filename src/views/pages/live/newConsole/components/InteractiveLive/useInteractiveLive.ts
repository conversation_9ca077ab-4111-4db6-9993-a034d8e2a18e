/**
 * @Owners ljh
 * @Title 连麦
 */
import { type Console } from '@/api/interface/live/console';
import { updateEnableMic } from '@/api/modules/live/console';
import { LiveEnableMicStatus } from '@/consts/cLive';
import { modal } from '@/views/components/UseAppPrompt';
import { cLive } from '@consts';
import TencentCloudChat from '@tencentcloud/chat';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { LiveContext } from '../../contexts/LiveContext';

enum InteractiveLiveTab {
    /** 连麦列表 */
    CoStream = '1',
    /** 邀请连线 */
    InviteLink = '2',
}

export const useInteractiveLive = () => {
    const { liveId, setLiveDetail, chatInited, chat } = useContext(LiveContext);
    /** 连麦列表 */
    const [activeTab, setActiveTab] = useState(InteractiveLiveTab.CoStream);

    const handleCustomMessage = useMemo<Record<string, (msg: Console.ChatMessage) => void>>(
        () => ({
            /** 主播开启连麦 */
            [cLive.IMMessageCustomEvent.LiveMicOpen]: () => {
                setLiveDetail && setLiveDetail(prev => (prev ? { ...prev, enableMic: LiveEnableMicStatus.Open } : prev));
            },
            /** 主播关闭连麦 */
            [cLive.IMMessageCustomEvent.LiveMicClose]: () => {
                setLiveDetail && setLiveDetail(prev => (prev ? { ...prev, enableMic: LiveEnableMicStatus.Close } : prev));
            },
        }),
        [setLiveDetail]
    );

    /**
     * 处理接收到的消息
     */
    const handleReceivedMessage = useCallback(
        (event: { name: string; data: Console.ChatMessage[] }) => {
            const { data } = event;

            data.forEach(msg => {
                const { type, payload } = msg;
                // 只处理自定义事件
                if (type !== TencentCloudChat.TYPES.MSG_CUSTOM) return;

                const customData = payload.data ? JSON.parse(payload.data) : {};
                const customEvent: string = customData.event ?? '';
                handleCustomMessage[customEvent] &&
                    handleCustomMessage[customEvent]({
                        ...msg,
                        payload: {
                            ...msg.payload,
                            data: customData,
                        },
                    });
            });
        },
        [handleCustomMessage]
    );

    useEffect(() => {
        if (chatInited && chat?.current) {
            chat.current.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
        }

        return () => {
            if (chat?.current) {
                chat.current.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
            }
        };
    }, [chatInited, handleReceivedMessage]);

    const handleTabChange = useCallback((key: string) => {
        setActiveTab(key as InteractiveLiveTab);
    }, []);

    /** 开启直播间连麦 */
    const handleOpenInteractive = useCallback(async () => {
        await updateEnableMic({ liveId, status: LiveEnableMicStatus.Open });
    }, [liveId]);

    /** 关闭直播间连麦 */
    const handleCloseInteractive = useCallback(() => {
        modal.confirm({
            title: '确定关闭直播间连麦吗？',
            content: '可在手机端或中控台中开启直播间连麦',
            okText: '确定关闭',
            cancelText: '暂不关闭',
            onOk: async () => {
                try {
                    await updateEnableMic({ liveId, status: LiveEnableMicStatus.Close });
                } catch (error) {
                    console.error(error);
                }
            },
        });
    }, [liveId]);

    return {
        activeTab,
        handleTabChange,
        handleOpenInteractive,
        handleCloseInteractive,
    };
};
