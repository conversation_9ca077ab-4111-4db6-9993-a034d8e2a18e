.live-settings-drawer {
    .ant-drawer-body {
        display: flex;
        gap: 0 10px;
        max-height: 100%;
        overflow: hidden;
        padding: 0;
        background: #f7f8f9;
    }

    .ant-drawer-footer {
        border-top: none;
        text-align: right;
    }

    .live-settings-drawer-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        max-height: 100%;
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #eaeaea transparent;

        // &::-webkit-scrollbar {
        //     width: 6px;
        // }
    }

    .live-settings-drawer-right {
        padding: 20px 32px 20px 40px;
        background: #fff;
        overflow: scroll;

        .title {
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            margin-bottom: 12px;
            user-select: none;
        }

        .top {
            overflow: hidden;
            height: 50vh;
            padding: 12px;
            background: #f7f8fa;
            border-radius: 6px 6px 6px 6px;
            border: 1px solid #e8e8e8;

            .no-push-url {
                background: #fff;
                height: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
            }
            .footer-text {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 12px;
                .title {
                    margin: 0;
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 14px;
                    color: #5c5c5c;
                    line-height: 22px;
                    text-align: center;
                }
            }
        }

        .bottom {
            margin-top: 24px;
        }
    }

    // 卡片
    .live-setting-card {
        flex-shrink: 0;
        padding: 32px 45px 32px 24px;
        margin-bottom: 10px;
        background: #fff;

        &:first-of-type {
            padding-top: 16px;
        }

        &:last-of-type {
            flex-grow: 1;
            margin-bottom: 0;
        }
    }

    .lsc-header {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        padding-left: 12px;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #00b68f;
            border-radius: 4px;
        }
    }

    .lsc-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    .lsc-body {
    }

    .live-push-switch {
        display: flex;
        gap: 12px;
        align-items: center;
        margin-top: 12px;
        justify-content: space-between;

        &.disabled {
            opacity: 0.6;
            user-select: none;
            * {
                cursor: not-allowed;
            }
        }

        .live-push-switch-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 8px;
            cursor: pointer;
            padding: 14px 0 16px;
            position: relative;
            // transition: all 200ms;

            &.disabled {
                opacity: 0.4;
                user-select: none;
                * {
                    cursor: not-allowed;
                }
            }

            // &:hover {
            //     transform: scale(1.02);
            // }

            &.manual {
                background: #f5f5f5;
            }

            &.auto {
                background: url('https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/live-push-bg-v2.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }

            .live-push-switch-item-watermark {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 60px;
                height: 60px;
                background: url('https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/live-push-watermark.png');
                background-repeat: no-repeat;
                background-size: 100% 100%;
            }

            .live-push-switch-item-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 14px;
                color: #333333;
                line-height: 18px;
                text-align: center;
            }

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                line-height: 18px;
                text-align: left;
                margin: 8px 0;
            }

            .live-push-switch-item-btn {
            }
        }
    }

    .live-settings-drawer-left-content {
        border: 1px solid #eee;
        border-radius: 8px;
    }

    // 链接
    .live-settings-link-box {
        position: relative;
        padding: 8px 8px 16px;
        // border-radius: 8px;
        // border: 1px solid #eee;

        &.live-push-box {
            border-top: 1px solid #eee;
            .lslb-link {
                margin-top: 20px;
            }
        }

        &.disabled {
            opacity: 0.6;
            user-select: none;
            cursor: not-allowed;
        }

        // + .live-settings-link-box {
        //     margin-top: 12px;
        // }
    }

    .lslb-title {
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
        color: #333;
    }

    .lslb-link {
        font-size: 12px;
        color: #666;
    }

    .lslb-switch {
        position: absolute;
        right: 8px;
        top: 12px;
        color: #666;
    }

    .lslb-copy-btn {
        position: absolute;
        right: 8px;
        top: 12px;
        color: #666;
    }

    // 屏蔽词设置
    .live-block-keywords-settings {
    }

    .lbks-explain {
        margin-bottom: 16px;
        font-size: 12px;
        line-height: 18px;
        color: #999;
    }

    .lbks-input {
        margin-bottom: 16px;
    }

    .lbks-add-btn {
        padding-left: 5px;
        padding-right: 0px;
        height: 22px;
    }

    .lbks-section {
    }

    .lbks-section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    .lbks-subtitle {
        font-size: 14px;
        color: #333;
        font-weight: 600;
    }

    .lbks-reset-btn {
        font-size: 14px;
        color: #999;
        cursor: pointer;
    }

    .lbks-expand-btn {
        margin-top: 12px;
        padding: 0;
        gap: 4px;

        .lbks-expand-btn-icon {
            transition: transform 0.2s;
        }

        &.lbks-expand-btn--active {
            .lbks-expand-btn-icon {
                transform: rotate(180deg);
            }
        }
    }

    .lbks-empty {
        padding-top: 20px;
    }

    .lbks-keyword-list-wrapper {
        overflow: hidden;
    }

    .lbks-keyword-list {
        overflow: hidden;
        display: flex;
        flex-wrap: wrap;
        gap: 12px 8px;
    }

    .lbks-keyword {
        display: inline-flex;
        align-items: center;
        padding: 5px 8px;
        font-size: 14px;
        line-height: 22px;
        color: #333;
        background: #e5f8f7;
        border-radius: 6px;
    }

    .live-quick-comment-settings {
    }

    .live-tag-holder {
        margin-right: 2px;
        display: inline-flex;
        align-items: center;
        color: #bfbfbf;
        cursor: move;
    }

    .live-tag {
        display: inline-flex;
        align-items: center;
        padding: 5px 8px;
        font-size: 14px;
        color: #333;
        background: #e5f8f7;
        border-radius: 6px;
    }

    .live-tag-delete-btn {
        margin-left: 4px;
        color: #999999;
        font-size: 12px;
        cursor: pointer;
    }
}

.live-card-wrap {
    position: relative;
    // flex: 0 0 420px;
    width: 100%;
    height: 530px;
    .live-player-video-area {
        position: relative;
        width: 100%;
        height: 100%;
        object-fit: cover;

        .live-player-video {
            display: block;
            width: 100%;
            height: 100%;
            background: #000;
        }

        &::after {
            content: '';
            position: absolute;
            inset: 0;
            background: rgba(0, 0, 0, 0.3);
        }
    }
}

.pre-live-modal-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .pre-live-icon {
        margin: 16px 0;
    }

    .pre-live-modal-title {
        width: 438px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.88);
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-bottom: 8px;
    }
    .pre-live-modal-description {
        width: 374px;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        line-height: 22px;
        text-align: center;
        font-style: normal;
        text-transform: none;
    }
}
.pre-live-modal-footer {
    margin-top: 80px;
}
