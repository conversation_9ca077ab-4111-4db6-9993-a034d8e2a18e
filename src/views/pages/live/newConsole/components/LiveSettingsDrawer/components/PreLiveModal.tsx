/**
 * @Owners felix.cai
 * @Title 直播预告弹窗提醒组件
 */

import { type Console } from '@/api/interface/live/console';
import { updateLiveConfig } from '@/api/modules/live/console';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { useCountDown } from 'ahooks';
import { Button, Modal, Popconfirm, Row } from 'antd';
import { memo, type FunctionComponent, useEffect, useMemo, useRef, useState } from 'react';

interface PreLiveModalProps {
    liveId: number;
    countdownSec: number;
    preLiveInfo: Console.Response.GetRecentlyPrePushPreLive;
    onLiveSettingsDrawerOpen(): void;
}

const PreLiveModal: FunctionComponent<PreLiveModalProps> = ({ preLiveInfo, countdownSec, liveId, onLiveSettingsDrawerOpen }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    /** 是否已经执行过 */
    const isExecutedRef = useRef(false);

    const handleEnd = () => {
        if (!isExecutedRef.current) {
            isExecutedRef.current = true;
        }
    };

    const [_countdown, formattedRes] = useCountDown({
        targetDate: countdownSec,
        onEnd: handleEnd,
    });

    /** 当前时间是否已经过了开播时间 */
    const isPast = useMemo(
        () => formattedRes.hours <= 0 && formattedRes.minutes <= 0 && formattedRes.seconds <= 0,
        [formattedRes]
    );

    useEffect(() => {
        if (isPast && isExecutedRef.current && preLiveInfo && !preLiveInfo?.prePushType) {
            setIsModalOpen(true);
        }
    }, [isPast, preLiveInfo]);

    useEffect(() => {
        if (!isExecutedRef.current && formattedRes?.minutes < 5 && preLiveInfo && !preLiveInfo?.prePushType) {
            setIsModalOpen(true);
            isExecutedRef.current = true;
        }
    }, [formattedRes, preLiveInfo]);

    const handleAutoPush = () => {
        cancel();
        onLiveSettingsDrawerOpen();
    };

    const handleManualPush = async () => {
        try {
            await updateLiveConfig({
                liveId,
                isPrePush: 1,
                prePushType: 1,
            });
            cancel();
            onLiveSettingsDrawerOpen();
        } catch (error) {
            console.error('Error updating live config:', error);
        }
    };

    const footer = useMemo(
        () => (
            <Row justify='center' className='pre-live-modal-footer'>
                {!isPast && (
                    <Button key='auto' onClick={handleAutoPush}>
                        去设置自动推流
                    </Button>
                )}
                <Popconfirm title='确认要开启本场直播吗？' onConfirm={handleManualPush}>
                    <Button key='manual' style={{ marginLeft: 16 }} type='primary'>
                        {isPast ? '立即开播' : '手动立即开播'}
                    </Button>
                </Popconfirm>
            </Row>
        ),
        [handleManualPush, handleAutoPush, isPast]
    );

    const cancel = () => {
        setIsModalOpen(false);
    };

    return (
        <Modal closable open={isModalOpen} footer={footer} onCancel={cancel}>
            <div className='pre-live-modal-wrapper'>
                <div className='pre-live-icon'>
                    <ExclamationCircleFilled style={{ fontSize: 50, color: '#faad14' }} />
                </div>
                {isPast ? (
                    <div className='pre-live-modal-title'>直播时间已到，请点击【立即开播】</div>
                ) : (
                    <div className='pre-live-modal-title'>
                        距开播还有 {formattedRes.hours < 10 ? '0' + formattedRes.hours : formattedRes.hours}:
                        {formattedRes.minutes < 10 ? '0' + formattedRes.minutes : formattedRes.minutes}:
                        {formattedRes.seconds < 10 ? '0' + formattedRes.seconds : formattedRes.seconds}
                        ，开播时需要您手动点击【立即开播】
                    </div>
                )}
                <div className='pre-live-modal-description'>
                    您也可前往【设置-直播推流】开启自动推流，到达直播预告开播时间点，将自动开启直播
                </div>
            </div>
        </Modal>
    );
};

export default memo(PreLiveModal);
