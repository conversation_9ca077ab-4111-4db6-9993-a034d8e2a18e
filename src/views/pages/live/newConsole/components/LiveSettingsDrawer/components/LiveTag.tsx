/**
 * @Owners ljh
 * @Title 标签
 */
import SvgIcon from '@/views/components/common/SvgIcon';
import { CloseOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { type CSSProperties, useCallback, useMemo, useRef } from 'react';
import { type HandlerManager, type XYCoord, useDrag, useDrop } from 'react-dnd';

export interface liveTagItem {
    id: string;
    content: string;
    isGlobal?: number;
}

export interface liveTagProps {
    className?: string;
    style?: CSSProperties;
    /** 是否显示关闭按钮 */
    closeIcon?: boolean;
    item?: liveTagItem;
    /** 索引 */
    index: number;
    /** 移动 */
    onMove?(fromIndex: number, toIndex: number): void;
    /** 删除 */
    onDelete?(index: number, target?: liveTagItem): void;
}

interface LiveDragTag {
    index: number;
    item?: liveTagItem;
}

const LiveTag = (props: liveTagProps) => {
    const { className, style, item, index, closeIcon = true, onMove, onDelete } = props;
    const tagRef = useRef<HTMLDivElement>(null);
    const classes = classNames('live-tag', className);

    const [{ handlerId }, drop] = useDrop<LiveDragTag, void, { handlerId: ReturnType<HandlerManager['getHandlerId']> }>({
        accept: 'box',
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            };
        },
        hover(item: LiveDragTag, monitor) {
            if (!tagRef.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;
            if (dragIndex === hoverIndex) {
                return;
            }

            const hoverBoundingRect = tagRef.current?.getBoundingClientRect();
            const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

            const clientOffset = monitor.getClientOffset();
            const hoverClientX = (clientOffset as XYCoord).x - hoverBoundingRect.left;

            if (dragIndex < hoverIndex && hoverClientX < hoverMiddleX) {
                return;
            }

            if (dragIndex > hoverIndex && hoverClientX > hoverMiddleX) {
                return;
            }

            onMove?.(dragIndex, hoverIndex);
            item.index = hoverIndex;
        },
    });

    const [collected, drag] = useDrag(() => ({
        type: 'box',
        item: () => ({
            id: item?.id,
            index,
        }),
        collect: monitor => ({
            style: {
                opacity: !!monitor.isDragging() ? 0.4 : 1,
            },
        }),
    }));

    const styles = useMemo(
        () => ({
            ...style,
            ...collected.style,
        }),
        [collected.style, style]
    );

    const handleDelete = useCallback(() => {
        if (!closeIcon) return;
        onDelete?.(index, item);
    }, [onDelete, item, index, closeIcon]);

    drag(drop(tagRef));

    return (
        <div ref={tagRef} style={styles} className={classes} data-handler-id={handlerId}>
            <div className='live-tag-holder' ref={drag}>
                <SvgIcon name='holder-outlined' iconStyle={{ width: '12px', height: '12px' }} />
            </div>
            {item?.content}
            {closeIcon && <CloseOutlined className='live-tag-delete-btn' onClick={handleDelete} />}
        </div>
    );
};

export default LiveTag;
