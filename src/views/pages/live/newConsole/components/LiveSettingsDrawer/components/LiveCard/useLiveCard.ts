/**
 * @Owners felix.cai
 * @Title 直播卡片
 */
import { modal } from '@/views/components/UseAppPrompt';
import { cLive } from '@consts';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import { LiveContext } from '../../../../contexts/LiveContext';
import { PushType, useLivePlayer } from '../../../../hooks/useLivePlayer';

export const useLiveCard = ({ videoId, pushUrl }: { videoId: string; pushUrl?: string }) => {
    const [searchParams] = useSearchParams();
    const liveId = Number(searchParams.get('liveId'));

    const { tabActiveKey } = useContext(LiveContext);
    /** 直播状态 */
    const [liveStatus, setLiveStatus] = useState<cLive.PushStatusMap>();

    const {
        createTCPlayer,
        playerInstance,
        errorInfo,
        clearError,
        toggleMuted,
        isMuted,
        handleChangePushStatus,
        isFullscreen,
        togglePlayerFullscreen,
        videoContainerRef,
    } = useLivePlayer(videoId, liveId);

    /** 是否显示播放器 */
    const isShowPlayer = useMemo(() => liveStatus && [cLive.PushStatusMap.Pushing].includes(liveStatus), [liveStatus]);

    useEffect(() => {
        if (isShowPlayer) {
            if (tabActiveKey === cLive.ConsoleInteractiveTabs.Live && pushUrl) {
                if (!playerInstance.current) {
                    setTimeout(() => {
                        createTCPlayer(pushUrl);
                    }, 1000);
                } else {
                    // 重新设置后，它会自动重新拉流和自动播放
                    playerInstance.current?.src(pushUrl);
                }
            } else {
                playerInstance.current?.unload();
            }
        }
    }, [tabActiveKey, pushUrl, isShowPlayer]);

    useEffect(() => {
        // 初始化推流状态
        if (pushUrl !== undefined) {
            setLiveStatus(cLive.PushStatusMap.Pushing);
        }
    }, [pushUrl]);

    useEffect(() => {
        if (pushUrl && liveStatus === cLive.PushStatusMap.Pushing) {
            if (!playerInstance.current) {
                createTCPlayer(pushUrl);
            } else {
                // 重新设置后，它会自动重新拉流和自动播放
                playerInstance.current?.src(pushUrl);
            }
        }

        return () => {
            if (playerInstance?.current?.unload) playerInstance.current.unload();
        };
    }, [pushUrl]);

    /**
     * @description: 暂停直播
     */
    const handleLivePause = () => {
        modal.confirm({
            title: '确定要暂停直播吗？',
            content: '暂停后直播将显示主播暂时离开，点击继续后重新开启直播。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await handleChangePushStatus(PushType.PausePush);
            },
        });
    };

    /**
     * @description: 关闭直播
     */
    const handleLiveClose = () => {
        modal.confirm({
            title: '确定要关闭直播吗？',
            content: '关闭后直播将结束，需要重新发起直播。',
            style: {
                top: '300px',
            },
            mask: false,
            onOk: async () => {
                await handleChangePushStatus(PushType.EndPush);
            },
        });
    };

    /**
     * @description: 恢复直播
     */
    const handleLiveResume = async () => {
        await handleChangePushStatus(PushType.ResumePush);
    };

    /**
     * @description: 刷新直播
     */
    const handleLiveRefresh = async () => {
        clearError();
        if (!pushUrl) return;

        playerInstance.current?.src(pushUrl);
    };

    return {
        videoContainerRef,
        liveStatus,
        isShowPlayer,
        isMuted,
        errorInfo,
        toggleMuted,
        isFullscreen,
        togglePlayerFullscreen,
        handleLivePause,
        handleLiveClose,
        handleLiveResume,
        handleLiveRefresh,
    };
};
