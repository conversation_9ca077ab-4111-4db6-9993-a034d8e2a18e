/**
 * @Owners ljh
 * @Title 快捷评论设置
 */
import { type Console } from '@/api/interface/live/console';
import { getLiveManualComment, updateLiveManualComment } from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import { LiveQuickCommentType } from '@/consts/cLive';
import SvgIcon from '@/views/components/common/SvgIcon';
import { Button, Empty, Input, Popconfirm, Row, Space, Spin } from 'antd';
import classNames from 'classnames';
import { nanoid } from 'nanoid';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

import LiveTag from './LiveTag';

interface KeywordItem {
    content: string;
    id: string;
}

export interface LiveQuickCommentSettingsRef {
    save(): Promise<void>;
    /** 检查是否已编辑 */
    checkIfEdited(): boolean;
}

export interface LiveQuickCommentSettingsProps {
    liveId?: number;
}

const LiveQuickCommentSettings = forwardRef<LiveQuickCommentSettingsRef, LiveQuickCommentSettingsProps>((props, ref) => {
    const { liveId } = props;
    const [loading, setLoading] = useState(false);
    const [inputVal, setInputVal] = useState<string>('');
    /** 已屏蔽关键词 */
    const [keywordList, setKeywordList] = useState<Console.Response.LiveManualCommentRecord[]>([]);
    /** 展开 */
    const [expand, setExpand] = useState(false);
    /** 展开按钮是否显示 */
    const [expandBtnVisible, setExpandBtnVisible] = useState(false);
    /** 是否首次渲染 */
    const isFirstRender = useRef(true);
    /** 是否已编辑 */
    // const isEdited = useRef(false);
    /** 是否存在变更 */
    const [isModified, setIsModified] = useState(false);

    const keywordListBox = useRef<HTMLDivElement>(null);

    /** 保存屏蔽词 */
    const handleSave = useCallback(async () => {
        if (loading || !liveId) return;
        setLoading(true);
        try {
            await updateLiveManualComment({
                liveId,
                preCommentItems: keywordList,
            });
        } finally {
            setLoading(false);
        }
    }, [loading, liveId, keywordList]);

    /** 获取已屏蔽关键词 */
    const fetchLiveBlockKeywords = async () => {
        if (!liveId || loading) return;
        setLoading(true);
        try {
            const res = await getLiveManualComment({
                liveId,
            });

            const keywords = res.data?.preCommentItems ?? [];
            setKeywordList(keywords);
        } finally {
            setLoading(false);
        }
    };

    useImperativeHandle(ref, () => ({
        save: handleSave,
        checkIfEdited: () => isModified,
    }));

    useEffect(() => {
        fetchLiveBlockKeywords();
        const mutationObserver = new MutationObserver(() => {
            if (isFirstRender.current) {
                isFirstRender.current = false;
                setExpand(!!(keywordListBox.current && keywordListBox.current.scrollHeight < 164));
            }
            setExpandBtnVisible(!!(keywordListBox.current && keywordListBox.current.scrollHeight >= 164));
        });

        if (keywordListBox.current) {
            mutationObserver.observe(keywordListBox.current, {
                childList: true, // 监听子元素变化
            });
        }

        return () => {
            mutationObserver.disconnect();
        };
    }, []);

    /** 添加屏蔽词 */
    const handleAddKeywords = useCallback(() => {
        if (loading) return;

        const keyword = inputVal.trim();
        if (!keyword) return;
        setIsModified(true);
        setKeywordList(prev => [
            ...prev,
            {
                isGlobal: LiveQuickCommentType.NonGlobal,
                id: nanoid(6) + `${Math.floor(Math.random() * (1000 - 100)) + 100}`,
                content: keyword,
            },
        ]);
        setInputVal('');
    }, [inputVal, keywordList, loading]);

    /** 删除屏蔽词 */
    const handleDeleteItem = useCallback(
        (index: number, item: KeywordItem) => {
            if (loading) return;
            const { id } = item;
            setIsModified(true);
            setKeywordList(prev => prev.filter(item => item.id !== id));
        },
        [keywordList, loading]
    );

    /** 重置屏蔽词 */
    const handleResetKeywords = useCallback(() => {
        if (loading) return;

        setIsModified(true);
        setKeywordList(prev => prev.filter(item => item.isGlobal === LiveQuickCommentType.Global));
    }, [loading]);

    const handleMove = useCallback((fromIndex: number, toIndex: number) => {
        if (loading) return;

        setIsModified(true);
        setKeywordList(prev => {
            const newList = [...prev];
            newList.splice(fromIndex, 1);
            newList.splice(toIndex, 0, prev[fromIndex]);
            return newList;
        });
    }, []);

    /** 放弃当前更改 */
    const handleResetQuickCommentByCancel = useCallback(() => {
        if (loading) return;
        setIsModified(false);
        fetchLiveBlockKeywords();
    }, [loading]);

    /** 展开 */
    const toggleExpand = useCallback(() => {
        setExpand(prev => !prev);
    }, []);

    return (
        <div className='live-quick-comment-settings live-quick-comment-settings'>
            <div className='lbks-explain'>在直播间添加快捷评论，便于用户快速发送评论；拖动评论可以调整显示顺序</div>
            <Input
                className='lbks-input'
                placeholder='添加1-20字快捷评论'
                allowClear
                disabled={loading}
                value={inputVal}
                onChange={e => {
                    setInputVal(e.target.value);
                }}
                onPressEnter={handleAddKeywords}
                maxLength={20}
                suffix={
                    !!inputVal.length && (
                        <Button onClick={handleAddKeywords} className='lbks-add-btn' type='link' size='small' disabled={loading}>
                            添加
                        </Button>
                    )
                }
            />

            <div className='lbks-section'>
                <div className='lbks-section-header'>
                    <div className='lbks-subtitle'>已设置快捷评论</div>
                    <Popconfirm
                        title='确认要恢复初始状态吗？'
                        description='原来的配置不会保存哦，请谨慎操作。'
                        onConfirm={handleResetKeywords}
                    >
                        <div className='lbks-reset-btn'>重置</div>
                    </Popconfirm>
                </div>
                <Spin spinning={loading}>
                    <DndProvider backend={HTML5Backend}>
                        <div className='lbks-keyword-list' ref={keywordListBox} style={{ maxHeight: expand ? 'none' : '164px' }}>
                            {keywordList?.map((item, index) => (
                                <LiveTag
                                    style={{
                                        backgroundColor: item.isGlobal === LiveQuickCommentType.Global ? '#F7F8F9' : '#E5F8F7',
                                    }}
                                    closeIcon={item.isGlobal !== LiveQuickCommentType.Global}
                                    item={item}
                                    key={item.id}
                                    index={index}
                                    onMove={handleMove}
                                    onDelete={handleDeleteItem}
                                />
                            ))}
                        </div>

                        {!keywordList || !keywordList.length ? (
                            <div className='lbks-empty'>
                                <Empty image={EmptyImg} description='暂无数据' />
                            </div>
                        ) : (
                            expandBtnVisible && (
                                <Button
                                    className={classNames('lbks-expand-btn', {
                                        'lbks-expand-btn--active': expand,
                                    })}
                                    color='default'
                                    variant='link'
                                    size='small'
                                    onClick={toggleExpand}
                                >
                                    {expand ? '收起' : '展开'}
                                    <SvgIcon className='lbks-expand-btn-icon' name='down-outlined' />
                                </Button>
                            )
                        )}
                    </DndProvider>
                </Spin>
            </div>

            {isModified && (
                <Row style={{ marginTop: 32 }} gutter={32} justify='end'>
                    <Space size={16}>
                        <Popconfirm
                            title='确认要放弃当前更改吗？'
                            description='放弃后，当前配置不会保存哦，请谨慎操作。'
                            onConfirm={handleResetQuickCommentByCancel}
                        >
                            <Button type='default' disabled={loading}>
                                取消
                            </Button>
                        </Popconfirm>
                        <Button type='primary' onClick={handleSave} loading={loading}>
                            确定
                        </Button>
                    </Space>
                </Row>
            )}
        </div>
    );
});

export default LiveQuickCommentSettings;
