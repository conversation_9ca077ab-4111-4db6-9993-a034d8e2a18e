/**
 * @Owners ljh
 * @Title 直播播放器
 */
import EmptyImg from '@/assets/images/empty-placeholder.png';
import liveLogo from '@/assets/images/live-logo.png';
import Show from '@/views/components/common/Show';
import SvgIcon from '@/views/components/common/SvgIcon';
import { ExpandOutlined } from '@ant-design/icons';
import { cLive } from '@consts';
import { Button, Empty, Flex } from 'antd';
import classNames from 'classnames';
import 'tcplayer.js/dist/tcplayer.min.css';

import './index.scss';
import { useLiveCard } from './useLiveCard';

const LiveCard = ({ pushUrl }: { pushUrl?: string }) => {
    const {
        videoContainerRef,
        liveStatus,
        isShowPlayer,
        isMuted,
        errorInfo,
        toggleMuted,
        isFullscreen,
        togglePlayerFullscreen,
        // handleLiveResume,
        handleLiveRefresh,
    } = useLiveCard({ videoId: 'preview-live-player', pushUrl });

    return (
        <div className={classNames('preview-live-card-wrap live-card-wrap-dark')}>
            <div className='live-player-container' ref={videoContainerRef}>
                {/* S 视频头部信息条 */}
                <Show when={isFullscreen}>
                    <div className='live-player-top'>
                        <img className='live-player-top-logo' src={liveLogo} alt='远方的梦想' draggable={false} />
                        <div className='live-player-top-info'>
                            <Button
                                className='live-player-exit-fullscreen-btn'
                                variant='solid'
                                color='default'
                                size='small'
                                icon={<SvgIcon name='exit-expand-outlined' />}
                                onClick={togglePlayerFullscreen}
                            >
                                退出
                            </Button>
                        </div>
                    </div>
                </Show>
                {/* E 视频头部信息条 */}
                <div
                    className={classNames('live-player-video-area', {
                        'live-player-video-area--hidden': !isShowPlayer || !!errorInfo,
                    })}
                >
                    <video
                        className='live-player-video'
                        id='preview-live-player'
                        preload='auto'
                        autoPlay
                        playsInline
                        webkit-playsinline='true'
                    />
                </div>
            </div>

            <div className='live-card-context'>
                <div className='live-card-context-video-info'>
                    <Flex className='live-card-control-bar' gap={8} justify='end'>
                        <Show when={isShowPlayer}>
                            <Button
                                size='small'
                                icon={<SvgIcon name={isMuted ? 'muted-outlined' : 'sound-outlined'} />}
                                onClick={toggleMuted}
                            />
                        </Show>

                        <Show when={isShowPlayer}>
                            <Button size='small' icon={<ExpandOutlined />} onClick={togglePlayerFullscreen}>
                                全屏
                            </Button>
                        </Show>
                    </Flex>

                    {/* S 暂停推流状态展示 */}
                    <Show when={!errorInfo && liveStatus === cLive.PushStatusMap.PausePush}>
                        <div className='live-card-context-pause-status'>
                            <div className='live-card-context-pause-status-text'>您已暂停直播</div>
                            {/* <Button type='primary' icon={<SvgIcon name='play-circel-outlined' />} onClick={handleLiveResume}>
                                继续直播
                            </Button> */}
                        </div>
                    </Show>
                    {/* E 暂停推流状态展示 */}
                    {/* S 未直播状态展示 */}
                    <Show when={!!liveStatus && [cLive.PushStatusMap.Expired, cLive.PushStatusMap.StopPush].includes(liveStatus)}>
                        <Empty className='live-card-context-live-empty' image={EmptyImg} description='暂未开直播' />
                    </Show>
                    {/* E 未直播状态展示 */}
                    <Show when={!!errorInfo}>
                        <div className='live-card-context-pause-status'>
                            <div className='live-card-context-pause-status-text'>未拉到流画面</div>
                            <Button type='primary' icon={<SvgIcon name='play-circel-outlined' />} onClick={handleLiveRefresh}>
                                刷新重试
                            </Button>
                        </div>
                    </Show>
                </div>
            </div>
        </div>
    );
};

export default LiveCard;
