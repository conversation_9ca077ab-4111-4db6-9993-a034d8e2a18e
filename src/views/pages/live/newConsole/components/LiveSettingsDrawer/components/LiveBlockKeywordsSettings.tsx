/**
 * @Owners ljh
 * @Title 直播屏蔽词设置
 */
import { getLiveBlockKeywords, updateLiveBlockKeywords } from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import SvgIcon from '@/views/components/common/SvgIcon';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Empty, Input, Popconfirm, Row, Space, Spin } from 'antd';
import classNames from 'classnames';
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';

interface KeywordItem {
    text: string;
    id: number;
}

export interface LiveBlockKeywordsSettingsRef {
    save(): Promise<void>;
    /** 检查是否已编辑 */
    checkIfEdited(): boolean;
}

export interface LiveBlockKeywordsSettingsProps {
    liveId?: number;
}

const LiveBlockKeywordsSettings = forwardRef<LiveBlockKeywordsSettingsRef, LiveBlockKeywordsSettingsProps>((props, ref) => {
    const { liveId } = props;
    const [loading, setLoading] = useState(false);
    const [inputVal, setInputVal] = useState<string>('');
    /** 已屏蔽关键词 */
    const [keywordList, setKeywordList] = useState<KeywordItem[]>([]);
    /** 下一个关键词id */
    const nextKeywordId = useRef(0);
    /** 展开 */
    const [expand, setExpand] = useState(false);
    /** 展开按钮是否显示 */
    const [expandBtnVisible, setExpandBtnVisible] = useState(false);
    /** 是否首次渲染 */
    const isFirstRender = useRef(true);
    /** 是否已编辑 */
    // const isEdited = useRef(false);
    /** 是否存在变更 */
    const [isModified, setIsModified] = useState(false);
    const keywordListBox = useRef<HTMLDivElement>(null);

    /** 保存屏蔽词 */
    const handleSave = useCallback(async () => {
        if (loading || !liveId) return;
        setLoading(true);
        try {
            await updateLiveBlockKeywords({
                liveId,
                keywords: keywordList.map(item => item.text),
            });
        } finally {
            setLoading(false);
        }
    }, [loading, liveId, keywordList]);

    /** 获取已屏蔽关键词 */
    const fetchLiveBlockKeywords = async () => {
        if (loading) return;
        setLoading(true);
        try {
            const res = await getLiveBlockKeywords({
                liveId,
            });

            const keywords = (res?.data?.keywords ?? []).map((item, index) => ({
                text: item,
                id: index,
            }));
            nextKeywordId.current = keywords.length;
            setKeywordList(keywords);
        } finally {
            setLoading(false);
        }
    };

    useImperativeHandle(ref, () => ({
        save: handleSave,
        checkIfEdited: () => isModified,
    }));

    useEffect(() => {
        fetchLiveBlockKeywords();
        const mutationObserver = new MutationObserver(() => {
            if (isFirstRender.current) {
                isFirstRender.current = false;
                setExpand(!!(keywordListBox.current && keywordListBox.current.scrollHeight < 164));
                mutationObserver.disconnect();
            }
            setExpandBtnVisible(!!(keywordListBox.current && keywordListBox.current.scrollHeight >= 164));
        });

        if (keywordListBox.current) {
            mutationObserver.observe(keywordListBox.current, {
                childList: true, // 监听子元素变化
            });
        }

        return () => {
            mutationObserver.disconnect();
        };
    }, []);

    /** 添加屏蔽词 */
    const handleAddKeywords = useCallback(() => {
        if (loading) return;

        const keyword = inputVal.trim();
        if (!keyword) return;
        setIsModified(true);
        setKeywordList(prev => [
            ...prev,
            {
                text: keyword,
                id: nextKeywordId.current++,
            },
        ]);
        setInputVal('');
    }, [inputVal, keywordList, loading]);

    /** 删除屏蔽词 */
    const handleDeleteItem = useCallback(
        (item: KeywordItem) => {
            if (loading) return;
            const { id } = item;
            setIsModified(true);
            setKeywordList(prev => prev.filter(item => item.id !== id));
        },
        [keywordList, loading]
    );

    /** 放弃当前更改 */
    const handleResetKeywordsByCancel = useCallback(() => {
        if (loading) return;
        setIsModified(false);
        // setKeywordList([]);
        fetchLiveBlockKeywords();
    }, [loading]);

    /** 重置屏蔽词 */
    const handleResetKeywords = useCallback(() => {
        if (loading) return;
        setIsModified(true);
        setKeywordList([]);
    }, [loading]);

    /** 展开 */
    const toggleExpand = useCallback(() => {
        setExpand(prev => !prev);
    }, []);

    return (
        <div className='live-block-keywords-settings'>
            <div className='lbks-explain'>
                注意：1.命中关键词的互动消息，不会再直播间公屏展示。2.关键词命中规则：用户发送消息与关键词相同，即算命中。
            </div>
            <Input
                className='lbks-input'
                placeholder='添加1-10字屏蔽关键词'
                allowClear
                disabled={loading}
                value={inputVal}
                onChange={e => {
                    setInputVal(e.target.value);
                }}
                onPressEnter={handleAddKeywords}
                maxLength={10}
                suffix={
                    !!inputVal.length && (
                        <Button onClick={handleAddKeywords} className='lbks-add-btn' type='link' size='small' disabled={loading}>
                            添加
                        </Button>
                    )
                }
            />

            <div className='lbks-section'>
                <div className='lbks-section-header'>
                    <div className='lbks-subtitle'>已屏蔽关键词</div>
                    <Popconfirm
                        title='确认要恢复初始状态吗？'
                        description='原来的配置不会保存哦，请谨慎操作。'
                        onConfirm={handleResetKeywords}
                    >
                        <div className='lbks-reset-btn'>重置</div>
                    </Popconfirm>
                </div>
                <Spin spinning={loading}>
                    <div className='lbks-keyword-list' ref={keywordListBox} style={{ maxHeight: expand ? 'none' : '164px' }}>
                        {keywordList?.map(item => (
                            <div key={item.id} className='live-tag'>
                                {item.text}
                                <CloseOutlined onClick={() => handleDeleteItem(item)} className='live-tag-delete-btn' />
                            </div>
                        ))}
                    </div>

                    {!keywordList || !keywordList.length ? (
                        <div className='lbks-empty'>
                            <Empty image={EmptyImg} description='暂无数据' />
                        </div>
                    ) : (
                        expandBtnVisible && (
                            <Button
                                className={classNames('lbks-expand-btn', {
                                    'lbks-expand-btn--active': expand,
                                })}
                                color='default'
                                variant='link'
                                size='small'
                                onClick={toggleExpand}
                            >
                                {expand ? '收起' : '展开'}
                                <SvgIcon className='lbks-expand-btn-icon' name='down-outlined' />
                            </Button>
                        )
                    )}
                </Spin>
            </div>

            {isModified && (
                <Row style={{ marginTop: 24 }} gutter={32} justify='end'>
                    <Space size={16}>
                        <Popconfirm
                            title='确认要放弃当前更改吗？'
                            description='放弃后，当前配置不会保存哦，请谨慎操作。'
                            onConfirm={handleResetKeywordsByCancel}
                        >
                            <Button type='default' disabled={loading}>
                                取消
                            </Button>
                        </Popconfirm>
                        <Button type='primary' onClick={handleSave} loading={loading}>
                            确定
                        </Button>
                    </Space>
                </Row>
            )}
        </div>
    );
});

export default LiveBlockKeywordsSettings;
