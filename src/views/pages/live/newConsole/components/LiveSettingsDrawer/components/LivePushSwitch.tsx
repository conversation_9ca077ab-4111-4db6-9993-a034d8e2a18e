/**
 * @Owners felix.cai
 * @Title 推流预览
 */

import { CheckOutlined } from '@ant-design/icons';
import { Button, Popconfirm } from 'antd';
import { type CSSProperties } from 'react';

export interface LivePushSwitchProps {
    disabled?: boolean;
    style?: CSSProperties;
    /** 推流类型 1:手动推流 2:自动推流 */
    pushType?: number | null;
    onManualPush?(): void;
    onAutoPush?(): void;
}

const LivePushSwitch = (props: LivePushSwitchProps) => {
    const { style, disabled, onManualPush, onAutoPush, pushType } = props;

    return (
        <div className={`live-push-switch ${disabled ? 'disabled' : ''}`} style={style}>
            <div className='live-push-switch-item manual'>
                <div className='live-push-switch-item-title'>手动推流</div>
                <div className='sub-title'>开播时需手动点击【立即开播】才能开启直播</div>
                <Popconfirm title='确认要开启本场直播吗？' onConfirm={onManualPush}>
                    <Button
                        disabled={disabled || pushType === 1}
                        className='live-push-switch-item-btn'
                        type='primary'
                        ghost
                        size='small'
                    >
                        {pushType === 1 && <CheckOutlined />}
                        {pushType === 1 ? '已开启' : '手动立即开播'}
                    </Button>
                </Popconfirm>
            </div>

            <div className={`live-push-switch-item auto ${pushType === 1 ? 'disabled' : ''}`}>
                <div className='live-push-switch-item-title'>自动推流</div>
                <div className='sub-title'>到达直播预告开播时间点，将自动开启直播</div>
                {/* <Popconfirm title='确认删除？' onConfirm={onAutoPush}> */}
                <Button
                    disabled={disabled}
                    onClick={onAutoPush}
                    className='live-push-switch-item-btn'
                    type='primary'
                    size='small'
                >
                    {pushType === 2 && <CheckOutlined />}
                    {pushType === 2 ? '已开启' : '开启自动推流'}
                </Button>
                {/* </Popconfirm> */}
                {/* 水印 */}
                <div className='live-push-switch-item-watermark' />
            </div>
        </div>
    );
};

export default LivePushSwitch;
