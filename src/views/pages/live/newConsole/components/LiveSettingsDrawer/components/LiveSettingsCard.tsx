/**
 * @Owners ljh
 * @Title 直播设置卡片
 */
import { type CSSProperties, type ReactNode } from 'react';

export interface LiveSettingsCardProps {
    style?: CSSProperties;
    /** 标题 */
    title?: string;
    /** 卡片右上角的操作区域 */
    extra?: ReactNode;
    children?: ReactNode;
}

const LiveSettingsCard = (props: LiveSettingsCardProps) => {
    const { title, extra, children, style } = props;

    return (
        <div className='live-setting-card' style={style}>
            <div className='lsc-header'>
                <div className='lsc-title'>{title}</div>
                {extra}
            </div>
            <div className='lsc-body'>{children}</div>
        </div>
    );
};

export default LiveSettingsCard;
