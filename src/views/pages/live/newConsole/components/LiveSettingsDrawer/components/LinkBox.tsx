/**
 * @Owners ljh
 * @Title 链接卡片
 */

import SvgIcon from '@/views/components/common/SvgIcon';
import { CheckOutlined } from '@ant-design/icons';
import { copyTextToClipboard } from '@utils';
import { Button } from 'antd';
import { throttle } from 'lodash';
import { type CSSProperties, useMemo, useState } from 'react';

export interface LinkBoxProps {
    style?: CSSProperties;
    /** 标题 */
    title?: string;
    /** 链接 */
    link?: string;
}

const LinkBox = (props: LinkBoxProps) => {
    const { style, title, link } = props;

    const [isCopied, setIsCopied] = useState(false);

    const handleCopyText = useMemo(
        () =>
            throttle(async () => {
                if (!link) return;
                await copyTextToClipboard(link);
                setIsCopied(true);
                setTimeout(() => {
                    setIsCopied(false);
                }, 1500);
            }, 1500),
        [link]
    );

    return (
        <div className='live-settings-link-box' style={style}>
            <div className='lslb-title'>{title}</div>
            <div className='lslb-link'>{link}</div>
            {!!link ? (
                <Button
                    className='lslb-copy-btn'
                    size='small'
                    color='default'
                    variant='filled'
                    icon={isCopied ? <CheckOutlined /> : <SvgIcon name='copy-outlined' />}
                    onClick={handleCopyText}
                >
                    复制
                </Button>
            ) : null}
        </div>
    );
};

export default LinkBox;
