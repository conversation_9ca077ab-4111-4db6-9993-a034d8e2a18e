/**
 * @Owners felix.cai
 * @Title 推流预览
 */

import { Popconfirm, Switch } from 'antd';
import classNames from 'classnames';
import { type CSSProperties } from 'react';

export interface LivePushBoxProps {
    style?: CSSProperties;
    disabled?: boolean;
    pushState: boolean;
    onChangePushState(): void;
}

const LivePushBox = (props: LivePushBoxProps) => {
    const { style, disabled, pushState, onChangePushState } = props;

    return (
        <div className={classNames('live-settings-link-box live-push-box', { disabled })} style={style}>
            <div className='lslb-title'>推流预览</div>
            <div className='lslb-link'>
                获取推流地址后，打开右侧“推流预览开关”，再前往OBS完成推流设置并【开启直播】，就可以在右侧预览示例和主播大屏查看推流画面了（直播预览期间，画面仅主播端可见）
            </div>
            <div className='lslb-switch'>
                {pushState ? (
                    <Switch checked={pushState} onChange={onChangePushState} />
                ) : (
                    <Popconfirm title='确认要开启推流预览吗？成功开启后将不支持关闭！' onConfirm={onChangePushState}>
                        <Switch checked={pushState} />
                    </Popconfirm>
                )}
            </div>
        </div>
    );
};

export default LivePushBox;
