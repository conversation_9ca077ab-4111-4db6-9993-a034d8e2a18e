/**
 * @Owners ljh
 * @Title 直播设置
 */
import { updateLiveConfig } from '@/api/modules/live/console';
import { message } from 'antd';
import { memo, type FunctionComponent, useCallback, useEffect, useMemo } from 'react';

import LinkBox from '../components/LinkBox';
import LivePushBox from '../components/LivePushBox';
import LivePushSwitch from '../components/LivePushSwitch';
import '../index.scss';
import { usePreLivePush } from '../usePreLivePush';
export interface LiveSettingsDrawerProps {
    obsDomain?: string;
    obsPushCode?: string;
    liveId: number;
    onPushUrlChange?(pushUrl?: string): void;
    onPushStateChange?(): void;
}

const _PreLivePushCard: FunctionComponent<LiveSettingsDrawerProps> = props => {
    const { obsDomain, obsPushCode, liveId, onPushUrlChange, onPushStateChange } = props;

    const { liveConfig, pushState, pushUrl, isPast, fetchConfig } = usePreLivePush({ liveId });

    useEffect(() => {
        onPushUrlChange?.(pushUrl);
    }, [pushUrl]);

    const disabled = useMemo(
        () => !obsDomain || !obsPushCode || liveConfig?.isPrePush === 0,
        [obsDomain, obsPushCode, liveConfig]
    );

    const handleManualPush = useCallback(async () => {
        if (!obsDomain || !obsPushCode) {
            message.warning('请先获取推流地址！');
            return;
        }
        if (!pushState) {
            message.warning('请先开启推流预览按钮！');
            return;
        }

        if (liveConfig?.prePushType === 1) {
            message.warning('当前已开启手动推流模式，无法再次切换！');
            return;
        }

        try {
            await updateLiveConfig({
                liveId,
                isPrePush: 1,
                prePushType: 1,
            });
            fetchConfig();
            onPushStateChange?.();
        } catch (error) {
            console.error('Error updating live config:', error);
        }
    }, [liveConfig, pushState, obsDomain, obsPushCode, liveId]);

    const handleAutoPush = useCallback(async () => {
        if (!obsDomain || !obsPushCode) {
            message.error('请先获取推流地址');
            return;
        }
        if (!pushState) {
            message.error('请先开启推流预览按钮');
            return;
        }

        if (liveConfig && liveConfig?.prePushType === 1) {
            message.error('当前已开启手动推流模式，无法切换自动推流');
            return;
        }

        if (isPast) {
            message.error('直播预告已经到达开播时间，不允许设置自动推流');
            return;
        }

        const prePushType = liveConfig?.prePushType === 2 ? 0 : 2;

        try {
            await updateLiveConfig({
                liveId,
                isPrePush: 1,
                prePushType,
            });

            fetchConfig();
            onPushStateChange?.();

            if (prePushType === 2) {
                message.success('已开启自动推流，到达直播预告的开播时间点，将自动开启直播');
            } else {
                message.success('已关闭自动推流,要开播时记得点击下方【立即开播】哦！');
            }
        } catch (error) {
            console.error('Error updating live config:', error);
        }
    }, [isPast, liveConfig, pushState, obsDomain, obsPushCode, liveId]);

    const handleChangePushState = useCallback(async () => {
        try {
            if (!obsDomain || !obsPushCode) {
                message.warning('请先获取推流地址！');
                return;
            }

            if (liveConfig?.isPrePush === 1) {
                message.warning('开启直播推流预览后，不支持关闭！');
                return;
            }

            await updateLiveConfig({
                liveId,
                isPrePush: liveConfig?.isPrePush === 1 ? 0 : 1,
            });
            fetchConfig();
            onPushStateChange?.();
        } catch (error) {
            console.error('Error updating live config:', error);
        }
    }, [liveId, liveConfig, obsDomain, obsPushCode]);

    return (
        <div>
            <div className='live-settings-drawer-left-content'>
                <LinkBox title='OBS服务器:' link={obsDomain} />
                <LinkBox title='OBS推流码:' link={obsPushCode} />
                <LivePushBox disabled={disabled} pushState={pushState} onChangePushState={handleChangePushState} />
            </div>
            <LivePushSwitch
                disabled={disabled}
                pushType={liveConfig?.prePushType}
                onManualPush={handleManualPush}
                onAutoPush={handleAutoPush}
            />
        </div>
    );
};

export default memo(_PreLivePushCard);
