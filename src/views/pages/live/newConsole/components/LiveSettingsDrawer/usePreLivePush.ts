/**
 * @Owners felix.cai
 * @Title 获取直播地址
 */

import { type Console } from '@/api/interface/live/console';
import {
    getLiveConfig,
    getLiveConfig as _fetchLiveConfig,
    getPlayUrl,
    getRecentlyPrePushPreLive,
    updateLiveConfig,
} from '@/api/modules/live/console';
import { useCallback, useEffect, useMemo, useState } from 'react';

export const usePreLivePush = ({ liveId }: { liveId: number }) => {
    const [liveConfig, setLiveConfig] = useState<Console.Response.GetLiveConfig>();
    const [pushUrl, setPushUrl] = useState<string>();
    const [pushState, setPushState] = useState<boolean>(false);
    const [preLiveInfo, setPreLiveInfo] = useState<Console.Response.GetRecentlyPrePushPreLive>();

    const fetchConfig = useCallback(async () => {
        try {
            const res = await getLiveConfig({ liveId });
            setLiveConfig(res?.data);

            setPushState(res?.data?.isPrePush === 1);

            if (res?.data?.isPrePush === 1) {
                const res = await getPlayUrl({ liveId });
                setPushUrl(res?.data?.webRtcAuto);
            }
            fetchPrePushPreLive();
        } catch (error) {
            console.error('Error fetching live config:', error);
        }
    }, [liveId]);

    const fetchPrePushPreLive = async () => {
        const resp = await getRecentlyPrePushPreLive();
        setPreLiveInfo(resp?.data);
    };

    const updateConfig = useCallback(
        async (newConfig: Partial<Console.Response.GetLiveConfig>) => {
            try {
                await updateLiveConfig({ liveId, ...newConfig });
                fetchConfig();
                // setLiveConfig(newConfig);
                // if (typeof newConfig?.isPrePush === 'number') setPushState(newConfig?.isPrePush === 1);
            } catch (error) {
                console.error('Error updating live config:', error);
            }
        },
        [liveId]
    );

    useEffect(() => {
        fetchConfig();
    }, [fetchConfig]);

    /** 是否已经过了开播时间 */
    const isPast = useMemo(() => {
        if (!preLiveInfo) return false;
        const { preStartTimeSec = 0 } = preLiveInfo;
        return preStartTimeSec === 0;
    }, [preLiveInfo]);

    return {
        isPast,
        pushState,
        preLiveInfo,
        pushUrl,
        liveConfig,
        fetchConfig,
        updateConfig,
    };
};
