/**
 * @Owners ljh
 * @Title 获取直播地址
 */

import { getStreamUrl } from '@/api/modules/live/console';
import { throttle } from 'lodash';
import { useContext, useMemo } from 'react';

import { LiveContext } from '../../contexts/LiveContext';

export const usePushUrl = () => {
    const { liveDetail, getLiveDetail } = useContext(LiveContext);

    /** OBS推流地址 */
    const obsDomain = useMemo(() => liveDetail?.pushUrl?.obsDomain, [liveDetail?.pushUrl?.obsDomain]);
    /** OBS推流码 */
    const obsPushCode = useMemo(() => liveDetail?.pushUrl?.obsPushCode, [liveDetail?.pushUrl?.obsPushCode]);

    /** 获取推流地址 */
    const handleGetPushUrl = useMemo(
        () =>
            throttle(async () => {
                if (!!obsDomain && !!obsPushCode) return;
                await getStreamUrl({
                    id: liveDetail?.id,
                });
                if (getLiveDetail) {
                    getLiveDetail();
                }
            }, 2000),
        [liveDetail?.id, getLiveDetail, obsDomain, obsPushCode]
    );

    return {
        obsPushCode,
        obsDomain,
        handleGetPushUrl,
    };
};
