/**
 * @Owners ljh
 * @Title 直播设置
 */
import EmptyImg from '@/assets/images/empty-placeholder.png';
import { But<PERSON>, Drawer, Empty } from 'antd';
import { forwardRef, useCallback, useContext, useImperativeHandle, useRef, useState } from 'react';

import { LiveContext } from '../../contexts/LiveContext';
import PreviewCarousel from '../PreviewCarousel';

import LiveBlockKeywordsSettings, { type LiveBlockKeywordsSettingsRef } from './components/LiveBlockKeywordsSettings';
import LiveCard from './components/LiveCard';
import LiveQuickCommentSettings, { type LiveQuickCommentSettingsRef } from './components/LiveQuickCommentSettings';
import LiveSettingsCard from './components/LiveSettingsCard';
import PreLivePushCard from './components/PreLivePushCard';
import './index.scss';
import { usePushUrl } from './usePushUrl';

const previewCarouselList = [
    {
        image: 'https://applet.ifengqun.com/fq-live-web/images/live-setting-preview1.png',
        title: '用户端快速评论效果',
    },
    {
        image: 'https://applet.ifengqun.com/fq-live-web/images/live-setting-preview2.png',
        title: '用户端快速评论效果',
    },
    {
        image: 'https://applet.ifengqun.com/fq-live-web/images/live-setting-preview3.png',
        title: '用户端屏蔽词效果',
    },
];

export interface LiveSettingsDrawerRef {
    open(): void;
}

export interface LiveSettingsDrawerProps {
    handlePushStateChange?(): void;
}

const LiveSettingsDrawer = forwardRef<LiveSettingsDrawerRef, LiveSettingsDrawerProps>(({ handlePushStateChange }, ref) => {
    const { liveDetail } = useContext(LiveContext);
    const { obsDomain, obsPushCode, handleGetPushUrl } = usePushUrl();
    const [visible, setVisible] = useState(false);
    /** 保存中 */
    // const [loading, setLoading] = useState(false);
    /** 屏蔽关键词设置 */
    const liveBlockKeywordsSettingsRef = useRef<LiveBlockKeywordsSettingsRef>(null);
    /** 快捷评论设置 */
    const liveQuickCommentSettingsRef = useRef<LiveQuickCommentSettingsRef>(null);
    /** 推流地址 */
    const [prePushUrl, setPrePushUrl] = useState<string>();

    useImperativeHandle(ref, () => ({
        open() {
            setVisible(true);
        },
    }));

    /** 关闭 */
    const handleClose = useCallback(() => {
        // if (liveBlockKeywordsSettingsRef.current?.checkIfEdited() || liveQuickCommentSettingsRef.current?.checkIfEdited()) {
        //     modal.confirm({
        //         title: '当前修改未保存，确认保存吗？',
        //         okText: '保存',
        //         onOk: () => {
        //             handleSave();
        //         },
        //         onCancel: () => {
        //             setVisible(false);
        //         },
        //     });
        // } else {
        setVisible(false);
        // }
    }, []);

    /** 保存 */
    // const handleSave = useCallback(async () => {
    //     if (loading) return;
    //     setLoading(true);
    //     try {
    //         await Promise.all([liveBlockKeywordsSettingsRef.current?.save(), liveQuickCommentSettingsRef.current?.save()]);
    //         message.success('保存成功');
    //         setVisible(false);
    //     } finally {
    //         setLoading(false);
    //     }
    // }, [loading, handleClose]);

    const handlePushUrlChange = useCallback((pushUrl?: string) => {
        setPrePushUrl(pushUrl);
    }, []);

    return (
        <Drawer
            className='live-settings-drawer'
            open={visible}
            onClose={handleClose}
            title='直播设置'
            width={1400}
            // footer={renderFooter}
            footer={null}
            destroyOnClose
        >
            <div className='live-settings-drawer-left'>
                <LiveSettingsCard
                    title='直播地址'
                    extra={
                        <Button type='primary' variant='outlined' ghost size='small' onClick={handleGetPushUrl}>
                            获取推流地址
                        </Button>
                    }
                >
                    <PreLivePushCard
                        obsDomain={obsDomain}
                        obsPushCode={obsPushCode}
                        liveId={liveDetail?.id as number}
                        onPushUrlChange={handlePushUrlChange}
                        onPushStateChange={handlePushStateChange}
                    />
                </LiveSettingsCard>

                <LiveSettingsCard title='设置屏蔽关键词'>
                    <LiveBlockKeywordsSettings ref={liveBlockKeywordsSettingsRef} liveId={liveDetail?.id} />
                </LiveSettingsCard>

                <LiveSettingsCard title='设置快捷评论'>
                    <LiveQuickCommentSettings ref={liveQuickCommentSettingsRef} liveId={liveDetail?.id} />
                </LiveSettingsCard>
            </div>
            <div className='live-settings-drawer-right'>
                <div className='title'>直播预览</div>
                <div className='top'>
                    {prePushUrl && <LiveCard pushUrl={prePushUrl} />}
                    {!prePushUrl && (
                        <div className='no-push-url'>
                            <Empty
                                image={EmptyImg}
                                style={{ margin: '0px auto 24px' }}
                                description='推流预览打开后可看到直播预览'
                            />
                        </div>
                    )}
                    <div className='footer-text'>
                        <div className='title'>直播推流后的效果</div>
                    </div>
                </div>
                <div className='bottom'>
                    <div className='title'>用户预览示例</div>
                    <PreviewCarousel autoplay={false} dataSource={previewCarouselList} />
                </div>
            </div>
        </Drawer>
    );
});

export default LiveSettingsDrawer;
