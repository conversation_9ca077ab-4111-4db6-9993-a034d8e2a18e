/**
 * @Owners xj
 * @Title 修改直播间信息
 */

import { getStreamUrl } from '@/api/modules/live/console';
import { cLive } from '@consts';
import { Button, Col, Modal, Popconfirm, Row, Typography } from 'antd';
import { type FC } from 'react';

import { type Dataitem } from '../index';
interface IProps {
    showModel: boolean;
    dataSource?: Dataitem;
    onModelClose(): void;
    reload(): void;
}

const LiveAddress: FC<IProps> = props => {
    const { showModel, dataSource, onModelClose, reload } = props;
    const { pushUrl } = dataSource || {};

    const handleGetStream = async () => {
        if (dataSource) {
            await getStreamUrl({
                id: dataSource.id,
            });
            reload();
        }
    };

    return (
        <Modal
            title='直播地址'
            open={showModel}
            onCancel={onModelClose}
            width={600}
            footer={[
                <Button key='back' onClick={onModelClose}>
                    返回
                </Button>,
                dataSource &&
                dataSource.liveCategory === 2 &&
                !pushUrl &&
                [cLive.PushStatusMap.NoStart, cLive.PushStatusMap.NoLive].includes(dataSource.pushStatus) ? (
                    <Popconfirm
                        title='提示'
                        description='该推流有效期为48小时，建议在开播前进行获取，超过48小时后则该推流失效，需要重新创建直播间'
                        okText='继续获取'
                        cancelText='暂不获取'
                        onConfirm={handleGetStream}
                    >
                        <Button type='primary'>获取流地址</Button>
                    </Popconfirm>
                ) : (
                    ''
                ),
            ]}
        >
            <Row>
                <Col span='6'>OBS服务器：</Col>
                <Col span='13'>{pushUrl?.obsDomain || '-'}</Col>
                <Col span='4' offset={1}>
                    <Typography.Paragraph copyable={{ text: pushUrl?.obsDomain, icon: '复制' }} />
                </Col>
            </Row>
            <Row>
                <Col span='6'>OBS推流码：</Col>
                <Col span='13'>{pushUrl?.obsPushCode || '-'}</Col>
                <Col span='4' offset={1}>
                    <Typography.Paragraph copyable={{ text: pushUrl?.obsPushCode, icon: '复制' }} />
                </Col>
            </Row>
        </Modal>
    );
};

export default LiveAddress;
