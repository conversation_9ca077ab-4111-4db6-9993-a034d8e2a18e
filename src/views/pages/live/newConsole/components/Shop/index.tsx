/**
 * @Owners lzy
 * @Title 用户列表
 */
import { type Console } from '@/api/interface/live/console';
import { Tabs, type TabsProps } from 'antd';
import React, { useState } from 'react';

import NoticeSettings from '../NoticeSettings';

import Data from './Data';
import LiveGoodsList from './LiveGoodsList';
import './index.scss';

type Props = {
    isLiveEnd: boolean;
    liveId: number;
    liveDetail?: Console.Response.LiveDetailInfo;
};

const App: React.FC<Props> = ({ isLiveEnd, liveId, liveDetail }) => {
    const [activeKey, setActiveKey] = useState('1');
    const onChange = (key: string) => {
        setActiveKey(key);
    };

    const items: TabsProps['items'] = [
        {
            key: '1',
            label: '商品',
            children: <LiveGoodsList liveDetail={liveDetail} isLiveEnd={isLiveEnd} />,
        },
        {
            key: '2',
            label: '数据',
            children: <Data liveId={liveId} isLiveEnd={isLiveEnd} />,
        },
        {
            key: '3',
            label: '消息通知',
            children: <NoticeSettings />,
        },
    ];

    return <Tabs defaultActiveKey={activeKey} items={items} onChange={onChange} className='shopTabs' />;
};

export default App;
