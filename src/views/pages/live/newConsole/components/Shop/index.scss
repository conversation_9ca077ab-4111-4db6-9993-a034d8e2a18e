:where(.css-dev-only-do-not-override-1md34ks).ant-tabs-top>.ant-tabs-nav::before {
    border-bottom: none;
}

.shopContainer {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.shopTabs {
    border-radius: 6px;

    .ant-tabs-nav {
        background-color: #fff;
    }

    // padding: 24px;
    // padding-right: 0px;
    // padding-top: 0px;
    flex: 1;
    overflow: auto;

    // .ant-tabs-nav::before {
    //     border-bottom: none;
    //     // display: none;
    // }
    .userList {
        // height: 400px;
        box-sizing: border-box;
        // margin-right: 24px;
        // padding-right: 24px;
    }

    .userListItem {
        padding: 12px;

        .btn {
            display: none;
        }

        .tag {
            margin: 0 4px;
        }

        .title {
            display: flex;
            align-items: center;
        }

        .img {
            width: 16px;
            height: 16px;
        }
    }

    .userListItem:hover {
        /* 中性色/背景/bg02 */
        background: #f7f8f9;
        border-radius: 6px;

        .btn {
            display: inline-block;
        }

        .img {
            display: none;
        }
    }

    .liveGoodsListTab {
        .ant-tabs-tab {
            font-size: 16px !important;
        }

        .ant-tabs-nav {
            padding: 0;
            font-weight: normal;

            &::before {
                border-bottom: none;
                // display: none;
            }
        }

        .ant-tabs-tab-active {
            font-weight: 600;
        }

        .ant-tabs-ink-bar {
            display: none;
        }
    }

    #echatmain {
        padding: 24px;
    }
}