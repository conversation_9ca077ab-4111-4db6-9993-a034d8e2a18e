.live-good-table {
    .ant-table-title {
        padding-left: 0px !important;
        padding-right: 0px !important;
        padding-top: 0px !important;
    }

    .goods-info {
        position: relative;

        .good-img-box {
            min-width: 65px;
            height: 65px;
            position: relative;
            border-radius: 4px 4px 4px 4px;
            overflow: hidden;
        }

        .goods-pinned-info {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
        }

        .red-border {
            border: 2px solid #FF4D4F;
        }

        .origin-border {
            border: 2px solid #FF8F16;

            .explaining {
                background-color: #FF8F16;
            }
        }

        .explaining {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(135deg, #FD4A39 0%, #FD397E 100%);
            color: #ffffff;
            text-align: center;
            font-weight: 400;
            font-size: 10px;
            border-radius: 2px 2px 0 0;
        }

        .goods-info-box {
            margin-left: 10px;
            text-align: left;

            .spuName {
                max-width: 100%;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }

            .selling {
                margin-top: 4px;
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #d46b08;
                line-height: 18px;
                text-align: left;
            }
        }

        .short-title-bar {
            flex: 1;
            display: flex;
            align-items: center;

            margin-bottom: 4px;

            .short-title-text {
                // max-width: 240px;
                font-weight: 600;
                // max-width: 320px;

                max-width: 100%;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;

                // text-overflow: ellipsis; //显示省略符号来代表被修剪的文本
                // white-space: nowrap;
                // overflow: hidden; //招出部分隐藏
            }

            .edit-short-title-box {
                display: flex;
                align-items: center;
            }
        }

        .short-title-icon {
            color: #509863;
            cursor: pointer;
            margin-left: 5px;
            padding: 0 5px;
        }
    }

    .handle-btn {
        padding: 0 8px;
    }

    .goods-coupons-list {


        .goods-coupons-item {
            display: flex;
            align-items: center;
            height: 24px;
            padding: 0 4px;
            margin-right: 4px;
            box-sizing: border-box;
            font-size: 12px;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            color: #FF3550;
            white-space: nowrap;
            width: max-content;
            max-width: 100%;
            box-sizing: border-box;
            // overflow: hidden;
            position: relative;

            &:after {
                position: absolute;
                content: "";
                top: 0;
                left: 0;
                box-sizing: border-box;
                width: 200%;
                height: 200%;
                transform: scale(0.5);
                transform-origin: left top;
                pointer-events: none;
                border: 1PX solid rgba(255, 53, 80, 0.6);
                border-radius: 8px;
            }

            .goods-coupons-item-label {
                position: relative;
                padding-right: 4px;
                padding-left: 4px;

                &::after {
                    position: absolute;
                    content: "";
                    top: 0;
                    left: 0;
                    box-sizing: border-box;
                    width: 200%;
                    height: 200%;
                    transform: scale(0.5);
                    transform-origin: left top;
                    pointer-events: none;
                    border-right: 1PX dashed #FF3550;
                }
            }

            .goods-coupons-item-text {
                flex: 1;
                margin-right: 4px;
                overflow: hidden;
                box-sizing: border-box;
                padding-right: 0px;
                padding-left: 4px;
                max-width: 12em;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .goods-coupons-item-box {
            display: flex;
            align-items: center;
            background: #F7F8F9;
            border-radius: 4px 4px 4px 4px;
            margin-bottom: 6px;
            padding: 4px 6px;
            width: fit-content;

            &.normal {
                background: #fff;
            }

            .left-time {
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #666666;
                line-height: 14px;
            }

            .col-red {
                color: #FF4D4F;
                padding-left: 3px;
            }
        }
    }
}

.goods-sort-select {
    .ant-select-selection-placeholder {
        color: rgba(0, 0, 0, 0.88);
    }

    .ant-select-item-option {
        &:hover {
            background-color: #F7F8F9 !important;
            color: #00B68F;
        }
    }
}

.live-goods-list-sort-radio {
    .ant-radio-wrapper {
        margin-bottom: 0;
    }
}