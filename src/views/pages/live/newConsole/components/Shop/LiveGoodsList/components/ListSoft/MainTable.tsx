/**
 * @Owners lzy
 * @Title 排序头部列表
 */

import { type Console } from '@/api/interface/live/console';
import { HolderOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
// eslint-disable-next-line @typescript-eslint/tslint/config
import { DndContext } from '@dnd-kit/core';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button, Flex, Input, Popconfirm, Table, Tooltip } from 'antd';
// eslint-disable-next-line @typescript-eslint/tslint/config
import type { TableColumnsType } from 'antd';
import { cloneDeep } from 'lodash';
import React, { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

type DataType = Console.Response.LiveGoodsCategoryVO;

interface RowContextProps {
    listeners?: SyntheticListenerMap;
    setActivatorNodeRef?(element: HTMLElement | null): void;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC<{ index: number }> = ({ index }) => {
    const { setActivatorNodeRef, listeners } = useContext(RowContext);
    return (
        <Button
            type='text'
            size='small'
            icon={<HolderOutlined />}
            style={{ cursor: index !== 0 ? 'move' : 'no-drop', opacity: index !== 0 ? undefined : 0.3 }}
            ref={setActivatorNodeRef}
            {...listeners}
        />
    );
};
const newData: DataType = {
    id: undefined,
    categoryName: '',
    sort: 0,
};
const initialData: DataType[] = [
    { id: 1, categoryName: 'John Brown', sort: 32 },
    { id: 2, categoryName: 'Jim Green', sort: 42 },
    { id: 3, categoryName: 'Joe Black', sort: 32 },
];

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
    'data-row-key': number;
}

const Row: React.FC<RowProps> = props => {
    const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
        id: props['data-row-key'],
    });

    const style: React.CSSProperties = {
        ...props.style,
        transform: CSS.Translate.toString(transform),
        transition,
        ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
    };

    const contextValue = useMemo<RowContextProps>(() => ({ setActivatorNodeRef, listeners }), [setActivatorNodeRef, listeners]);

    return (
        <RowContext.Provider value={contextValue}>
            <tr {...props} ref={setNodeRef} style={style} {...attributes} />
        </RowContext.Provider>
    );
};
export type MainTableRef = {
    getData(): DataType[];
};
type MainTableProps = {
    data: DataType[];
    open: boolean;
};
const App = forwardRef<MainTableRef, MainTableProps>(({ data, open }, ref) => {
    const [dataSource, setDataSource] = useState<DataType[]>(initialData);
    const indexRef = useRef(-1);

    useEffect(() => {
        setDataSource(cloneDeep(data));
    }, [open]);

    useImperativeHandle(ref, () => ({
        getData: () => dataSource,
    }));

    const columns: TableColumnsType<DataType> = [
        {
            key: 'index',
            align: 'center',
            width: 32,
            title: '',
            render: (_text: number, _item, index) => <DragHandle index={index} />,
        },
        {
            key: 'index',
            // align: 'center',
            width: 120,
            title: '序号',
            render: (_text: number, _item, index) => <span style={{ marginLeft: '4px' }}>{index + 1}</span>,
        },
        {
            title: '商品类型',
            dataIndex: 'categoryName',
            render: (_text, item, index) => (
                <Input
                    placeholder='请输入'
                    value={item.categoryName}
                    onChange={e => {
                        handleChangeName(index, e.target.value);
                    }}
                    maxLength={10}
                    disabled={index === 0}
                    showCount
                />
            ),
        },
        {
            title: '操作',
            key: 'delete',
            width: 150,
            dataIndex: 'delete',
            fixed: 'right',
            align: 'center',
            render: (_text: number, _item, index) => (
                <Popconfirm title='确定删除该商品类型吗？' onConfirm={() => handleDelete(index)}>
                    {index === 0 ? (
                        <Tooltip placement='top' title={'「全部」商品类型不支持操作'}>
                            <Button color='danger' variant='link' disabled={index === 0}>
                                删除
                            </Button>
                        </Tooltip>
                    ) : (
                        <Button color='danger' variant='link'>
                            删除
                        </Button>
                    )}
                </Popconfirm>
            ),
        },
    ];

    const onDragEnd = ({ active, over }: DragEndEvent) => {
        if (over?.id === dataSource[0]?.id || active?.id === dataSource[0]?.id) return;

        if (active.id !== over?.id) {
            setDataSource(prevState => {
                const activeIndex = prevState.findIndex(record => record.id === active?.id);
                const overIndex = prevState.findIndex(record => record.id === over?.id);
                return arrayMove(prevState, activeIndex, overIndex);
            });
        }
    };

    const handleDelete = (index: number) => {
        dataSource.splice(index, 1);
        setDataSource([...dataSource]);
    };

    const handleAdd = () => {
        indexRef.current = indexRef.current - 1;
        // console.log(indexRef.current);

        setDataSource([
            ...dataSource,
            {
                ...cloneDeep(newData),
                id: indexRef.current,
            },
        ]);
    };

    const handleChangeName = (index: number, value: string) => {
        dataSource[index].categoryName = value;
        setDataSource([...cloneDeep(dataSource)]);
    };

    return (
        <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
            <SortableContext
                items={dataSource.map((i, index) => (index === 0 ? 0 : i.id || 0))}
                strategy={verticalListSortingStrategy}
            >
                <Table<DataType>
                    title={() => (
                        <Flex justify='space-between' align='center'>
                            <span
                                style={{
                                    fontFamily: 'PingFang SC, PingFang SC',
                                    fontWeight: 500,
                                    fontSize: '16px',
                                    color: '#333333',
                                }}
                            >
                                商品类型列表
                            </span>
                            <Button color='primary' variant='outlined' onClick={handleAdd}>
                                新建商品类型
                            </Button>
                        </Flex>
                    )}
                    rowKey='id'
                    components={{
                        body: {
                            row: Row,
                        },
                    }}
                    scroll={{
                        y: '74vh',
                    }}
                    columns={columns}
                    dataSource={dataSource}
                    pagination={false}
                />
            </SortableContext>
        </DndContext>
    );
});

export default App;
