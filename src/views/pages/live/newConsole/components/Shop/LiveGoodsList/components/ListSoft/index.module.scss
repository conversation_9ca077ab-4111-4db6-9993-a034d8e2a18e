.header {
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    .left {
        flex: 1;
        overflow-x: auto;
    }
    .rightBtn {
        flex: 0 0 auto;
        color: #00b68f;
    }
    :global {
        .ant-segmented-item-selected {
            color: #00b68f;
        }
    }
}
.container {
    display: flex;
    flex-direction: column;
    justify-content: space-between !important;
    height: 100%;
    .main {
        height: 85vh;
        overflow: hidden;
        :global {
            .ant-table-title {
                padding-top: 24px !important;
            }
        }
    }
    :global {
        .ant-drawer-body {
            padding-top: 0px;
        }
    }
    .flotter {
        margin-top: 24px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 12px;
    }
}
