/**
 * @Owners lzy
 * @Title 排序头部列表
 */

import { type Console } from '@/api/interface/live/console';
import { getAllGoodsCategory, updateAllGoodsCategory } from '@/api/modules/live/console';
import { modal } from '@/views/components/UseAppPrompt';
import { EditOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { Button, Drawer, Flex, Segmented, message } from 'antd';
import { cloneDeep, isEqual } from 'lodash';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

import MainTable, { type MainTableRef } from './MainTable';
import style from './index.module.scss';

type Props = {
    // isLiveEnd: boolean;
    liveId: number;
    onChange?(value: number): void;
    onChangeDone?(value: number): void;
};

export type ListSoftRef = {
    handleGetAllGoodsCategory(): void;
};

type LiveGoodsCategory = Console.Response.LiveGoodsCategoryVO;

const initData = {
    categoryName: '全部',
    id: 0,
    sort: 0,
};

const Index = forwardRef<ListSoftRef, Props>(({ liveId, onChange, onChangeDone }, ref) => {
    const [loading, setLoading] = useState(false);
    // const [editableStr, setEditableStr] = useState('This is an editable text.');
    const [goodsCategory, setGoodsCategory] = useState<LiveGoodsCategory[]>([initData]);
    const [open, setOpen] = useState(false);
    const [categoryId, setCategoryId] = useState(0);

    const mainTableRef = useRef<MainTableRef | null>(null);

    useImperativeHandle(ref, () => ({
        handleGetAllGoodsCategory,
    }));

    useEffect(() => {
        handleGetAllGoodsCategory();
    }, []);

    const handleGetAllGoodsCategory = () => {
        getAllGoodsCategory({ liveId }).then(res => {
            setGoodsCategory([initData, ...(res.data || [])]);
            setLoading(false);
        });
    };

    const handleChangeSoft = (value: number) => {
        setCategoryId(value);
        onChange && onChange(value);
    };

    const handleShowDrawer = () => {
        setOpen(true);
    };

    const handleOnClose = () => {
        if (!isEqual(goodsCategory, mainTableRef.current?.getData() || [])) {
            return modal.confirm({
                // title: '是否保存当前话术？',
                icon: <ExclamationCircleFilled />,
                content: '确定退出编辑？退出后已修改的内容将不保存',
                onOk() {
                    // console.log('OK');
                    // handleUpdateAllGoodsCategory();
                    setOpen(false);
                },
                onCancel() {},
                okText: '确认',
                cancelText: '取消',
            });
        }
        setOpen(false);
    };

    const handleUpdateAllGoodsCategory = () => {
        const liveGoodsCategoryList = cloneDeep(mainTableRef.current?.getData() || []);
        // eslint-disable-next-line @typescript-eslint/prefer-for-of
        for (let index = 0; index < liveGoodsCategoryList.length; index++) {
            const element = liveGoodsCategoryList[index];
            if (!element.categoryName) {
                return message.error('请填写所有商品类型');
            }
        }
        setLoading(true);
        liveGoodsCategoryList.splice(0, 1);
        liveGoodsCategoryList.forEach(_item => {
            _item.sort = undefined;
            if (_item.id !== undefined && _item.id < 0) {
                _item.id = undefined;
            }
        });
        // console.log(liveGoodsCategoryList);

        updateAllGoodsCategory({
            liveGoodsCategoryList,
            liveId,
        })
            .then(() => {
                setOpen(false);
                setCategoryId(0);
                handleGetAllGoodsCategory();
                onChangeDone && onChangeDone(0);
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <div>
            <Flex
                gap='small'
                style={{
                    background: 'rgb(250, 250, 250)',
                    borderRadius: '6px',
                }}
                justify={'space-between'}
                align={'center'}
                className={style.header}
            >
                <Segmented
                    style={{
                        background: 'rgb(250, 250, 250)',
                        padding: '6px',
                    }}
                    className={style.left}
                    value={categoryId}
                    onChange={handleChangeSoft}
                    options={goodsCategory.map(item => ({
                        value: item.id || 0,
                        label: item.categoryName,
                    }))}
                />
                <Button type='text' className={style.rightBtn} onClick={handleShowDrawer}>
                    <EditOutlined /> 编辑
                </Button>
            </Flex>
            <Drawer title='修改商品类型' onClose={handleOnClose} open={open} width={900} className={`${style.container}`}>
                <div className={style.main}>
                    <MainTable data={goodsCategory} open={open} ref={mainTableRef} />
                </div>
                <div className={style.flotter}>
                    <Button loading={loading} color='default' variant='outlined' disabled={loading} onClick={handleOnClose}>
                        取消
                    </Button>
                    <Button
                        onClick={handleUpdateAllGoodsCategory}
                        loading={loading}
                        type='primary'
                        disabled={loading}
                        // icon={<img src={touchIcon} className={style.img} />}
                    >
                        确认
                    </Button>
                </div>
            </Drawer>
        </div>
    );
});

export default Index;
