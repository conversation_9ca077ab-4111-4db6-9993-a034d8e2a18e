/**
 * @Owners blv
 * @Title 创建成功组件
 */
import { type Goods } from '@/api/interface/goods';
import { type Console } from '@/api/interface/live/console';
import {
    addLiveGoods,
    cancelSayLiveGoods,
    closeNoticeFromApi,
    deleteLiveGoods,
    findLiveGoodsByPage,
    getLiveGoodsCount,
    goodsSkuPriceMgrOnAndOff,
    importLiveGoods,
    openNoticeFromApi,
    optLiveGoods,
    publishLiveGoods,
    resetLiveGoodsSerialNo,
    saySortLiveGoods,
    setLiveGoodsCustomSpuName,
    setLiveGoodsSort,
    setLiveGoodsTop,
    sortLiveGoods,
} from '@/api/modules/live/console';
import { findUserLivePermissions } from '@/api/modules/live/create';
import refreshIcon from '@/assets/images/refreshIcon.png';
import useCountDown from '@/hooks/useCountDown';
import { uNumber } from '@/utils/uNumber';
// import { uDecimal } from '@/utils/uDecimal';
import { SelectGoodsModal } from '@/views/components/SelectGoodsModal';
import { CheckOutlined, DownOutlined, EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { cLive } from '@consts';
import { uGoodsInfo } from '@utils';
import {
    Button,
    Col,
    Dropdown,
    Image,
    Input,
    InputNumber,
    Modal,
    Popconfirm,
    Popover,
    Row,
    Select,
    Space,
    Spin,
    Table,
    Tabs,
    Tag,
    Tooltip,
    message,
    type MenuProps,
} from 'antd';
import { type ColumnsType } from 'antd/es/table';
import copyToClipboard from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { throttle } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

import EditingBatchShop from '../../EditingBatchShop';
import EditingScript, { ScriptTabsEnum } from '../../EditingScript';
import EditingShopInfo from '../../EditingShopInfo';

import ListSoft, { type ListSoftRef } from './components/ListSoft';
import './index.scss';

export type GoodsItem = Console.Response.FindLiveGoodsByPage['dataList'][number];

type Props = {
    isLiveEnd: boolean;
    liveDetail?: Console.Response.LiveDetailInfo;
};

enum PinnedMap {
    NoSayPinned = 0, // 不讲解
    SayPinned = 1, // 讲解中
}

enum MenuPropsKeys {
    /**
     * 批量置顶
     */
    setLiveGoodsTop = '0',
    /**
     * 批量下架
     */
    optLiveGoods = '1',
    /**
     * 批量删除
     */
    deleteLiveGoods = '2',

    // 待上架
    /**
     * 批量上架
     */
    optLiveGoods2 = '3',
    /**
     * 批量删除
     */
    deleteLiveGoods2 = '4',
    /**
     * 手动添加
     */
    addShop = '5',
    /**
     * 导入上场直播商品
     */
    importLastShop = '6',
    /**
     * 批量编辑商品类型
     */
    batchEditShop = '7',
}

const items: MenuProps['items'] = [
    {
        key: MenuPropsKeys.batchEditShop,
        label: '批量编辑商品类型',
    },
    {
        key: MenuPropsKeys.setLiveGoodsTop,
        label: '批量置顶',
    },
    {
        key: MenuPropsKeys.optLiveGoods,
        label: '批量下架',
    },
    {
        key: MenuPropsKeys.deleteLiveGoods,
        label: '批量删除',
    },
];
const items2: MenuProps['items'] = [
    {
        key: MenuPropsKeys.batchEditShop,
        label: '批量编辑商品类型',
    },
    {
        key: MenuPropsKeys.optLiveGoods2,
        label: '批量上架',
    },
    {
        key: MenuPropsKeys.deleteLiveGoods,
        label: '批量删除',
    },
];
const items3: MenuProps['items'] = [
    {
        key: MenuPropsKeys.addShop,
        label: '手动添加',
    },
    {
        key: MenuPropsKeys.importLastShop,
        label: '导入上场直播商品',
    },
];

type CountProps = {
    currentTime: string;
    endTime: string;
    onRefresherRefresh?(): void;
};
const CountDown = (props: CountProps) => {
    const { currentTime, endTime, onRefresherRefresh } = props;
    const { d, h, m, s } = useCountDown({
        currentTime: dayjs(currentTime).valueOf(),
        endTime: dayjs(endTime).valueOf(),
        maxUnit: 'day',
        onEnd: () => {
            onRefresherRefresh && onRefresherRefresh();
        },
    });
    if (+d > 3) {
        return undefined;
    }
    if (d === '0' && +h < 24) {
        return (
            <div className='left-time'>
                距结束
                <span className='col-red'>
                    {h}:{m}:{s}
                </span>
            </div>
        );
    }
    if (d === '0' && h === '0' && m === '0' && s === '0') {
        return <div className='left-time'>已结束</div>;
    }
    return (
        <div className='left-time'>
            距结束 {d}天{h}:{m}:{s}
        </div>
    );
};

const LiveGoodsList = (props: Props) => {
    const { isLiveEnd, liveDetail } = props;
    const [goodsList, setGoodsList] = useState<GoodsItem[]>([]);
    const [selectRecord, setSelectRecord] = useState<GoodsItem>();
    const [selectRecords, setSelectRecords] = useState<GoodsItem[]>([]);

    const [showSelectModal, setShowSelectModal] = useState<boolean>(false);
    const [operateSpuId, setOperateSpuId] = useState<number>(0);
    const [alterSortValue, setAlterSortValue] = useState<number>(0);
    const [page, setPage] = useState<number>(1);
    const [hasMoreData, setHasMoreData] = useState<boolean>(true);
    const [loading, setLoading] = useState<boolean>(false);
    const [importLoading, setImportLoading] = useState<boolean>(false);
    const [showEditingScript, setShowEditingScript] = useState(false);
    const [showEditingShopInfo, setShowEditingShopInfo] = useState(false);
    const [showBatchEditShop, setShowBatchEditShop] = useState(false);
    const [activeKey, setActiveKey] = useState('1');
    const [toEditScriptType, setToEditScriptType] = useState<ScriptTabsEnum>();
    const [isTimer, setIsTimer] = useState(false);
    const [goodsSortType, setGoodsSortType] = useState<string | undefined>();

    const [searchParams] = useSearchParams();
    const liveId = searchParams.get('liveId') || 0;
    const [goodsCount, setGoodsCount] = useState<{
        count: number;
        unUpCount: number;
    }>({
        count: 0,
        unUpCount: 0,
    });

    const [currentGood, setCurrentGood] = useState<GoodsItem | undefined>();
    const [recordGoodSetShortTitleSpuId, setRecordGoodSetShortTitleSpuId] = useState<number>(0);
    const [goodShortTitleValue, setGoodShortTitleValue] = useState<string>('');

    const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
    const [showRowSelection, setShowRowSelection] = useState(false);
    const [expandSpuIds, setExpandSpuIds] = useState<number[]>([]);
    // 直播奖励开关：1-开启，0-关闭
    const [rewardEnabled, setRewardEnabled] = useState(0);

    const categoryIdRef = useRef(0);

    const isLivingStatus = useMemo(() => liveDetail?.pushStatus === cLive.PushStatusMap.Pushing, [liveDetail]);

    const listSoftRef = useRef<ListSoftRef | null>(null);
    /** 是否为未开始或未开播的直播间 */
    const notStartLive = useMemo(
        () =>
            !!liveDetail?.pushStatus && [cLive.PushStatusMap.NoStart, cLive.PushStatusMap.NoLive].includes(liveDetail.pushStatus),
        [liveDetail?.pushStatus]
    );

    useEffect(() => {
        setGoodsList([]);
        setPage(1);
        handleGetPermission();
    }, []);

    useEffect(() => {
        getGoodsList();
    }, [page]);

    const handleGetPermission = async () => {
        const res = await findUserLivePermissions();
        if (res?.data) {
            setRewardEnabled(res.data.rewardEnabled);
        }
    };

    const handleShowEditingScript = (record: GoodsItem) => {
        setShowEditingScript(true);
        setToEditScriptType(undefined);
        setSelectRecord({ ...record });
    };

    const handleShowEditingShopInfo = (record: GoodsItem) => {
        setShowEditingShopInfo(true);
        setSelectRecord({ ...record });
    };

    // const onEditGoodsShortTitle = (good: GoodsItem) => {
    //     setCurrentGood(good);
    //     setRecordGoodSetShortTitleSpuId(good?.spuId);
    //     setGoodShortTitleValue(good?.customSpuName || '');
    // };

    const onGoodShortTitleSubmit = throttle(async () => {
        await setLiveGoodsCustomSpuName({
            liveId: liveId as number,
            customSpuName: goodShortTitleValue,
            spuId: currentGood?.spuId as number,
        });

        message.success('设置成功');
        setRecordGoodSetShortTitleSpuId(0);
        setCurrentGood(undefined);
        getGoodsList();
    }, 500);

    const handleResetSerialNo = async () => {
        await resetLiveGoodsSerialNo({
            liveId: +liveId,
        });
        getGoodsList(true);
    };

    const getShortTitleBarNode = (record: GoodsItem) => (
        <div className='short-title-bar'>
            <Tooltip placement='topLeft' title={`短标题：${record.customSpuName}`}>
                <div className='short-title-text'>{record.customSpuName}</div>
                {/* 短标题：{record.customSpuName} */}
            </Tooltip>
            {/* {recordGoodSetShortTitleSpuId !== record.spuId && ( */}
            {/*  <div> */}
            {/* <span>{record.customSpuName}</span> */}
            {/* <span onClick={() => onEditGoodsShortTitle(record)}>
                        <EditOutlined className='short-title-icon' />
                    </span> */}
            {/* </div> */}
            {/* )} */}
            {recordGoodSetShortTitleSpuId === record.spuId && (
                <div className='edit-short-title-box'>
                    <Input
                        style={{ height: '32px' }}
                        value={goodShortTitleValue}
                        onChange={event => setGoodShortTitleValue(event.currentTarget.value)}
                    />
                    <span onClick={onGoodShortTitleSubmit}>
                        <CheckOutlined className='short-title-icon' />
                    </span>
                </div>
            )}
        </div>
    );

    const columns: ColumnsType<GoodsItem> = [
        {
            title: '位置序号',
            key: 'sort',
            width: 100,
            dataIndex: 'sort',
            render: (text: string, record: GoodsItem) => (
                <Row align='middle'>
                    <Col>
                        {operateSpuId === record.spuId ? (
                            <InputNumber
                                style={{ width: '70px' }}
                                value={alterSortValue}
                                min={1}
                                max={500}
                                precision={0}
                                onChange={e => {
                                    setAlterSortValue(e as number);
                                }}
                            />
                        ) : (
                            text
                        )}
                    </Col>
                    {!isLiveEnd && (
                        <Col>
                            {operateSpuId === record.spuId ? (
                                <CheckOutlined onClick={() => handleUpdateAlterSortValue(record.spuId, +text)} />
                            ) : (
                                <EditOutlined
                                    onClick={() => {
                                        setOperateSpuId(record.spuId);
                                        setAlterSortValue(+text);
                                    }}
                                />
                            )}
                        </Col>
                    )}
                </Row>
            ),
        },
        {
            title: (
                <Space>
                    链接序号
                    {notStartLive && (
                        <Button size='small' color='primary' variant='text' onClick={handleResetSerialNo}>
                            重置
                        </Button>
                    )}
                </Space>
            ),
            key: 'serialNo',
            width: notStartLive ? 160 : 100,
            dataIndex: 'serialNo',
        },
        {
            title: '商品信息',
            key: 'spuId',
            width: 300,
            dataIndex: 'spuId',
            render: (_text: string, record: GoodsItem) => (
                <Row>
                    <div className='goods-info' style={{ display: 'flex', flexDirection: 'row' }}>
                        <Image src={record.mainUrl || ''} style={{ width: '65px', height: '65px' }} />
                        <div className='goods-info-box'>
                            {getShortTitleBarNode(record)}
                            {/* <Tooltip placement='topLeft' title={record.spuName}>
                                <span className={'spuName'}>{record.spuName}</span>
                            </Tooltip> */}
                            {record.sellingPoint ? <div className='selling'>{record.sellingPoint}</div> : null}
                        </div>
                    </div>
                </Row>
            ),
        },
        {
            title: '商品类型',
            key: 'twoCategoryName',
            width: 100,
            dataIndex: 'twoCategoryName',
            render: (_text: string, record: GoodsItem) => record.customerCategoryName || record.twoCategoryName,
        },
        // {
        //     title: '基础信息',
        //     key: 'status',
        //     width: 250,
        //     dataIndex: 'status',
        //     align: 'center',
        //     render: (_text: string, record: GoodsItem) => (
        //         <>
        //             <div>商品编码：{record.spuCode || '-'}</div>
        //             <div>商品类型：{record.goodsType === 1 ? '普通商品' : '跨境商品'}</div>
        //         </>
        //     ),
        // },
        // {
        //     title: '属性',
        //     key: 'propertyValuesStr',
        //     dataIndex: 'propertyValuesStr',
        //     align: 'center',
        //     width: 400,
        //     render: (_text: string, record: GoodsItem) => (
        //         <>
        //             {!!record.goodsSkuList &&
        //                 !!record.goodsSkuList.length &&
        //                 record.goodsSkuList.slice(0, 1).map(i => (
        //                     <Row key={i.skuCode} justify={'start'}>
        //                         <Col span={10}>
        //                             <Tooltip title={i.skuDesc}>
        //                                 属性：<span>{i.skuDesc ? i.skuDesc.slice(0, 10) : '--'}</span>
        //                             </Tooltip>
        //                         </Col>
        //                         <Col span={7}>价格：{uDecimal.FenToAmount(i.retailPrice)}</Col>
        //                         <Col span={7}>库存：{i.skuStockNum}</Col>
        //                     </Row>
        //                 ))}
        //             {!!record.goodsSkuList && record.goodsSkuList.length > 1 && (
        //                 <Popover
        //                     content={
        //                         <Table
        //                             rowKey={'skuCode'}
        //                             pagination={false}
        //                             dataSource={record.goodsSkuList}
        //                             style={{ width: '600px' }}
        //                             columns={[
        //                                 { title: '属性', dataIndex: 'skuDesc', key: 'skuDesc' },
        //                                 {
        //                                     title: '价格',
        //                                     dataIndex: 'retailPrice',
        //                                     key: 'retailPrice',
        //                                     render: (value: number) => uDecimal.FenToAmount(value),
        //                                 },
        //                                 { title: '库存', dataIndex: 'skuStockNum', key: 'skuStockNum' },
        //                             ]}
        //                         />
        //                     }
        //                 >
        //                     <Button type='link'>查看所有规格</Button>
        //                 </Popover>
        //             )}
        //         </>
        //     ),
        // },
        {
            title: isLiveEnd ? '' : '操作',
            key: 'spuId',
            width: isLiveEnd ? 0 : 150,
            dataIndex: 'spuId',
            fixed: 'right',
            render: (text: number, _record: GoodsItem) =>
                isLiveEnd ? (
                    ''
                ) : (
                    <Space direction='vertical'>
                        {/* {record.sayPinned === 1 ? (
                            <Popconfirm title='确认取消讲解商品？' onConfirm={() => handleCancelSay(text)}>
                                <Button type='link'>取消讲解</Button>
                            </Popconfirm>
                        ) : (
                            <Popconfirm title='确认讲解商品？' onConfirm={() => handleSay(text)}>
                                <Button type='link'>讲解</Button>
                            </Popconfirm>
                        )} */}
                        <Button
                            className='handle-btn'
                            type='link'
                            onClick={() => {
                                handleShowEditingShopInfo(_record);
                            }}
                        >
                            编辑商品信息
                        </Button>
                        <Popconfirm title='确认上架此商品？' onConfirm={() => handleOptLiveGoods([text], 'UP')}>
                            <Button type='link' className='handle-btn'>
                                上架
                            </Button>
                        </Popconfirm>
                        <Popconfirm title='确认删除此商品？' onConfirm={() => handleDelete(text)}>
                            <Button type='link' className='handle-btn' danger>
                                删除
                            </Button>
                        </Popconfirm>
                    </Space>
                ),
        },
    ] as unknown as ColumnsType<GoodsItem>;

    const copy = (copyValue: string) => {
        if (copyToClipboard(copyValue)) {
            message.success('复制成功');
        }
    };

    const _getPromotionDetail = useCallback(
        (data: GoodsItem) =>
            uGoodsInfo.getGoodsPromotionSummary({
                spuExclusivePrice: data.spuExclusivePrice,
                activeCoupon: data.activeCouponList,
                activeInfo: data.activeData,
            }),
        []
    );
    const finishTimeFn = async () => {
        setIsTimer(true);
    };

    const renderCoupons = (item: GoodsItem) => {
        const { spuExclusive, showCoupons, activeDetail } = _getPromotionDetail(item);
        const coupon = uGoodsInfo._formatCoupon(spuExclusive, showCoupons, activeDetail);
        return (
            <div className='goods-coupons-list'>
                {coupon.map((item, index) => (
                    <div className={`goods-coupons-item-box ${!isTimer ? 'normal' : ''}`} key={index}>
                        <div className='goods-coupons-item'>
                            <div className='goods-coupons-item-label'>
                                <span className='goods-coupons-item-label-text'>{item.prefix}</span>
                            </div>
                            <div className='goods-coupons-item-text'>{item.text}</div>
                        </div>
                        {!isTimer && item.type !== -1 && (
                            <CountDown
                                currentTime={item.activeTime?.serverTime as string}
                                endTime={item.activeTime?.expiryEnd as string}
                                onRefresherRefresh={finishTimeFn}
                            />
                        )}
                    </div>
                ))}
                {!!coupon && coupon.length > 0 && (
                    <Popover
                        content={
                            <Table
                                key={'skuCode'}
                                pagination={false}
                                dataSource={coupon?.map(item2 => ({
                                    activeEnd: item2.activeTime?.expiryEnd,
                                    activeStart: item2.activeTime?.expiryStart,
                                    name: item2.text,
                                    lable: item2.prefix,
                                }))}
                                style={{
                                    width: '600px',
                                    maxHeight: '500px',
                                    overflow: 'auto',
                                }}
                                scroll={{ x: true, y: 400 }}
                                columns={[
                                    { title: '活动类型', width: 100, dataIndex: 'lable', key: 'lable' },
                                    { title: '详情', dataIndex: 'name', key: 'name' },
                                    { title: '开始时间', dataIndex: 'activeStart', key: 'activeStart' },
                                    { title: '结束时间', dataIndex: 'activeEnd', key: 'activeEnd' },
                                ]}
                            />
                        }
                    >
                        <Button type='link'>查看所有券信息</Button>
                    </Popover>
                )}
            </div>
        );
    };

    const columns2: ColumnsType<GoodsItem> = [
        {
            title: '位置序号',
            key: 'sort',
            width: 100,
            dataIndex: 'sort',
            render: (text: string, record: GoodsItem) => (
                <Row align='middle'>
                    <Col>
                        {operateSpuId === record.spuId ? (
                            <InputNumber
                                style={{ width: '70px' }}
                                value={alterSortValue}
                                min={1}
                                max={500}
                                precision={0}
                                onChange={e => {
                                    setAlterSortValue(e as number);
                                }}
                            />
                        ) : (
                            text
                        )}
                    </Col>
                    {!isLiveEnd && (
                        <Col>
                            {operateSpuId === record.spuId ? (
                                <CheckOutlined onClick={() => handleUpdateAlterSortValue(record.spuId, +text)} />
                            ) : (
                                <EditOutlined
                                    onClick={() => {
                                        setOperateSpuId(record.spuId);
                                        setAlterSortValue(+text);
                                    }}
                                />
                            )}
                        </Col>
                    )}
                </Row>
            ),
        },
        {
            title: (
                <Space>
                    链接序号
                    {notStartLive && (
                        <Button size='small' color='primary' variant='text' onClick={handleResetSerialNo}>
                            重置
                        </Button>
                    )}
                </Space>
            ),
            key: 'serialNo',
            width: notStartLive ? 160 : 100,
            dataIndex: 'serialNo',
        },
        {
            title: '商品信息',
            key: 'spuId',
            width: 300,
            // minWidth: '300px',
            // maxWidth: '300px',
            dataIndex: 'spuId',
            render: (_text: string, record: GoodsItem) => (
                <Row>
                    <div className='goods-info' style={{ display: 'flex', flexDirection: 'row' }}>
                        <div
                            className={`good-img-box ${
                                [record?.sayPinned, record?.isEpigraphTextOpening].includes(PinnedMap.SayPinned)
                                    ? 'red-border'
                                    : 'origin-border'
                            }`}
                        >
                            <Image src={record.mainUrl || ''} style={{ width: '65px', height: '65px' }} />
                            <div className='explaining'>
                                {record?.sayPinned === PinnedMap.SayPinned
                                    ? '讲解中'
                                    : record?.isEpigraphTextOpening === PinnedMap.SayPinned
                                    ? '提词中'
                                    : '待讲解'}
                            </div>
                        </div>

                        <div className='goods-info-box'>
                            {getShortTitleBarNode(record)}
                            {/* <Tooltip placement='topLeft' title={record.spuName}>
                                <span className={'spuName'}>{record.spuName}</span>
                            </Tooltip> */}
                            {record.sellingPoint ? <div className='selling'>{record.sellingPoint}</div> : null}
                        </div>
                    </div>
                </Row>
            ),
        },
        {
            title: '编码信息',
            key: 'sku',
            width: 200,
            dataIndex: 'sku',
            render: (_text: string, item: GoodsItem) => (
                <Row>
                    <div>spuId:{item.spuId || '-'}</div>&nbsp;&nbsp;
                    <div>商品编码：{item.spuCode || '-'}</div>&nbsp;&nbsp;
                    <div>商品类型：{item.goodsType === 1 ? '普通商品' : '跨境商品'}</div>
                </Row>
            ),
        },
        {
            title: '满减活动',
            key: 'activeInfos',
            width: 200,
            dataIndex: 'activeInfos',
            render: (_text: string, item: GoodsItem) => {
                const activeInfos = item.activeInfos?.[0];
                const conditionList = activeInfos?.ext?.conditionList || [];
                return (
                    <div>
                        {activeInfos ? (
                            <>
                                <div>
                                    ID :{activeInfos.id || '--'}
                                    <Button
                                        onClick={() => {
                                            copy(`${activeInfos.id}`);
                                        }}
                                        type='link'
                                    >
                                        复制
                                    </Button>
                                </div>
                                <div>{activeInfos.spuLimitCount === 0 ? '不限购' : `限购${activeInfos.spuLimitCount}件`}</div>
                                <div>
                                    {conditionList.map((v, i) => (
                                        <Tag color='red' key={i}>
                                            满 {uNumber.centToYuan(v.threshold || 0)} 减 {uNumber.centToYuan(v.discount || 0)}
                                        </Tag>
                                    ))}
                                </div>
                            </>
                        ) : (
                            '--'
                        )}
                    </div>
                );
            },
        },
        {
            title: '直播销量',
            key: 'currentSales',
            width: 100,
            dataIndex: 'currentSales',
        },
        {
            title: '活动信息',
            key: 'activeDetail',
            width: 350,
            dataIndex: 'activeDetail',
            render: (_text: string, _record: GoodsItem) => renderCoupons(_record),
        },
        {
            title: '商品类型',
            key: 'twoCategoryName',
            width: 100,
            dataIndex: 'twoCategoryName',
            render: (_text: string, record: GoodsItem) => record.customerCategoryName || record.twoCategoryName,
        },
        // {
        //     title: '讲解状态',
        //     key: 'sayPinned',
        //     width: 100,
        //     dataIndex: 'sayPinned',
        //     align: 'center',
        //     render: (val: number) => (val === 1 ? <Tag color='green'>讲解中</Tag> : <Tag color='orange'>待讲解</Tag>),
        // },
        {
            title: isLiveEnd ? '' : '操作',
            key: 'spuId',
            width: isLiveEnd ? 0 : 150,
            dataIndex: 'spuId',
            fixed: 'right',
            render: (text: number, record: GoodsItem) =>
                isLiveEnd ? null : (
                    <Space direction='horizontal' style={{ display: 'flex', flexWrap: 'wrap' }}>
                        {isLivingStatus ? (
                            record.sayPinned === PinnedMap.SayPinned ? (
                                <Popconfirm title='确认取消讲解商品？' onConfirm={() => handleCancelSay(text)}>
                                    <Button className='handle-btn' type='link'>
                                        取消讲解
                                    </Button>
                                </Popconfirm>
                            ) : (
                                <Popconfirm title='确认讲解商品？' onConfirm={() => handleSay(text)}>
                                    <Button className='handle-btn' type='link'>
                                        讲解
                                    </Button>
                                </Popconfirm>
                            )
                        ) : null}
                        {isLivingStatus ? (
                            record.isEpigraphTextOpening === PinnedMap.SayPinned ? (
                                <Popconfirm title='确认取消提词吗？' onConfirm={() => handleCancelTips(record)}>
                                    <Button className='handle-btn' type='link'>
                                        取消提词
                                    </Button>
                                </Popconfirm>
                            ) : (
                                <Dropdown
                                    menu={{
                                        items: [
                                            {
                                                key: String(ScriptTabsEnum.Invite),
                                                label: '商品介绍',
                                                onClick: () => handleToGoodTips(record, ScriptTabsEnum.Invite),
                                            },
                                            {
                                                key: String(ScriptTabsEnum.Interaction),
                                                label: '案例分析',
                                                onClick: () => handleToGoodTips(record, ScriptTabsEnum.Interaction),
                                            },
                                        ],
                                    }}
                                >
                                    <Button className='handle-btn' icon={<DownOutlined />} iconPosition='end' type='link'>
                                        提词
                                    </Button>
                                </Dropdown>
                            )
                        ) : null}
                        <Button
                            className='handle-btn'
                            type='link'
                            onClick={() => {
                                handleShowEditingScript(record);
                            }}
                        >
                            编辑话术
                        </Button>
                        <Button
                            className='handle-btn'
                            type='link'
                            onClick={() => {
                                handleShowEditingShopInfo(record);
                            }}
                        >
                            编辑商品信息
                        </Button>
                        {record.isTop === 0 ? (
                            <Popconfirm title='确认置顶此商品？' onConfirm={() => handleSetLiveGoodsTop(text, 1)}>
                                <Button className='handle-btn' type='link'>
                                    置顶
                                </Button>
                            </Popconfirm>
                        ) : (
                            <Popconfirm title='确认取消置顶此商品？' onConfirm={() => handleSetLiveGoodsTop(text, 0)}>
                                <Button className='handle-btn' type='link'>
                                    取消置顶
                                </Button>
                            </Popconfirm>
                        )}
                        <Popconfirm title='确认下架此商品？' onConfirm={() => handleOptLiveGoods([text], 'DOWN')}>
                            <Button type='link' className='handle-btn'>
                                下架
                            </Button>
                        </Popconfirm>
                        {liveDetail?.isSupportPublishGoods === 1 && record.goodsUpStatus === 1 ? (
                            <Popconfirm
                                title='确认要立即开售吗？确认后商品售卖装配配置将同步调整'
                                onConfirm={() => handlePublishLiveGoods(record.spuId)}
                            >
                                <Button type='link' disabled={loading} className='handle-btn'>
                                    立即开售
                                </Button>
                            </Popconfirm>
                        ) : null}
                        <Popconfirm title='确认删除此商品？' onConfirm={() => handleDelete(text)}>
                            <Button type='link' danger className='handle-btn'>
                                删除
                            </Button>
                        </Popconfirm>
                    </Space>
                ),
        },
    ] as unknown as ColumnsType<GoodsItem>;

    const handleToGoodTips = async (goodItem: GoodsItem, editScriptType: ScriptTabsEnum) => {
        openNoticeFromApi({
            liveId: Number(liveId),
            spuId: goodItem.spuId,
            type: Number(editScriptType),
        })
            .then(() => {
                getGoodsList(true);
            })
            .catch(err => {
                if (err.code === '05008') {
                    setToEditScriptType(editScriptType);
                    Modal.confirm({
                        title: '当前暂无内容，请添加商品话术',
                        icon: <ExclamationCircleOutlined />,
                        content: (
                            <Row style={{ paddingBottom: '50px', color: '#666' }}>添加后，点击提词时主播端可显示相应内容</Row>
                        ),
                        okText: '编辑话术',
                        okButtonProps: { style: { background: '#00B68F' } },
                        cancelText: '取消',
                        onOk: () => {
                            setSelectRecord(goodItem);
                            setShowEditingScript(true);
                        },
                    });
                    return;
                }
            });
    };

    const handleCancelTips = async (_goodItem: GoodsItem) => {
        await closeNoticeFromApi({ liveId: Number(liveId) });
        message.success('取消成功');
        getGoodsList(true);
    };

    const getGoodsList = async (isFresh = false, type?: string) => {
        if (liveId) {
            const newType = type ? +type : +activeKey;
            if (isFresh) {
                setPage(1);
            }
            setLoading(true);
            const res = await findLiveGoodsByPage({
                condition: {
                    liveId: +liveId,
                    type: newType,
                    customerCategoryId: categoryIdRef.current === 0 ? undefined : categoryIdRef.current,
                },
                page: isFresh ? 1 : page,
                rows: 100,
            });
            setLoading(false);
            if (res.data) {
                const _list = isFresh || page === 1 ? res.data.dataList : [...goodsList, ...res.data.dataList];
                setGoodsList(_list);
                setHasMoreData(_list.length < res.data.total);
                setSelectedRowKeys([]);
            }
            // setOperateSpuId(0);
            queryLiveGoodsCount(newType);
        }
    };

    const queryLiveGoodsCount = async (type: number) => {
        const res = await getLiveGoodsCount({
            liveId: +liveId,
            type,
        });
        if (res.data) {
            setGoodsCount(res.data);
        }
    };

    const handleUpdateAlterSortValue = async (spuId: number, oldSort: number) => {
        if (oldSort === alterSortValue || alterSortValue > goodsList.length) {
            setAlterSortValue(0);
            setOperateSpuId(0);
            resetGoodsSort();
            return;
        }
        await setLiveGoodsSort({
            liveId: +liveId,
            spuId,
            sort: alterSortValue,
            optType: +activeKey === 1 ? 1 : 2,
        });
        setAlterSortValue(0);
        setOperateSpuId(0);
        getGoodsList(true);
        resetGoodsSort();
    };

    const handleSetData = async (newGoodsList: Goods.Response.getGoodsByPage['dataList']) => {
        if (!newGoodsList.length) {
            return;
        }
        setShowSelectModal(false);
        const spus = newGoodsList.map(i => ({ spuId: i.spuId }));
        await addLiveGoods({
            liveId: +liveId,
            spus,
        });
        getGoodsList(true);
    };

    const handleDelete = async (spuId: number) => {
        await deleteLiveGoods({
            liveId: +liveId,
            spuId,
            optType: +activeKey === 1 ? 1 : 2,
        });
        getGoodsList(true);
    };

    const handleDeleteAll = async () => {
        await deleteLiveGoods({
            liveId: +liveId,
            spuIds: selectedRowKeys,
            optType: +activeKey === 1 ? 1 : 2,
        });
        message.success('批量删除成功');
        getGoodsList(true);
    };

    const handleSetLiveGoodsTop = async (spuId: number, top: number) => {
        /* if (goodsList[0].isTop === 1 && top === 1) {
            return message.error('已经有置顶的商品');
        } */
        await setLiveGoodsTop({
            liveId: +liveId,
            spuId,
            top,
        });

        getGoodsList(true);
    };

    const handleSetLiveGoodsTopAll = async () => {
        await setLiveGoodsTop({
            liveId: +liveId,
            spuIds: selectedRowKeys,
            top: 1,
        });
        message.success('批量置顶成功');
        getGoodsList(true);
    };

    const handleSay = async (spuId: number) => {
        await saySortLiveGoods({
            liveId: +liveId,
            spuId,
        });
        getGoodsList(true);
    };

    const handleCancelSay = async (spuId: number) => {
        await cancelSayLiveGoods({
            liveId: +liveId,
            spuId,
        });
        getGoodsList(true);
    };

    const handleOptLiveGoods = async (spuIds: number[], type: string, show = false) => {
        if (type === 'DOWN') {
            // 下架
            await optLiveGoods({
                liveId: +liveId,
                spuIds,
                type: 0,
            });
            if (show) message.success('批量下架成功');
            getGoodsList(true);
        } else if (type === 'UP') {
            // 上架
            const res = await optLiveGoods({
                liveId: +liveId,
                spuIds,
                type: 1,
            });

            if (res.msg === '部分会员专享商品因不支持分享，上架失败，请在待上架列表查看') {
                message.warning(res.msg);
            }
            if (show) message.success('批量上架成功');
            getGoodsList(true);
        }
    };

    const handleMore = () => {
        setPage(page + 1);
    };

    const handleOnClick: MenuProps['onClick'] = ({ key }) => {
        if (!selectedRowKeys.length && key !== MenuPropsKeys.addShop && key !== MenuPropsKeys.importLastShop) {
            return message.warning('请先选择商品');
        }
        switch (key) {
            case MenuPropsKeys.setLiveGoodsTop:
                handleSetLiveGoodsTopAll();
                break;
            case MenuPropsKeys.optLiveGoods:
                handleOptLiveGoods(selectedRowKeys, 'DOWN', true);
                break;
            case MenuPropsKeys.deleteLiveGoods:
                handleDeleteAll();
                break;
            case MenuPropsKeys.optLiveGoods2:
                handleOptLiveGoods(selectedRowKeys, 'UP', true);
                break;
            case MenuPropsKeys.deleteLiveGoods2:
                handleDeleteAll();
                break;
            case MenuPropsKeys.addShop:
                setShowSelectModal(true);
                break;
            case MenuPropsKeys.importLastShop:
                handleImportShop();
                break;
            case MenuPropsKeys.batchEditShop:
                setShowBatchEditShop(true);
        }
    };

    const handleImportShop = () => {
        setImportLoading(true);
        importLiveGoods({
            liveId: +liveId,
        })
            .then(() => {
                message.success('导入成功');
                handleRefresh();
            })
            .finally(() => {
                setImportLoading(false);
            });
    };

    const handleShowSelect = () => {
        setShowRowSelection(!showRowSelection);
    };

    const handleRefresh = () => {
        getGoodsList(true);
        listSoftRef.current?.handleGetAllGoodsCategory();
    };

    const handleSort = async (value: string) => {
        setGoodsSortType(value);
        if (liveId && value !== undefined) {
            await sortLiveGoods({
                liveId: +liveId,
                // 0 上架中商品 1 待上架商品
                goodsType: activeKey === '0' ? 1 : 0,
                sortType: +value,
            });
            handleRefresh();
        }
    };

    const resetGoodsSort = () => {
        setGoodsSortType(undefined);
    };

    const extra = useMemo(
        () =>
            isLiveEnd ? (
                ''
            ) : (
                <Space>
                    <Select
                        style={{ width: 120 }}
                        className='goods-sort-select'
                        value={goodsSortType}
                        onSelect={handleSort}
                        placeholder='商品排序'
                        options={[
                            // { value: '0', label: '默认' },
                            { value: '1', label: '按销量降序' },
                            { value: '2', label: '按售价降序' },
                        ]}
                    />
                    <Dropdown
                        menu={{
                            items: activeKey === '0' ? items2 : items,
                            onClick: handleOnClick,
                        }}
                        placement='bottom'
                        overlayStyle={{
                            textAlign: 'center',
                        }}
                        arrow={{ pointAtCenter: true }}
                    >
                        <Button onClick={handleShowSelect}>批量操作</Button>
                    </Dropdown>

                    <Dropdown
                        menu={{
                            items: items3,
                            onClick: handleOnClick,
                        }}
                        placement='bottom'
                        arrow={{ pointAtCenter: true }}
                    >
                        <Button
                            type='primary'
                            // onClick={() => {
                            //     setShowSelectModal(true);
                            // }}
                            loading={importLoading}
                        >
                            {importLoading ? '导入中' : '添加商品'}
                        </Button>
                    </Dropdown>

                    <Button type='link' style={{ padding: 0, paddingLeft: 4 }} onClick={handleRefresh}>
                        <span
                            style={{
                                background: '#F7F8F9',
                                padding: '4px 8px',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                borderRadius: '4px',
                                width: '100%',
                                height: '100%',
                                // marginRight: '24px',
                                // marginLeft: '8px',
                            }}
                            // onClick={handleReloadOutlined}
                        >
                            <img
                                src={refreshIcon}
                                alt=''
                                style={{
                                    width: '16px',
                                    height: '16px',
                                }}
                            />
                        </span>
                    </Button>
                </Space>
            ),
        [isLiveEnd, activeKey, selectedRowKeys, showRowSelection]
    );

    const onChange = (key: string) => {
        setActiveKey(key);
        getGoodsList(true, key);
    };

    const handleUp = async (newGoodsList: Goods.Response.getGoodsByPage['dataList'], type: number) => {
        if (!newGoodsList.length) {
            return;
        }
        setShowSelectModal(false);
        const spus = newGoodsList.map(i => ({ spuId: i.spuId }));
        const unupSpus = newGoodsList.map(i => ({ spuId: i.spuId }));
        const res = await addLiveGoods({
            liveId: +liveId,
            spus: type === 1 ? spus : undefined,
            unupSpus: type === 0 ? unupSpus : undefined,
        });

        if (res?.msg === '部分会员专享商品因不支持分享，上架失败，请在待上架列表查看') {
            message.warning(res.msg);
        }
        listSoftRef.current?.handleGetAllGoodsCategory();
        getGoodsList(true);
    };

    const handleSelectChange = (_selectedRowKeys: React.Key[], items: GoodsItem[]) => {
        setSelectRecords(items);
        setSelectedRowKeys(_selectedRowKeys as number[]);
    };

    const handleExpand = (expanded: boolean, record: GoodsItem) => {
        let newExpandSpuIds: number[] = [...expandSpuIds];
        if (expanded) {
            newExpandSpuIds.push(record.spuId);
        } else {
            newExpandSpuIds = newExpandSpuIds.filter(i => i !== record.spuId);
        }
        setExpandSpuIds(newExpandSpuIds);
    };

    const handleGoodsSkuPriceMgrOnAndOff = (
        skuId: number,
        liveId: number,
        item: {
            retailPrice?: number;
            skuCode?: string;
            skuDesc?: string;
            skuPrice?: number;
            skuStockNum?: number;
            skuId?: number | undefined;
            exclusiveRetailPrice?: number | undefined;
            listedFlag?: boolean | undefined;
            priceStatus?: unknown;
        }
    ) => {
        const priceStatus = item.listedFlag ? 110 : 200;
        goodsSkuPriceMgrOnAndOff({
            skuId,
            priceStatus,
            liveId,
        }).then(() => {
            item.listedFlag = !item.listedFlag;
            setGoodsList([...goodsList]);
            message.success(priceStatus === 110 ? '下架专享价成功' : '上架专享价成功');
            getGoodsList(true);
        });
    };

    const renderTag = (status: number) => {
        switch (status) {
            case 110:
                return <Tag color='blue'>待生效</Tag>;
            case 200:
                return <Tag color='green'>生效中</Tag>;
            case 100:
                return <Tag color='default'>已失效</Tag>;
            default:
                return null;
        }
    };

    const renderSubTable = (record: GoodsItem) => (
        <Table
            key={'skuCode'}
            pagination={false}
            dataSource={record.goodsSkuList}
            columns={[
                {
                    title: 'SKU编码',
                    dataIndex: 'skuCode',
                    key: 'skuCode',
                    render: (_value: string, item) => (
                        <div>
                            {_value || '--'}
                            {item.skuExclusivePriceStatus && (
                                <span style={{ marginLeft: '10px' }}>{renderTag(item.skuExclusivePriceStatus)}</span>
                            )}
                        </div>
                    ),
                },
                { title: '规格属性', dataIndex: 'skuDesc', key: 'skuDesc' },
                { title: '库存', dataIndex: 'skuStockNum', key: 'skuStockNum' },
                {
                    title: '商品售价',
                    dataIndex: 'retailPrice',
                    key: 'retailPrice',
                    render: (_value: number) => `￥${uNumber.centToYuan(_value)}`,
                },
                {
                    title: '直播专享价',
                    dataIndex: 'exclusiveRetailPrice',
                    key: 'exclusiveRetailPrice',
                    render: (_value: number) => (_value ? `￥${uNumber.centToYuan(_value || 0)}` : '--'),
                },
                {
                    title: '专享价限购',
                    dataIndex: 'purchaseLimit',
                    key: 'purchaseLimit',
                    render: (_value: number, item) => {
                        if (item.exclusiveRetailPrice) {
                            return item.purchaseLimitFlag ? `限购${_value}件` : '不限购';
                        }
                        return '--';
                    },
                },
                {
                    title: '直播奖励',
                    dataIndex: 'rewardPrice',
                    key: 'rewardPrice',
                    hidden: rewardEnabled ? false : true,
                    render: (_value: number) => (_value ? `￥${uNumber.centToYuan(_value || 0)}` : '--'),
                },
                {
                    title: '操作',
                    key: 'status',
                    width: 100,
                    dataIndex: 'status',
                    hidden: activeKey === '1' ? false : true,
                    render: (_text: string, item) =>
                        isLiveEnd || !item?.exclusiveRetailPrice ? (
                            ''
                        ) : (
                            <Popconfirm
                                title={item.listedFlag ? '下架当前专享价商品，是否继续？' : '上架当前专享价商品，是否继续？'}
                                // disabled={isView}
                                onConfirm={() => handleGoodsSkuPriceMgrOnAndOff(item.skuId || 0, record.liveId, item)}
                            >
                                <Button
                                    type='link'
                                    className='handle-btn'
                                    // disabled={isView}
                                >
                                    {item.listedFlag ? '下' : '上'}架专享价
                                </Button>
                            </Popconfirm>
                        ),
                },
            ]}
        />
    );

    const handleChangeSoft = (value: number) => {
        categoryIdRef.current = value;
        getGoodsList(true);
    };
    const handleChangeSoftDone = (value: number) => {
        categoryIdRef.current = value;
        getGoodsList(true);
    };

    const handlePublishLiveGoods = (spuId: number) => {
        setLoading(true);
        publishLiveGoods({
            liveId: +liveId,
            spuId,
        })
            .then(() => {
                getGoodsList(true);
                message.success('开售成功');
            })
            .finally(() => {
                setLoading(false);
            });
    };

    return (
        <Spin spinning={loading}>
            <ProCard
                title={
                    <Tabs
                        defaultActiveKey={activeKey}
                        style={{
                            padding: '0px',
                            overflow: 'auto',
                        }}
                        items={[
                            {
                                key: '1',
                                label: `${goodsCount.count > 0 ? `直播商品 (${goodsCount.count})` : '直播商品'}`,
                            },
                            {
                                key: '0',
                                label: `${goodsCount.unUpCount > 0 ? `待上架 (${goodsCount.unUpCount})` : '待上架'}`,
                            },
                        ]}
                        className='liveGoodsListTab'
                        onChange={onChange}
                    />
                }
                // boxShadow
                extra={extra}
            >
                {showSelectModal && (
                    <SelectGoodsModal
                        isLive
                        liveId={+liveId}
                        goodsUpFlag={1}
                        goodsStatus={[3, 4]}
                        hasCheckedSpuIds={goodsList.map(v => v.spuId)}
                        disabledSpuIds={[]}
                        onOk={handleSetData}
                        onCancel={() => {
                            setShowSelectModal(false);
                        }}
                        rewardEnabled={rewardEnabled}
                        onAddLiveGoods={handleUp}
                    />
                )}
                <Table
                    rowKey={'spuId'}
                    pagination={false}
                    dataSource={goodsList}
                    style={{ flex: 1, overflow: 'auto', padding: '0px' }}
                    className='live-good-table'
                    title={
                        () => (
                            // goodsList.length ? (
                            <ListSoft
                                ref={listSoftRef}
                                onChange={handleChangeSoft}
                                onChangeDone={handleChangeSoftDone}
                                liveId={+liveId}
                            />
                        )
                        // ) : null
                    }
                    columns={activeKey === '0' ? columns : columns2}
                    rowSelection={
                        showRowSelection
                            ? {
                                  type: 'checkbox',
                                  selectedRowKeys,
                                  onChange: handleSelectChange,
                              }
                            : undefined
                    }
                    expandable={{
                        expandedRowKeys: expandSpuIds,
                        expandedRowRender: renderSubTable,
                        onExpand: handleExpand,
                        // defaultExpandedRowKeys: items.map(item => item.spuId),
                        // defaultExpandAllRows: true,
                    }}
                    scroll={{ x: 300 }}
                />
                {hasMoreData && (
                    <Button onClick={handleMore} type='link'>
                        加载更多
                    </Button>
                )}

                {selectRecord ? (
                    <EditingScript
                        open={showEditingScript}
                        item={selectRecord}
                        scriptTabType={toEditScriptType}
                        setOpen={() => {
                            setShowEditingScript(false);
                        }}
                    />
                ) : null}

                {selectRecord ? (
                    <EditingShopInfo
                        open={showEditingShopInfo}
                        item={selectRecord}
                        liveId={+liveId}
                        setOpen={() => {
                            setShowEditingShopInfo(false);
                        }}
                        onDone={() => {
                            getGoodsList(true);
                        }}
                    />
                ) : null}

                {selectRecords.length ? (
                    <EditingBatchShop
                        open={showBatchEditShop}
                        items={selectRecords}
                        liveId={+liveId}
                        setOpen={() => {
                            setShowBatchEditShop(false);
                        }}
                        onDone={handleRefresh}
                    />
                ) : null}
            </ProCard>
        </Spin>
    );
};

export default LiveGoodsList;
