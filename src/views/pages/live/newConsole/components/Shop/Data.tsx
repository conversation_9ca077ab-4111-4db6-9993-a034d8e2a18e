/**
 * @Owners lzy
 * @Title 用户列表
 */
import { type Console } from '@/api/interface/live/console';
import { trendData } from '@/api/modules/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import Show from '@/views/components/common/Show';
import { Empty } from 'antd';
import * as echarts from 'echarts';
import React, { useEffect, useRef, useState } from 'react';

import './index.scss';

type Props = {
    liveId: number;
    isLiveEnd: boolean;
};

interface TooltipDataParams {
    name: string;
    value: number;
    marker: string;
    seriesName: string;
}
const App: React.FC<Props> = ({ liveId, isLiveEnd }) => {
    const [data, setData] = useState<Console.Response.LiveTrendsDataDTO[]>([]);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const myChartRef = useRef<echarts.EChartsType | null>(null);

    useEffect(() => {
        handleGetData();
        handleStart();
        return () => {
            handleEnd();
        };
    }, []);

    useEffect(() => {
        if (isLiveEnd) handleEnd();
    }, [isLiveEnd]);

    const handleGetData = () => {
        trendData({ liveId }).then(res => {
            if (!res.data?.length) {
                return;
            }
            setData(res.data);
            handleSetEchart(res.data);
        });
    };

    const extractHourAndMinuteFromDate = (dateTime: string): string => {
        const date = new Date(dateTime);
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        return `${hours}:${minutes}`;
    };
    const handleSetEchart = (_data: Console.Response.LiveTrendsDataDTO[]) => {
        if (!myChartRef.current) {
            myChartRef.current = echarts.init(document.getElementById('echatmain'));
        }
        type NewData = {
            dataTime: string[];
            currentViewsOnlineUsers: number[];
        };
        const newData: NewData = {
            currentViewsOnlineUsers: [],
            dataTime: [],
        };
        _data.map(item => {
            newData.dataTime.push(item.dataTime);
            newData.currentViewsOnlineUsers.push(item.currentViewsOnlineUsers);
        });
        const option = {
            title: {
                text: '在线人数',
            },
            tooltip: {
                trigger: 'axis',
                formatter: (params: TooltipDataParams[]) => {
                    let result = params[0].name + '<br/>';
                    params.forEach(item => {
                        let value: number | string = item.value;
                        if (value >= 10000) {
                            value = (value / 10000).toFixed(2) + '万';
                        } else if (value >= 1000) {
                            value = (value / 1000).toFixed(2) + '千';
                        }
                        result += `${item.marker} ${item.seriesName}: ${value} <br/>`;
                    });
                    return result;
                },
            },
            // legend: {
            // data: ['Email'],
            // },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '10%',
                containLabel: true,
            },
            // toolbox: {
            //     feature: {
            //         saveAsImage: {},
            //     },
            // },
            xAxis: {
                type: 'category',
                data: newData.dataTime,
                boundaryGap: false,
                // type: 'time',
                axisLabel: {
                    formatter: extractHourAndMinuteFromDate,
                },
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter(value: number) {
                        if (value >= 10000) {
                            return (value / 10000).toFixed(0) + 'w';
                        } else if (value >= 1000) {
                            return (value / 1000).toFixed(0) + 'k';
                        }
                        return value;
                    },
                },
            },
            series: [
                {
                    name: '在线人数',
                    type: 'line',
                    stack: 'Total',
                    data: newData.currentViewsOnlineUsers,
                    lineStyle: {
                        color: '#00B68F', // 修改线条颜色
                    },
                    itemStyle: {
                        color: '#00B68F', // 修改拐点的颜色
                    },
                    smooth: true, // 平滑线条
                    symbol: 'none', // 不显示数据点
                },
            ],
            dataZoom: [
                {
                    // left: '20%',
                    // right: '20%',
                    type: 'slider', // 滑动条
                    show: true,
                    xAxisIndex: 0, // 绑定 X 轴
                    start: 0, // 显示数据范围起始百分比
                    end: 100, // 显示数据范围终止百分比
                    // backgroundColor: '#ebfaf6', // 整体背景色
                    // dataBackground: {
                    // lineStyle: {
                    //     color: '#ebfaf6', // 背景线颜色
                    // },
                    // areaStyle: {
                    //     color: '#ebfaf6', // 背景填充颜色
                    // },
                    // },
                    fillerColor: '#ebfaf6',
                },
                {
                    type: 'inside', // 支持鼠标滚轮缩放
                    xAxisIndex: 0,
                },
            ],
        };
        // myChartRef?.current?.clear();
        myChartRef?.current?.setOption(option);
    };

    const handleStart = () => {
        handleEnd();
        intervalRef.current = setInterval(() => {
            handleGetData();
        }, 30 * 1000); // 5分钟 = 300000 毫秒
    };

    const handleEnd = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null; // 清除引用
        }
    };
    return (
        <Show
            when={data && data?.length > 0}
            fallback={<Empty style={{ marginTop: '160px' }} image={EmptyImg} description='暂无数据' />}
        >
            <div id='echatmain' style={{ width: '100%', height: '540px' }} />
        </Show>
    );
};

export default App;
