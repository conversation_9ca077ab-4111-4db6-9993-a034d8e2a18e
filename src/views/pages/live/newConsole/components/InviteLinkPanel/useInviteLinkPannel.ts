/**
 * @Owners ljh
 * @Title 邀请连线列表
 */
import { type Console } from '@/api/interface/live/console';
import { getAudienceList, inviteCoStream } from '@/api/modules/live/console';
import { debounce, throttle } from 'lodash';
import { type ChangeEvent, useCallback, useContext, useMemo, useRef, useState } from 'react';

import { LiveContext } from '../../contexts/LiveContext';

/** 每页大小 */
const PAGE_SIZE = 20;

/** 搜索防抖时间 */
const SEARCH_DEBOUNCE_TIME = 500;

export const useInviteLinkPannel = (liveId?: number) => {
    const { liveToast } = useContext(LiveContext);
    /** 观众列表 */
    const [audienceList, setAudienceList] = useState<Console.Response.AudienceRecord[]>([]);
    const [scrollToIndex, setScrollToIndex] = useState<number>();
    /** 是否正在加载 */
    const [loading, setLoading] = useState(false);
    /** 上一次请求的payload */
    const prevPayload = useRef<Console.Params.GetAudienceList>({});
    /** 请求id */
    const requestId = useRef(0);
    /** 是否还有更多 */
    const hasMore = useRef(true);

    /**
     * 获取观众列表
     * @param payload 请求参数
     */
    const fetchAudienceList = useCallback(
        async (payload: Console.Params.GetAudienceList) => {
            const { page = 1 } = payload;
            if (loading && page !== 1) return;
            requestId.current++;
            const currentRequestId = requestId.current;

            try {
                setLoading(true);
                const { data } = await getAudienceList({
                    ...payload,
                    condition: {
                        liveId,
                        ...payload?.condition,
                    },
                });
                const list = data?.dataList ?? [];
                if (currentRequestId !== requestId.current) return;

                if (payload?.condition?.ifRefresh) {
                    delete payload.condition.ifRefresh;
                }
                prevPayload.current = payload;
                setAudienceList(prev => (page <= 1 ? list : prev.concat(list)));
                // 如果当前是第一页，则滚动到顶部
                if (page <= 1) {
                    setScrollToIndex(0);
                }
                hasMore.current = list.length >= PAGE_SIZE;
            } catch (error) {
                console.error(error);
            } finally {
                if (currentRequestId === requestId.current) {
                    setLoading(false);
                }
            }
        },
        [loading, liveId]
    );

    /**
     * 搜索
     * @param event 事件对象
     */
    const handleSearch = useMemo(() => {
        const loadOptions = (event: ChangeEvent<HTMLInputElement>) => {
            const { value } = event.target;
            fetchAudienceList({
                ...prevPayload.current,
                page: 1,
                condition: {
                    liveId,
                    nickname: value,
                },
            });
        };

        return debounce(loadOptions, SEARCH_DEBOUNCE_TIME);
    }, [fetchAudienceList, liveId]);

    /**
     * 加载更多
     */
    const handleLoadMore = useCallback(() => {
        const currentPage = prevPayload.current.page ?? 1;
        fetchAudienceList({ ...prevPayload.current, page: currentPage + 1 });
    }, [fetchAudienceList]);

    /**
     * 刷新
     */
    const handleRefresh = useCallback(async () => {
        fetchAudienceList({
            ...prevPayload.current,
            page: 1,
            condition: {
                ...prevPayload.current?.condition,
                ifRefresh: 1,
            },
        });
    }, [fetchAudienceList]);

    /**
     * 更新用户连麦状态
     * @param item 观众信息
     */
    const handleUpdateUserInviteStatus = useCallback(
        (item: (typeof audienceList)[number]) => {
            // 更新连麦状态
            const newAudienceList = [...audienceList];
            const index = newAudienceList.findIndex(i => i.userId === item.userId);
            if (index !== -1) {
                newAudienceList.splice(index, 1, { ...newAudienceList[index], isInvites: 1 });
            }
            setAudienceList(newAudienceList);
        },
        [audienceList]
    );

    /**
     * 发起连麦邀请
     * @param item 观众信息
     */
    const handleInvite = useCallback(
        throttle(async (item: (typeof audienceList)[number]) => {
            await inviteCoStream({
                liveId,
                targetUsers: item.userId,
            });
            liveToast?.open({
                content: '已发起连麦邀请，等待对方应答',
            });
            handleUpdateUserInviteStatus(item);
        }, 1000),
        [handleUpdateUserInviteStatus, liveToast]
    );

    return {
        loading,
        audienceList,
        fetchAudienceList,
        handleSearch,
        handleLoadMore,
        handleRefresh,
        handleInvite,
        scrollToIndex,
        setScrollToIndex,
    };
};
