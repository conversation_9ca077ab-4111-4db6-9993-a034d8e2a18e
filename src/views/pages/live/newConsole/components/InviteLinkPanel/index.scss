.invite-link-panel {
    display: flex;
    flex-direction: column;
    height: 100%;

    .invite-link-panel-search {
        box-sizing: border-box;
        flex-shrink: 0;
        width: auto;
        margin: 0 24px 16px;

        .ant-input-prefix {
            color: #999;
        }
    }

    .invite-link-panel-empty {
        margin-top: 62px;
    }

    .invite-link-panel-title {
        margin-bottom: 16px;
        padding: 0 24px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }

    .invite-link-panel-content {
        width: 100%;
        height: 100%;
        scrollbar-width: thin;
        scrollbar-color: #eaeaea transparent;
    }

    .invite-link-panel-content-inner {
        padding: 0 24px;

        &::-webkit-scrollbar {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(241, 238, 238, 0.6);
        }
    }

    .invite-link-panel-loading {
        padding: 8px 0;
        text-align: center;
    }

    .invite-link-panel-invite-button {
        padding: 0;
        width: 54px;
        height: 28px;
        font-size: 12px;

        &:disabled {
            color: #fff;
            background: rgba(0, 182, 143, 0.3);
            border-color: transparent;
        }
    }
}
