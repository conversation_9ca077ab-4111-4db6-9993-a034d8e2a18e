/**
 * @Owners ljh
 * @Title 邀请连线列表
 */
import EmptyImg from '@/assets/images/empty-placeholder.png';
import Show from '@/views/components/common/Show';
import SvgIcon from '@/views/components/common/SvgIcon';
import { Button, Empty, Flex, Input, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useContext, useEffect } from 'react';
import { AutoSizer, List, type ListRowRenderer, type ScrollParams } from 'react-virtualized';

import { LiveContext } from '../../contexts/LiveContext';
import InteractiveUserItem from '../InteractiveUserItem';

import './index.scss';
import { useInviteLinkPannel } from './useInviteLinkPannel';

const InviteLinkPanel = () => {
    const { liveId } = useContext(LiveContext);
    const { audienceList, handleSearch, handleLoadMore, handleInvite, handleRefresh, scrollToIndex, setScrollToIndex } =
        useInviteLinkPannel(liveId);

    useEffect(() => {
        handleRefresh();
    }, []);

    /** 是否超过5分钟 */
    const isOverFiveMinutes = useCallback((targetTime: string) => {
        const FIVE_MINUTES_MS = 5 * 60; // 5 分钟的秒数
        const diffMs = Math.abs(dayjs().diff(dayjs(targetTime), 's'));
        return diffMs > FIVE_MINUTES_MS;
    }, []);

    /** 列表滚动 */
    const handleListScroll = useCallback(
        (event: ScrollParams) => {
            const { clientHeight, scrollHeight, scrollTop } = event;
            setScrollToIndex(undefined);

            if (scrollTop + clientHeight >= scrollHeight - 80) {
                handleLoadMore();
            }
        },
        [handleLoadMore]
    );

    /** 列表行渲染 */
    const rowRenderer: ListRowRenderer = useCallback(
        ({ key, style, index }) => {
            const item = audienceList[index];
            const isInvited = item.isInvites === 1;
            let btnText = '邀请';
            if (isInvited) {
                btnText = '已邀请';
            } else {
                btnText = item.lastRejectTime && !isOverFiveMinutes(item.lastRejectTime) ? '已邀请' : '邀请';
            }

            // const viewTimeInSeconds = item.viewTime;
            // let viewTimeText = '已观看';
            // if (viewTimeInSeconds < 3600) {
            //     viewTimeText += `${Math.floor(viewTimeInSeconds / 60)}分钟`;
            // } else {
            //     const hours = Math.floor(viewTimeInSeconds / 3600);
            //     const minutes = Math.floor((viewTimeInSeconds % 3600) / 60);
            //     viewTimeText += `${hours}小时${minutes}分钟`;
            // }

            return (
                <InteractiveUserItem
                    key={key}
                    style={style}
                    item={item}
                    right={
                        <Button
                            className='invite-link-panel-invite-button'
                            type='primary'
                            onClick={() => handleInvite(item)}
                            disabled={isInvited}
                        >
                            {btnText}
                        </Button>
                    }
                />
            );
        },
        [audienceList, handleInvite, isOverFiveMinutes]
    );
    return (
        <div className='invite-link-panel'>
            <Input
                className='invite-link-panel-search'
                prefix={<SvgIcon name='search-outlined' />}
                placeholder='搜索用户昵称'
                onChange={handleSearch}
                allowClear
            />
            <Flex className='invite-link-panel-title' justify='space-between' align='center'>
                <span>直播间观众</span>
                <Tooltip title='直播间观众列表需手动刷新'>
                    <Button
                        color='default'
                        variant='filled'
                        size='small'
                        icon={<SvgIcon name='refresh-outlined' />}
                        onClick={handleRefresh}
                    />
                </Tooltip>
            </Flex>
            <Show
                when={audienceList.length}
                fallback={<Empty className='invite-link-panel-empty' image={EmptyImg} description='暂无数据，请手动刷新' />}
            >
                <div className='invite-link-panel-content'>
                    <AutoSizer>
                        {({ width, height }: { width: number; height: number }) => (
                            <List
                                className='invite-link-panel-content-inner'
                                width={width}
                                height={height}
                                rowCount={audienceList.length}
                                rowHeight={60}
                                rowRenderer={rowRenderer}
                                overscanRowCount={10}
                                onScroll={handleListScroll}
                                scrollToIndex={scrollToIndex}
                            />
                        )}
                    </AutoSizer>
                </div>
            </Show>
            {/* <Show when={loading}>
                <div className='invite-link-panel-loading'>
                    <Spin size='small' />
                </div>
            </Show> */}
        </div>
    );
};

export default InviteLinkPanel;
