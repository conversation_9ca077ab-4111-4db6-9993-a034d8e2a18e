/**
 * @Owners lzy
 * @Title 编辑商品信息
 */

import { type Console } from '@/api/interface/live/console';
import { batchEditLiveGoods, getAllGoodsCategory } from '@/api/modules/live/console';
// import touchIcon from '@/assets/images/touchIcon.png';
import { modal } from '@/views/components/UseAppPrompt';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Button, Drawer, Flex, Form, Image, Select, Table, type TableColumnsType } from 'antd';
import { cloneDeep, isEqual } from 'lodash';
import { useEffect, useState } from 'react';

import { type GoodsItem } from '../Shop/LiveGoodsList';

import style from './index.module.scss';

const initData = {
    categoryName: '全部',
    id: 0,
    sort: 0,
};
type LiveGoodsCategory = Console.Response.LiveGoodsCategoryVO;

type DataType = GoodsItem;

type _Props = {
    open: boolean;
    items: GoodsItem[];
    liveId: number;
    setOpen(): void;
    onDone(): void;
};

const Index: React.FC<_Props> = ({ open, setOpen, onDone, items, liveId }) => {
    const [loading, setLoading] = useState(false);
    const [goodsCategory, setGoodsCategory] = useState<LiveGoodsCategory[]>([initData]);
    const [customerCategoryId, setCustomerCategoryId] = useState<number>(0);
    const [dataSource, setDataSource] = useState<DataType[]>([]);

    useEffect(() => {
        setDataSource(cloneDeep(items));
    }, [open]);

    useEffect(() => {
        handleReset();
        handleGetAllGoodsCategory();
    }, [items]);

    const handleGetAllGoodsCategory = () => {
        getAllGoodsCategory({ liveId }).then(res => {
            setGoodsCategory([initData, ...(res.data || [])]);
            setLoading(false);
        });
    };

    const handleReset = () => {
        setLoading(false);
        setCustomerCategoryId(0);
    };

    const handleOnClose = () => {
        if (!isEqual(dataSource, items || [])) {
            return modal.confirm({
                // title: '是否保存当前话术？',
                icon: <ExclamationCircleFilled />,
                content: '确定退出编辑？退出后已修改的内容将不保存',
                onOk() {
                    // console.log('OK');
                    // handleSubmit();
                    setOpen();
                },
                onCancel() {},
                okText: '确认',
                cancelText: '取消',
            });
        }
        setOpen();
    };
    const handleOnchange = (value: number) => {
        setCustomerCategoryId(value);
    };

    const handleOnchangeAll = () => {
        const newDataSource = dataSource.map(item => ({
            ...item,
            customerCategoryId,
        }));
        setDataSource(newDataSource);
    };

    const handleSubmit = () => {
        if (!dataSource.length) {
            return setOpen();
        }
        setLoading(true);
        const liveGoods = dataSource.map(item => {
            const { customerCategoryId, liveId, spuId } = item;
            return {
                // customSpuName,
                // sellingPoint,
                customerCategoryId: customerCategoryId === 0 ? undefined : customerCategoryId,
                liveId,
                spuId,
            };
        });
        // const liveGoods = dataSource;
        batchEditLiveGoods({
            liveGoods,
            type: 2,
        })
            .then(() => {
                setOpen();
                onDone();
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const handleChangeName = (index: number, value: number) => {
        dataSource[index].customerCategoryId = value;
        setDataSource([...cloneDeep(dataSource)]);
    };

    const columns: TableColumnsType<DataType> = [
        {
            title: '商品名称',
            key: 'spuId',
            width: 300,
            dataIndex: 'spuId',
            // align: 'center',
            render: (_text: string, record: GoodsItem) => (
                <div className={style.goodsInfo} style={{ display: 'flex', flexDirection: 'row' }}>
                    <Image src={record.mainUrl || ''} className={style.img} />
                    <div className={style.info}>
                        {/* {getShortTitleBarNode(record)} */}
                        <div className={style.spuNamess}>{record.spuName}</div>
                        {/* {record.sellingPoint ? <div className='selling'>{record.sellingPoint}</div> : null} */}
                    </div>
                </div>
            ),
        },
        {
            title: '当前商品类型',
            key: 'twoCategoryName',
            width: 120,
            dataIndex: 'twoCategoryName',
            render: (_text: string, record: GoodsItem) => record.customerCategoryName || record.twoCategoryName,
        },
        {
            title: '改后商品类型',
            dataIndex: 'categoryName',
            render: (_text, _item, index) => (
                <Select
                    style={{
                        width: '100%',
                    }}
                    value={_item.customerCategoryId}
                    onChange={val => {
                        handleChangeName(index, val);
                    }}
                >
                    {goodsCategory.map(item => (
                        <Select.Option value={item.id} key={item.id}>
                            {item.categoryName}
                        </Select.Option>
                    ))}
                </Select>
            ),
        },
    ];

    return (
        <Drawer title='编辑商品信息' onClose={handleOnClose} open={open} width={900} className={`${style.container}`}>
            <Flex gap={16}>
                <Form.Item label='批量修改' className={style.lable}>
                    <Select value={customerCategoryId} onChange={handleOnchange}>
                        {goodsCategory.map(item => (
                            <Select.Option value={item.id} key={item.id}>
                                {item.categoryName}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
                <Button onClick={handleOnchangeAll} color='primary' variant='outlined'>
                    批量修改
                </Button>
            </Flex>
            <Table<DataType>
                rowKey='spuId'
                scroll={{
                    y: '74vh',
                }}
                columns={columns}
                dataSource={dataSource}
                pagination={false}
            />
            <div className={style.flotter}>
                <Button loading={loading} color='default' variant='outlined' disabled={loading} onClick={handleOnClose}>
                    取消
                </Button>
                <Button
                    onClick={handleSubmit}
                    loading={loading}
                    type='primary'
                    disabled={loading}
                    // icon={<img src={touchIcon} className={style.img} />}
                >
                    确认
                </Button>
            </div>
        </Drawer>
    );
};

export default Index;
