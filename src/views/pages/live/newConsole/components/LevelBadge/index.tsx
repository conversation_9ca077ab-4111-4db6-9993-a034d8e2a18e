/**
 * @Owners ljh
 * @Title 等级徽章
 */
import classNames from 'classnames';
import { memo, type CSSProperties, useMemo } from 'react';

import './index.scss';

/** 角色 */
export enum Role {
    /** 普通用户 */
    Normal = 1,
    /** 会员 */
    Vip = 2,
}

export interface LevelBadgeProps {
    className?: string;
    style?: CSSProperties;
    /** 当前等级 */
    level?: number;
    /** 徽章主题 */
    theme?: 'gold' | 'green';
    /** icon */
    icon?: string;
    onClick?(): void;
}

const greenBadgeColor = ['#3AB297', '#3AB297', '#3AB297', '#3AB297', '#1B9B7E', '#1B9B7E', '#0D8469'];

const goldBadgeColor = ['#FF9900', '#FF9900', '#FF9900', '#FF9900', '#D06400', '#9D4800', '#FF5F20'];

export const LevelBadge = memo<LevelBadgeProps>(props => {
    const {
        level,
        theme = 'green',
        onClick,
        className,
        style,
        icon = 'https://stantic.ifengqun.com/new-fengqun/fq-mall/common/global-vip-badge-1.png',
    } = props;

    const classes = classNames('level-badge', className);

    /** 返回等级索引 */
    const levelIndex = useMemo(() => Math.min(Math.floor((level ?? 0) / 10) || 0, 6), [level]);

    return !!level ? (
        <div
            className={classes}
            style={{ background: theme === 'gold' ? 'linear-gradient(112deg, #FFEEED 8%, #FFF0C5 98%)' : '#D6F4EA', ...style }}
            onClick={onClick}
        >
            <img className='level-badge-icon' src={icon} />
            <div
                className='level-badge-text'
                style={{
                    color: theme === 'gold' ? goldBadgeColor[levelIndex] : greenBadgeColor[levelIndex],
                    paddingRight: level < 10 ? '5px' : '4px',
                }}
            >
                {level}
            </div>
        </div>
    ) : null;
});
