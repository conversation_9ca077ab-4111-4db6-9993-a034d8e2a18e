/**
 * @Owners ljh
 * @Title 申请连麦列表
 */
import { type Console } from '@/api/interface/live/console';
import { acceptApplyCo, getApplyCoList } from '@/api/modules/live/console';
import { debounce } from 'lodash';
import { type ChangeEvent, useCallback, useMemo, useRef, useState } from 'react';

/** 每页大小 */
const PAGE_SIZE = 20;

/** 搜索防抖时间 */
const SEARCH_DEBOUNCE_TIME = 500;

export const useApplyCoList = (liveId?: number) => {
    /** 申请列表 */
    const [applyList, setApplyList] = useState<Console.Response.ApplyCoRecord[]>([]);

    const [scrollToIndex, setScrollToIndex] = useState<number>();
    /** 连麦列表是否正在加载 */
    const [loading, setLoading] = useState(false);
    /** 上一次请求的payload */
    const prevPayload = useRef<Console.Params.GetApplyCoList>({});
    /** 请求id */
    const requestId = useRef(0);
    /** 是否还有更多 */
    const hasMore = useRef(true);
    /** 总条数 */
    const [total, setTotal] = useState(0);

    /**
     * 获取观众列表
     * @param payload 请求参数
     */
    const fetchApplyList = useCallback(
        async (payload: Console.Params.GetApplyCoList, refresh = false) => {
            if (!refresh && (loading || !hasMore.current)) return;
            requestId.current++;
            const currentRequestId = requestId.current;

            try {
                setLoading(true);
                const { data } = await getApplyCoList({
                    ...payload,
                    liveId,
                    size: PAGE_SIZE,
                });
                const list = data?.dataList ?? [];
                if (currentRequestId !== requestId.current) return;

                prevPayload.current = payload;
                setApplyList(prev => (refresh ? list : prev.concat(list)));
                // 如果当前是第一页，则滚动到顶部
                if (refresh) {
                    setTotal(data?.total ?? 0);
                    setScrollToIndex(0);
                }
                hasMore.current = list.length >= PAGE_SIZE;
            } catch (error) {
                console.error(error);
            } finally {
                if (currentRequestId === requestId.current) {
                    setLoading(false);
                }
            }
        },
        [loading, liveId]
    );

    /**
     * 搜索
     * @param event 事件对象
     */
    const handleSearch = useMemo(() => {
        const loadOptions = (event: ChangeEvent<HTMLInputElement>) => {
            const { value } = event.target;
            fetchApplyList(
                {
                    nickname: !!value ? value : undefined,
                },
                true
            );
        };

        return debounce(loadOptions, SEARCH_DEBOUNCE_TIME);
    }, [fetchApplyList, liveId]);

    /**
     * 加载更多
     */
    const handleLoadMore = useCallback(() => {
        fetchApplyList({ ...prevPayload.current, anchorApplyId: applyList[applyList.length - 1]?.id });
    }, [fetchApplyList, applyList]);

    /**
     * 刷新
     */
    const handleRefresh = useCallback(async () => {
        fetchApplyList({}, true);
    }, [fetchApplyList]);

    /**
     * 同意连麦
     */
    const handleApply = useCallback(
        async (info: Console.Response.ApplyCoRecord) => {
            await acceptApplyCo({
                id: info.id,
                liveId,
                userId: info.userId,
                nickname: info.nickname,
            });
        },
        [liveId]
    );

    return {
        loading,
        hasMore,
        applyList,
        setApplyList,
        total,
        setTotal,
        handleSearch,
        handleLoadMore,
        handleRefresh,
        handleApply,
        scrollToIndex,
        setScrollToIndex,
        prevPayload,
    };
};
