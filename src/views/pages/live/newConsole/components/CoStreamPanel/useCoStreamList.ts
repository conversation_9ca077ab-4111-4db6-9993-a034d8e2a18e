/**
 * @Owners ljh
 * @Title 连麦列表
 */
import { type Console } from '@/api/interface/live/console';
import { getCoStreamList } from '@/api/modules/live/console';
import { useCallback, useState } from 'react';

export const useCoStreamList = (liveId?: number) => {
    /** 连麦列表 */
    const [coStreamList, setCoStreamList] = useState<Console.Response.CoStreamRecord[]>([]);

    /** 是否正在加载 */
    const [loading, setLoading] = useState(false);

    /**
     * 获取连麦列表
     */
    const fetchCoStreamList = useCallback(async () => {
        if (loading) return;
        setLoading(true);
        try {
            const res = await getCoStreamList({
                liveId,
            });
            setCoStreamList(res?.data?.seatsList ?? []);
        } catch (error) {
            throw error;
        } finally {
            setLoading(false);
        }
    }, [getCoStreamList, loading, liveId]);

    return {
        loading,
        coStreamList,
        setCoStreamList,
        fetchCoStreamList,
    };
};
