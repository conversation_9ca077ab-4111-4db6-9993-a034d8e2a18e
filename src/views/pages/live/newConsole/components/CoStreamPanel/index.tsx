/**
 * @Owners ljh
 * @Title 连麦列表
 */

import { type Console } from '@/api/interface/live/console';
import EmptyImg from '@/assets/images/empty-placeholder.png';
import { CoStreamCameraStatus, CoStreamMicStatus } from '@/consts/cLive';
import Show from '@/views/components/common/Show';
import SvgIcon from '@/views/components/common/SvgIcon';
import { RightOutlined } from '@ant-design/icons';
import { Button, Empty, Flex, Input, Modal, Tooltip } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { AutoSizer, List, type ListRowRenderer, type ScrollParams } from 'react-virtualized';

import InteractiveUserItem from '../InteractiveUserItem';

import './index.scss';
import { useCoStreamPanel } from './useCoStreamPanel';

export interface CoStreamPanelProps {
    /** 邀请连线 */
    onInvite?(): void;
}

const CoStreamPanel = (props: CoStreamPanelProps) => {
    const { onInvite } = props;
    const {
        coStreamList,
        applyTotal,
        applyList,
        fetchCoStreamList,
        handleRefresh,
        handleLoadMore,
        toggleUserMic,
        toggleUserCamera,
        handleHangUp,
        handleApplyCo,
        handleSearch,
    } = useCoStreamPanel();

    const [applyCoListModalOpen, setApplyCoListModalOpen] = useState(false); // 申请连麦列表弹窗

    useEffect(() => {
        fetchCoStreamList();
        handleRefresh();
    }, []);

    const handleOpenApplyCoListModal = useCallback(() => {
        setApplyCoListModalOpen(true);
    }, []);

    const handleCloseApplyCoListModal = useCallback(() => {
        setApplyCoListModalOpen(false);
    }, []);

    /** 列表滚动 */
    const handleListScroll = useCallback(
        (event: ScrollParams) => {
            const { clientHeight, scrollHeight, scrollTop } = event;
            // setScrollToIndex(undefined);

            if (scrollTop + clientHeight >= scrollHeight - 80) {
                handleLoadMore();
            }
        },
        [handleLoadMore]
    );

    /** 是否是连麦记录 */
    const isCoStreamRecord = useCallback(
        (item: Console.Response.ApplyCoRecord | Console.Response.CoStreamRecord): item is Console.Response.CoStreamRecord =>
            (item as Console.Response.CoStreamRecord).trtcPlayUrl !== undefined,
        []
    );

    /** 右侧操作 */
    const renderRightActions = useCallback(
        (item: Console.Response.ApplyCoRecord | Console.Response.CoStreamRecord) => {
            if (!isCoStreamRecord(item)) {
                return (
                    <Button
                        className='invite-link-panel-invite-button'
                        type='primary'
                        onClick={() => handleApplyCo(item)}
                        disabled={!!item.isAccept}
                    >
                        {item.isAccept ? '已接受' : '接受'}
                    </Button>
                );
            }

            const { cameraStatus, isMuted } = item;
            /** 摄像头是否开启 */
            const isCameraOpen = cameraStatus === CoStreamCameraStatus.Open;
            /** 麦克风是否静音 */
            const isMicMuted = isMuted === CoStreamMicStatus.Close;
            const cameraIcon = isCameraOpen ? 'video-camera-filled' : 'close-camera-filled';
            const cameraTooltip = isCameraOpen ? '摄像头已开' : '摄像头已关闭';
            const micIcon = isMicMuted ? 'close-audio-filled' : 'audio-filled';
            const micTooltip = isMicMuted ? '麦克风已关闭' : '麦克风已开启';

            return (
                <Flex gap={8}>
                    <Tooltip title={micTooltip}>
                        <Button
                            className='co-stream-panel-block-icon'
                            color='default'
                            variant='text'
                            size='small'
                            icon={<SvgIcon name={micIcon} />}
                            onClick={() => toggleUserMic(item)}
                        />
                    </Tooltip>
                    <Tooltip title={cameraTooltip}>
                        <Button
                            className='co-stream-panel-block-icon'
                            color='default'
                            variant='text'
                            size='small'
                            icon={<SvgIcon name={cameraIcon} />}
                            onClick={() => toggleUserCamera(item)}
                        />
                    </Tooltip>
                    <Tooltip title='挂断'>
                        <Button
                            className='co-stream-panel-block-icon'
                            color='default'
                            variant='text'
                            size='small'
                            icon={<SvgIcon name='hang-up' />}
                            onClick={() => handleHangUp(item)}
                        />
                    </Tooltip>
                </Flex>
            );
        },
        [handleHangUp, handleApplyCo, isCoStreamRecord, toggleUserCamera, toggleUserMic]
    );

    /** 列表行渲染 */
    const rowRenderer: ListRowRenderer = useCallback(
        ({ style, index }) => {
            const item = applyList[index];

            return <InteractiveUserItem key={item.userId} item={item} style={style} right={renderRightActions(item)} />;
        },
        [applyList, renderRightActions]
    );

    return (
        <div className='co-stream-panel'>
            <Input
                className='co-stream-panel-search'
                prefix={<SvgIcon name='search-outlined' />}
                placeholder='搜索用户昵称'
                onChange={handleSearch}
            />

            <Show
                when={!!coStreamList.length || !!applyList.length}
                fallback={
                    <Empty
                        className='co-stream-panel-empty'
                        image={EmptyImg}
                        description={
                            <Button type='primary' onClick={onInvite}>
                                邀请观众连线
                            </Button>
                        }
                    />
                }
            >
                <div className='co-stream-panel-block co-stream-panel-block-stable co-stream-panel-block--empty'>
                    <div className='co-stream-panel-block-title'>
                        已连麦
                        <span className='co-stream-panel-list-count'>
                            ({coStreamList.length}/{4})
                        </span>
                    </div>
                    <div className='co-stream-panel-block-content'>
                        <Show
                            when={!!coStreamList.length}
                            fallback={
                                <Empty className='co-stream-panel-block-empty' image={EmptyImg} description='暂无已连麦观众' />
                            }
                        >
                            <div className='co-stream-panel-block-list'>
                                {coStreamList.map(item => (
                                    <InteractiveUserItem key={item.userId} item={item} right={renderRightActions(item)} />
                                ))}
                            </div>
                        </Show>
                    </div>
                </div>

                {/* S 申请连麦 */}
                <div className='co-stream-panel-block co-stream-panel-block-flex'>
                    <div className='co-stream-panel-block-title'>
                        申请连麦<span className='co-stream-panel-list-count'>({applyTotal})</span>
                    </div>
                    <div className='co-stream-panel-block-content'>
                        <AutoSizer>
                            {({ width, height }: { width: number; height: number }) => (
                                <List
                                    className='co-stream-panel-block-list'
                                    width={width}
                                    height={height}
                                    rowCount={applyList.length}
                                    rowHeight={60}
                                    rowRenderer={rowRenderer}
                                    overscanRowCount={10}
                                    onScroll={handleListScroll}
                                />
                            )}
                        </AutoSizer>
                    </div>
                    <div className='co-stream-panel-block-footer'>
                        <Button block className='co-stream-panel-block-button' onClick={handleOpenApplyCoListModal}>
                            查看全部
                            <RightOutlined />
                        </Button>
                    </div>
                </div>
                {/* E 申请连麦 */}
            </Show>

            {/* S 申请连麦列表弹窗 */}
            <Modal
                className='apply-co-list-modal'
                open={applyCoListModalOpen}
                width={560}
                title='申请连麦'
                footer={null}
                destroyOnClose
                onCancel={handleCloseApplyCoListModal}
            >
                <div style={{ height: '530px' }}>
                    <AutoSizer>
                        {({ width, height }: { width: number; height: number }) => (
                            <List
                                className='apply-co-list-modal-list'
                                width={width}
                                height={height}
                                rowCount={applyList.length}
                                rowHeight={60}
                                rowRenderer={rowRenderer}
                                overscanRowCount={10}
                                onScroll={handleListScroll}
                            />
                        )}
                    </AutoSizer>
                </div>
            </Modal>
            {/* E 申请连麦列表弹窗 */}
        </div>
    );
};

export default CoStreamPanel;
