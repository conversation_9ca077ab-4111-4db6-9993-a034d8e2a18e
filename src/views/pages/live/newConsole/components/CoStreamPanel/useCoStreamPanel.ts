/**
 * @Owners ljh
 * @Title 邀请连线列表
 */
import { type Console } from '@/api/interface/live/console';
import { diconnectStreamOfUser, updateCameraStatusOfUser, updateMicStatusOfUser } from '@/api/modules/live/console';
import { CoStreamCameraStatus, CoStreamMicStatus } from '@/consts/cLive';
import { uString } from '@/utils';
import { modal } from '@/views/components/UseAppPrompt';
import { cLive } from '@consts';
import TencentCloudChat from '@tencentcloud/chat';
import { throttle } from 'lodash';
import { useCallback, useContext, useEffect, useMemo } from 'react';

import { LiveContext } from '../../contexts/LiveContext';

import { useApplyCoList } from './useApplyCoList';
import { useCoStreamList } from './useCoStreamList';

export const useCoStreamPanel = () => {
    const { liveToast, liveId, chatInited, chat } = useContext(LiveContext);

    const { coStreamList, fetchCoStreamList, loading: coStreamLoading, setCoStreamList } = useCoStreamList(liveId);
    const {
        prevPayload: applyPrevPayload,
        hasMore: applyListHasMore,
        applyList,
        setApplyList,
        total: applyTotal,
        setTotal: setApplyTotal,
        loading: applyLoading,
        handleLoadMore,
        handleRefresh,
        handleApply,
        handleSearch,
    } = useApplyCoList(liveId);

    const handleCustomMessage = useMemo<Record<string, (msg: Console.ChatMessage) => void>>(
        () => ({
            /** 主播关闭/开启摄像头 */
            [cLive.IMMessageCustomEvent.ManageCameraOff]: (msg: Console.ChatMessage) => {
                console.log('主播关闭/开启摄像头', msg);
                const { data } = msg.payload;
                const { targetUserId, cameraEnabled } = data;
                // 因为开启摄像头是邀请模式，所以不能直接修改摄像头状态
                if (!targetUserId || cameraEnabled) return;

                setCoStreamList(prev => {
                    const targetIndex = prev.findIndex(item => item.userId === targetUserId);
                    if (targetIndex === -1) return prev;

                    const list = [...prev];
                    list.splice(targetIndex, 1, {
                        ...list[targetIndex],
                        cameraStatus: cameraEnabled ? CoStreamCameraStatus.Open : CoStreamCameraStatus.Close,
                    });
                    return list;
                });
            },
            /** 观众切换媒体类型 */
            [cLive.IMMessageCustomEvent.ManageMicUserSwitchMediaType]: (msg: Console.ChatMessage) => {
                console.log('观众切换媒体类型', msg);
                const { data } = msg.payload;
                const { userId, mediaType } = data;

                if (!userId) return;

                setCoStreamList(prev => {
                    const targetIndex = prev.findIndex(item => item.userId === userId);
                    if (targetIndex === -1) return prev;

                    const list = [...prev];
                    list.splice(targetIndex, 1, {
                        ...list[targetIndex],
                        cameraStatus: mediaType === 2 ? CoStreamCameraStatus.Open : CoStreamCameraStatus.Close,
                    });
                    return list;
                });
            },
            /** 主播静音/取消静音 */
            [cLive.IMMessageCustomEvent.ManageMicMute]: (msg: Console.ChatMessage) => {
                console.log('主播静音/取消静音', msg);
                const { data } = msg.payload;
                const { targetUserId, isMuted } = data;

                if (!targetUserId) return;

                setCoStreamList(prev => {
                    const targetIndex = prev.findIndex(item => item.userId === targetUserId);
                    if (targetIndex === -1) return prev;

                    const list = [...prev];
                    list.splice(targetIndex, 1, {
                        ...list[targetIndex],
                        isMuted: isMuted ? CoStreamMicStatus.Close : CoStreamMicStatus.Open,
                    });
                    return list;
                });
            },
            /** 用户操作静音与解除静音 */
            [cLive.IMMessageCustomEvent.ManageMicMuteUser]: (msg: Console.ChatMessage) => {
                console.log('用户操作静音与解除静音', msg);
                const { data } = msg.payload;
                const { userId, isMuted } = data;

                if (!userId) return;

                setCoStreamList(prev => {
                    const targetIndex = prev.findIndex(item => item.userId === userId);
                    if (targetIndex === -1) return prev;

                    const list = [...prev];
                    list.splice(targetIndex, 1, {
                        ...list[targetIndex],
                        isMuted: isMuted ? CoStreamMicStatus.Close : CoStreamMicStatus.Open,
                    });
                    return list;
                });
            },
            /** 主播踢出连麦用户 */
            [cLive.IMMessageCustomEvent.ManageMicKick]: (msg: Console.ChatMessage) => {
                const { data } = msg.payload;
                const { targetUserId } = data.data;

                setCoStreamList(prev => prev.filter(item => item.userId !== targetUserId));
            },
            /** 观众申请连麦 */
            [cLive.IMMessageCustomEvent.ApplyMicRequestWait]: (msg: Console.ChatMessage) => {
                console.log('观众申请连麦', msg);
                // remark 只有没有下一页才监听
                if (applyListHasMore.current || applyPrevPayload.current.nickname) return;

                const { data } = msg.payload;
                const { avatar, isFollow, level, levelIcon, liveId, nickname, role, userId, applyId } = data;
                setApplyList(prev => {
                    const target = prev.find(item => item.userId === userId);
                    if (target) return prev;

                    setApplyTotal(prevTotal => prevTotal + 1);
                    return prev.concat([
                        {
                            applyTime: '',
                            avatar,
                            id: applyId,
                            isFollow,
                            level,
                            levelIcon,
                            liveId,
                            liveMicHistoryType: 3,
                            nickname,
                            role,
                            userId,
                            viewTime: 0,
                        },
                    ]);
                });
            },
            /** 观众取消连麦 */
            [cLive.IMMessageCustomEvent.ApplyMicCancel]: (msg: Console.ChatMessage) => {
                console.log('观众取消连麦', msg);
                const { data } = msg.payload;
                const { userId } = data;
                setApplyList(prev => prev.filter(item => item.userId !== userId));
                setApplyTotal(prev => prev - 1);
            },
            /** 主播接受用户申请连麦 */
            [cLive.IMMessageCustomEvent.ApplyMicAccept]: (msg: Console.ChatMessage) => {
                console.log('主播接受用户申请连麦', msg);
                const { data } = msg.payload;
                const { userId } = data;
                fetchCoStreamList();
                setApplyList(prev => prev.filter(item => item.userId !== userId));
                setApplyTotal(prev => prev - 1);
            },
            /** 观众同意/拒绝连麦邀请 */
            [cLive.IMMessageCustomEvent.InviteMicResponse]: (msg: Console.ChatMessage) => {
                console.log('观众同意/拒绝连麦邀请', msg);
                const { data } = msg.payload;
                const { targetUserId, isAccept } = data;

                if (!isAccept) {
                    return;
                }

                fetchCoStreamList();
                setApplyList(prev => {
                    const targetIndex = prev.findIndex(item => item.userId === targetUserId);
                    if (targetIndex === -1) return prev;

                    setApplyTotal(prev => prev - 1);
                    return prev.filter(item => item.userId !== targetUserId);
                });
            },
            /** 观众主动断开连麦 */
            [cLive.IMMessageCustomEvent.ManageMicUserDisconnect]: (msg: Console.ChatMessage) => {
                const { data } = msg.payload;
                const { userId } = data;
                setCoStreamList(prev => prev.filter(item => item.userId !== userId));
            },
        }),
        [liveToast]
    );

    /**
     * 处理接收到的消息
     */
    const handleReceivedMessage = useCallback((event: { name: string; data: Console.ChatMessage[] }) => {
        const { data } = event;

        data.forEach(msg => {
            const { type, payload } = msg;
            // 只处理自定义事件
            if (type !== TencentCloudChat.TYPES.MSG_CUSTOM) return;

            const customData = payload.data ? JSON.parse(payload.data) : {};
            const customEvent: string = customData.event ?? '';
            handleCustomMessage[customEvent] &&
                handleCustomMessage[customEvent]({
                    ...msg,
                    payload: {
                        ...msg.payload,
                        data: customData,
                    },
                });
        });
    }, []);

    useEffect(() => {
        if (chatInited && chat?.current) {
            chat.current.on(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
        }

        return () => {
            if (chat?.current) {
                chat.current.off(TencentCloudChat.EVENT.MESSAGE_RECEIVED, handleReceivedMessage);
            }
        };
    }, [chatInited, handleReceivedMessage]);

    /** 格式化用户名 */
    const formatUserName = useCallback((name: string) => (name.length > 5 ? `${name.slice(0, 5)}...` : name), []);

    /**
     * 切换麦克风
     */
    const toggleUserMic = useCallback(
        throttle(async (info: Console.Response.CoStreamRecord) => {
            const nextStatus = info.isMuted === CoStreamMicStatus.Close ? CoStreamMicStatus.Open : CoStreamMicStatus.Close;
            const nextStatusText = nextStatus === CoStreamMicStatus.Open ? '打开' : '关闭';

            await updateMicStatusOfUser({
                liveId,
                userId: info.userId,
                status: nextStatus,
            });
            liveToast?.open({
                content: `你已将用户${uString.stringEllipsis(info.nickname, 5)}的麦克风${nextStatusText}`,
            });
        }, 1000),
        [liveId, liveToast]
    );

    /**
     * 邀请用户开启摄像头
     */
    const toggleUserCamera = useCallback(
        throttle(async (info: Console.Response.CoStreamRecord) => {
            const nextStatus =
                info.cameraStatus === CoStreamCameraStatus.Open ? CoStreamCameraStatus.Close : CoStreamCameraStatus.Open;

            await updateCameraStatusOfUser({
                liveId,
                userId: info.userId,
                status: nextStatus,
            });

            if (nextStatus === CoStreamCameraStatus.Open) {
                liveToast?.open({
                    content: `已发送视频邀请，等待${formatUserName(info.nickname)}同意`,
                });
            } else {
                liveToast?.open({
                    content: `你已将用户${formatUserName(info.nickname)}的摄像头关闭`,
                });
            }
        }, 1000),
        [liveId, liveToast]
    );

    /**
     * 挂断连线
     */
    const handleHangUp = useCallback(
        throttle((info: Console.Response.CoStreamRecord) => {
            modal.confirm({
                title: '提示',
                content: `确定要断开${info.nickname}的连线吗？`,
                okText: '确认断开',
                onOk: async () => {
                    await diconnectStreamOfUser({
                        liveId,
                        userId: info.userId,
                    });
                    setCoStreamList(prev => prev.filter(item => item.userId !== info.userId));
                    liveToast?.open({
                        content: '已挂断连线',
                    });
                },
            });
        }, 1000),
        [liveId, liveToast]
    );

    /**
     * 同意连麦
     */
    const handleApplyCo = useCallback(
        (info: Console.Response.ApplyCoRecord) => {
            if (coStreamList.length >= 4) {
                liveToast?.open({
                    content: '连线已满员，请先断开现有连线',
                });
                return;
            }
            handleApply(info);
        },
        [handleApply, coStreamList, liveToast]
    );

    return {
        coStreamLoading,
        applyLoading,
        coStreamList,
        applyList,
        applyTotal,
        fetchCoStreamList,
        handleLoadMore,
        handleRefresh,
        handleApplyCo,
        toggleUserMic,
        toggleUserCamera,
        handleHangUp,
        handleSearch,
    };
};
