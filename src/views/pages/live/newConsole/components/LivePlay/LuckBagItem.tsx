/**
 * @Owners wyy
 * @Title 新增、编辑、查看福袋
 */
// import { type LuckyBag } from '@/api/interface/luckybag';
import { addLuckyBag, editLuckyBag } from '@/api/modules/luckyBag';
import { Button, Col, Drawer, Form, Popconfirm, Row, Tag, message } from 'antd';
import { type FC, useEffect, useState } from 'react';

import LuckBagForm from './LuckBagForm';
import LuckBagDetail from './components/LuckBagDetail';
import PreviewImage from './components/PreviewImage';
import { LuckBagPreviewImages, TAG_COLOR_MAP } from './constant';
import { STATUS_MAP, type LuckBagItemProps } from './types';

const MODAL_TITLES = {
    add: '新增福袋',
    edit: '编辑福袋',
    view: (status?: keyof typeof STATUS_MAP) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span>福袋详情</span>
            {status !== undefined && (
                <Tag style={TAG_COLOR_MAP[status] || {}}>
                    {status === 2 && (
                        <img
                            src='https://applet.ifengqun.com/fq-mall/prod/discover/discover-loading.gif'
                            style={{
                                width: '12px',
                                height: '12px',
                                marginRight: '4px',
                            }}
                            alt=''
                        />
                    )}
                    {STATUS_MAP[status]}
                </Tag>
            )}
        </div>
    ),
};

interface CouponListItem {
    couponId: number;
    sort: number;
    transferNum: number;
}

export type FormValues = {
    couponId: number;
    condition: string;
    conditionName?: string;
    inviteCount?: number;
    limitType: string;
    limitCount?: number;
    startType: string;
    countdownSeconds?: number;
    countdown: string;
    remark?: string;
    couponList?: number | string;
    awards?: {
        id: string;
        name: string;
        type: string;
        rule: string;
        validityPeriod: string;
        useRule: string;
        stock: number;
        issueCount?: number;
    };
};

const LuckBagItem: FC<LuckBagItemProps> = ({ open, mode = 'add', initialValues, status, onCancel, liveId, onSuccess, bagId }) => {
    const [openPopConfirm, setOpenPopConfirm] = useState(false);
    const [form] = Form.useForm<FormValues>();

    useEffect(() => {
        if (open && initialValues) {
            // 门槛条件映射
            const doorsillMap: { [key: number]: string } = {
                0: 'unlimited', // 不限
                1: 'follow', // 关注主播
                2: 'share', // 分享直播间
                3: 'invite', // 邀请好友
            };

            const distributeTypeMap: { [key: number]: string } = {
                1: 'manual', // 手动开始
                2: 'immediate', // 立即开始
                3: 'countdown', // 倒计时开始
            };

            const formValues = {
                condition: doorsillMap[initialValues.doorsill] || 'unlimited',
                inviteCount: initialValues.target || undefined,
                limitType: initialValues.limitNum ? 'limited' : 'unlimited',
                limitCount: initialValues.limitNum || 0,
                startType: distributeTypeMap[initialValues.distributeType] || 'immediate',
                countdownSeconds: initialValues.distributeRule?.countDownNum,
                remark: initialValues.name,
                couponList: initialValues.couponList[0].applyNum,
            };

            form.setFieldsValue(formValues);
        }
    }, [open, initialValues, form]);

    const handleOk = async () => {
        try {
            const values = await form.validateFields();
            // 参与条件映射
            const doorsillMap = {
                follow: 1, // 关注主播
                share: 2, // 分享直播间
                invite: 3, // 邀请好友
            };

            console.log('values', values);
            const couponList: CouponListItem[] = [
                {
                    couponId: values?.couponId,
                    sort: 0,
                    transferNum: Number(values?.couponList),
                },
            ];
            // 发放类型映射 1:手动开始 2:立即开始 3:倒计时开始
            const distributeTypeMap = {
                manual: 1,
                immediate: 2,
                countdown: 3,
            };

            const luckyBagInfo = {
                couponList,
                doorsill: doorsillMap[values.condition as keyof typeof doorsillMap] || 0,
                name: values?.remark,
                limitNum: values?.limitType === 'limited' ? values?.limitCount || 0 : 0,
                target: values?.inviteCount,
                type: 1,
                distributeRule: {
                    countDownNum: values?.countdownSeconds,
                    distributeType: distributeTypeMap[values?.startType as keyof typeof distributeTypeMap],
                },
                ...(mode === 'edit' ? { bagId } : {}),
            };

            const param = {
                liveId,
                luckyBagInfo,
            };

            console.log('🚀 ~ handleOk ~ values:', param);

            let rsp;
            if (mode === 'edit') {
                rsp = await editLuckyBag(param);
            } else {
                rsp = await addLuckyBag(param);
            }

            if (Number(rsp?.code) === 0) {
                message.success(`${mode === 'edit' ? '编辑' : '添加'}福袋成功`);
                form.resetFields();
                onSuccess?.();
                onCancel();
            } else {
                message.error(rsp?.msg || `${mode === 'edit' ? '编辑' : '添加'}福袋失败`);
            }
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    const handleCancel = () => {
        // 如果表单没有修改，直接返回
        if (!form.isFieldsTouched()) {
            form.resetFields();
            onCancel();
        } else {
            setOpenPopConfirm(true);
        }
    };

    const handleConfirmCancel = () => {
        form.resetFields();
        onCancel();
        setOpenPopConfirm(false);
    };

    return (
        <Drawer
            title={
                typeof MODAL_TITLES[mode] === 'function'
                    ? MODAL_TITLES[mode](status as 1 | 2 | 3 | undefined)
                    : MODAL_TITLES[mode]
            }
            open={open}
            width={1100}
            onClose={handleCancel}
            destroyOnClose
            footer={
                mode === 'view' ? null : (
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <Popconfirm
                            open={openPopConfirm}
                            title='确认取消'
                            description='取消则已填写的内容不保存，确定取消？'
                            onCancel={() => setOpenPopConfirm(false)}
                            onConfirm={handleConfirmCancel}
                            okText='确定'
                            cancelText='取消'
                        >
                            <Button onClick={handleCancel}>取消</Button>
                        </Popconfirm>
                        <Button type='primary' onClick={handleOk} style={{ marginLeft: 8 }}>
                            确定
                        </Button>
                    </div>
                )
            }
        >
            <Row gutter={12}>
                <Col span={16}>
                    {open &&
                        (mode === 'view' && initialValues ? (
                            <LuckBagDetail liveId={liveId} bagId={bagId} />
                        ) : (
                            <LuckBagForm form={form} mode={mode} couponData={initialValues} />
                        ))}
                </Col>
                {open && mode !== 'view' && (
                    <Col span={8}>
                        <PreviewImage images={LuckBagPreviewImages} />
                    </Col>
                )}
            </Row>
        </Drawer>
    );
};

export default LuckBagItem;
