/**
 * @Owners wyy
 * @Title 福袋列表
 */
import { type LuckyBag } from '@/api/interface/luckyBag';
import { addLuckyBag, getLiveLuckyBags, getLiveLuckyBagsCount, updateLuckyBag } from '@/api/modules/luckyBag';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Modal, Row, Table, Tabs, Tag, Tooltip, message, type TableColumnsType } from 'antd';
import { useEffect, useState } from 'react';

import LuckBagItem from './LuckBagItem';
import { TAG_COLOR_MAP } from './constant';
import './index.scss';
import { type LuckyBagTableProps } from './types';

type LuckBagPageItem = LuckyBag.Response.getLiveLuckyBags['dataList'];

const ACTIVITY_STATUS_OPTIONS = [
    { label: '全部', value: undefined, count: 0 },
    { label: '待开始', value: 1, count: 0 },
    { label: '进行中', value: 2, count: 0 },
    { label: '已结束', value: 3, count: 0 },
];

const LuckyBagTable: React.FC<LuckyBagTableProps> = ({ liveId }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedRecord, setSelectedRecord] = useState<LuckBagPageItem | null>(null);
    const [mode, setMode] = useState<'add' | 'edit' | 'view'>('add');
    const [loading, setLoading] = useState(false);
    const [luckyBagList, setLuckyBagList] = useState<LuckBagPageItem[]>([]);
    const [paginationState, setPaginationState] = useState<{
        current: number;
        pageSize: number;
        total: number;
        filterActivityStatus: number | undefined;
    }>({
        current: 1,
        pageSize: 10,
        total: 0,
        filterActivityStatus: undefined,
    });

    const [activityStatusOptions, setActivityStatusOptions] =
        useState<{ label: string; value: number | undefined; count: number }[]>(ACTIVITY_STATUS_OPTIONS);

    const handleAddLuckBag = (values: unknown) => {
        console.log('提交新增福袋', values);
        setIsModalOpen(false);
    };

    const handleStartNow = (record: LuckBagPageItem) => {
        Modal.confirm({
            title: '确定立即发放吗？',
            onOk: async () => {
                try {
                    const res = await updateLuckyBag({
                        bagId: record.bagId,
                        liveId,
                        action: 1,
                    });
                    if (Number(res?.code) === 0) {
                        message.success('福袋发放成功');
                        fetchLuckyBags();
                    } else {
                        message.error(res?.msg || '福袋发放失败');
                    }
                } catch (error) {
                    console.error('福袋发放失败:', error);
                }
            },
        });
    };

    const fetchActivityStatusCount = async () => {
        try {
            const resp = await getLiveLuckyBagsCount({ liveId });
            if (Number(resp?.code) === 0) {
                if (resp?.data) {
                    const { total = 0, luckyBagCountList = {} } = resp?.data as {
                        total: number;
                        luckyBagCountList: { [key: number]: { countNum: number } };
                    };
                    const newActivityStatusOptions = [
                        {
                            label: ACTIVITY_STATUS_OPTIONS[0].label,
                            value: ACTIVITY_STATUS_OPTIONS[0].value,
                            count: total,
                        },
                        {
                            label: ACTIVITY_STATUS_OPTIONS[1].label,
                            value: ACTIVITY_STATUS_OPTIONS[1].value,
                            count: luckyBagCountList?.[0]?.countNum || 0,
                        },
                        {
                            label: ACTIVITY_STATUS_OPTIONS[2].label,
                            value: ACTIVITY_STATUS_OPTIONS[2].value,
                            count: luckyBagCountList?.[1]?.countNum || 0,
                        },
                        {
                            label: ACTIVITY_STATUS_OPTIONS[3].label,
                            value: ACTIVITY_STATUS_OPTIONS[3].value,
                            count: luckyBagCountList?.[2]?.countNum || 0,
                        },
                    ];
                    setActivityStatusOptions(newActivityStatusOptions);
                }
            }
        } catch (error) {
            console.error('获取福袋列表失败:', error);
        }
    };

    const fetchLuckyBags = async () => {
        try {
            setLoading(true);
            const res = await getLiveLuckyBags({
                liveId,
                page: paginationState.current,
                rows: paginationState.pageSize,
                status: paginationState.filterActivityStatus,
            });
            if (res) {
                setLuckyBagList((res?.data?.dataList as unknown as LuckBagPageItem[]) || []);
                setPaginationState(prev => ({ ...prev, total: res.data?.total || 0 }));
            }
        } catch (error) {
            console.error('获取福袋列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchLuckyBags();
        fetchActivityStatusCount();
    }, [liveId, paginationState.current, paginationState.pageSize, paginationState.filterActivityStatus]);

    const handleViewDetail = (record: LuckBagPageItem) => {
        setSelectedRecord(record);
        setMode('view');
        setIsModalOpen(true);
    };

    const handleEdit = (record: LuckBagPageItem) => {
        setSelectedRecord(record);
        setMode('edit');
        setIsModalOpen(true);
    };

    const handleCopy = async (record: LuckBagPageItem) => {
        try {
            const params = {
                liveId,
                luckyBagInfo: {
                    name: record.name,
                    doorsill: record.doorsill,
                    type: record.type,
                    icon: record.icon,
                    // iconType: 0, // 图标类型 0 自定义
                    // inviteType: 1, // 邀请类型(1:全部用户都为有效邀请,2:有效邀请仅为本场直播间新用户)
                    limitNum: record.limitNum,
                    target: record.target,
                    distributeRule: {
                        countDownNum: record.countDownNum,
                        distributeType: 1,
                    },
                    couponList: [
                        {
                            couponId: Number(record.couponList?.[0]?.couponId),
                            sort: 0,
                            transferNum: Number(record.couponList?.[0]?.applyNum),
                        },
                    ],
                },
            };
            const res = await addLuckyBag(params);
            if (Number(res?.code) === 0) {
                message.success('复制创建成功');
                fetchLuckyBags();
                fetchActivityStatusCount();
            } else {
                message.error(res?.msg || '复制创建失败');
            }
        } catch (error) {
            console.error('复制创建失败:', error);
            message.error('复制创建失败');
        }
    };

    const handleDelete = (record: LuckBagPageItem) => {
        Modal.confirm({
            title: '确定删除当前活动？',
            content: '删除后，该活动内容不可找回，确认删除？',
            onOk: async () => {
                try {
                    const res = await updateLuckyBag({
                        bagId: record.bagId,
                        liveId,
                        action: 2,
                    });
                    if (Number(res?.code) === 0) {
                        message.success('福袋结束成功');
                        fetchLuckyBags();
                        fetchActivityStatusCount();
                    } else {
                        message.error(res?.msg || '福袋结束失败');
                    }
                } catch (error) {
                    console.error('福袋结束失败:', error);
                }
            },
        });
    };

    const handleEndNow = (record: LuckBagPageItem) => {
        Modal.confirm({
            title: '确定结束该活动吗？',
            onOk: async () => {
                try {
                    const res = await updateLuckyBag({
                        bagId: record.bagId,
                        liveId,
                        action: 0,
                    });
                    if (Number(res?.code) === 0) {
                        message.success('福袋结束成功');
                        fetchLuckyBags();
                        fetchActivityStatusCount();
                    } else {
                        message.error(res?.msg || '福袋结束失败');
                    }
                } catch (error) {
                    console.error('福袋结束失败:', error);
                }
            },
        });
    };

    const handleModalCancel = () => {
        setIsModalOpen(false);
        setSelectedRecord(null);
        setMode('add');
    };

    // 活动列表过滤条件
    const handleFilterChange = (key: string) => {
        setPaginationState(prev => ({
            ...prev,
            total: 0,
            current: 1,
            filterActivityStatus: Number(key),
        }));
    };

    const DOORSILL_MAP: { [key: number]: string } = {
        0: '不限',
        1: '未关注主播',
        2: '全部关注主播',
        3: '分享直播间可领',
        4: '邀请进入直播间',
    };

    const STATUS_MAP: { [key: number]: string } = {
        1: '待开始',
        2: '进行中',
        3: '已结束',
    };

    const columns: TableColumnsType<LuckBagPageItem> = [
        {
            title: 'ID',
            render: (_text: unknown, _record: LuckBagPageItem, index: number) =>
                index + 1 + (paginationState.current - 1) * paginationState.pageSize,
        },
        {
            title: '活动备注',
            dataIndex: 'name',
            width: 120,
        },
        {
            title: '活动门槛',
            dataIndex: 'doorsill',
            width: 120,
            render: (doorsill: number) => DOORSILL_MAP[doorsill] || '--',
        },
        {
            title: '活动时间',
            dataIndex: 'gmtCreate',
            width: 180,
        },
        {
            title: '状态',
            dataIndex: 'status',
            width: 80,
            render: (status: number) => (
                <Tag style={TAG_COLOR_MAP[status] || {}}>
                    {status === 2 && (
                        <img
                            src='https://applet.ifengqun.com/fq-mall/prod/discover/discover-loading.gif'
                            style={{
                                width: '12px',
                                height: '12px',
                                marginRight: '4px',
                            }}
                            alt=''
                        />
                    )}
                    {STATUS_MAP[status] || '--'}
                </Tag>
            ),
        },
        {
            title: '参与人数',
            dataIndex: 'receiveUserNum',
            width: 120,
            render: (receiveUserNum: number) => receiveUserNum || '--',
        },
        {
            title: '更新时间',
            dataIndex: 'gmtModified',
            width: 140,
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            width: 152,
            render: (_, record) => {
                const buttonStyle = { padding: '0 6px 0 0', height: 'auto', lineHeight: '20px', whiteSpace: 'nowrap' };
                switch (record.status) {
                    case 1:
                        return (
                            <div className='luck-bag-table-action'>
                                {record.countDownNum === 0 && (
                                    <Button type='link' size='small' onClick={() => handleStartNow(record)} style={buttonStyle}>
                                        立即发放
                                    </Button>
                                )}
                                <Button type='link' size='small' onClick={() => handleViewDetail(record)} style={buttonStyle}>
                                    详情
                                </Button>
                                <Tooltip title={record.source === '1' && '该活动由平台代创建，如需修改请联系平台'}>
                                    <Button
                                        type='link'
                                        size='small'
                                        disabled={record.source === '1'}
                                        onClick={() => handleEdit(record)}
                                        style={buttonStyle}
                                    >
                                        编辑
                                    </Button>
                                </Tooltip>
                                <Tooltip title={!record.source && '该活动由平台代创建，如需复制创建请联系平台'}>
                                    <Button
                                        type='link'
                                        size='small'
                                        disabled={record.source === '1'}
                                        onClick={() => handleCopy(record)}
                                        style={buttonStyle}
                                    >
                                        复制创建
                                    </Button>
                                </Tooltip>
                                <Button type='link' size='small' onClick={() => handleDelete(record)} style={buttonStyle}>
                                    删除
                                </Button>
                            </div>
                        );
                    case 2:
                        return (
                            <div className='luck-bag-table-action'>
                                <Button type='link' danger size='small' onClick={() => handleEndNow(record)} style={buttonStyle}>
                                    活动结束
                                </Button>
                                <Button type='link' size='small' onClick={() => handleViewDetail(record)} style={buttonStyle}>
                                    详情
                                </Button>
                                <Button type='link' size='small' onClick={() => handleCopy(record)} style={buttonStyle}>
                                    复制创建
                                </Button>
                            </div>
                        );
                    case 3:
                        return (
                            <div className='luck-bag-table-action'>
                                <Button type='link' size='small' onClick={() => handleViewDetail(record)} style={buttonStyle}>
                                    详情
                                </Button>
                                <Button type='link' size='small' onClick={() => handleCopy(record)} style={buttonStyle}>
                                    复制创建
                                </Button>
                                <Button type='link' size='small' onClick={() => handleDelete(record)} style={buttonStyle}>
                                    删除
                                </Button>
                            </div>
                        );
                    default:
                        return null;
                }
            },
        },
    ];

    return (
        <div className='luck-bag-table'>
            <Row className='luck-bag-table-header' style={{ position: 'relative' }}>
                <Col style={{ flex: 1 }}>
                    <Tabs onChange={handleFilterChange} className='table-tabs'>
                        {activityStatusOptions.map(option => (
                            <Tabs.TabPane key={option.value} tab={`${option.label}(${option?.count})`} />
                        ))}
                    </Tabs>
                </Col>
                <Col style={{ position: 'absolute', right: 0 }}>
                    <Button type='primary' icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
                        新增福袋
                    </Button>
                </Col>
            </Row>

            <Table
                columns={columns}
                loading={loading}
                dataSource={luckyBagList.map((item, index) => ({ ...item, key: index }))}
                rowKey='id'
                pagination={{
                    current: paginationState.current,
                    pageSize: paginationState.pageSize,
                    total: paginationState.total,
                    onChange: (newPage, newPageSize) => {
                        setPaginationState(prev => ({ ...prev, current: newPage, pageSize: newPageSize }));
                    },
                    showSizeChanger: true,
                    showTotal: total => `共 ${total} 条`,
                }}
                scroll={{ x: 'max-content' }}
                rowClassName={record => (record.status === 2 ? 'highlight-row' : '')}
            />

            <LuckBagItem
                open={isModalOpen}
                liveId={liveId}
                bagId={selectedRecord?.bagId ?? 0}
                mode={mode}
                status={selectedRecord?.status as 0 | 1 | 2 | undefined}
                initialValues={selectedRecord || undefined}
                onOk={handleAddLuckBag}
                onCancel={handleModalCancel}
                onSuccess={() => {
                    fetchLuckyBags();
                    fetchActivityStatusCount();
                }}
            />
        </div>
    );
};

export default LuckyBagTable;
