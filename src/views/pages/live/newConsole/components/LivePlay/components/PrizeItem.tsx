/**
 * @Owners felix.cai
 * @Title 奖项
 */

import { type RedPacket } from '@/api/interface/live/redpacket';
import { RightOutlined } from '@ant-design/icons';
import { Button, Card, Col, Divider, Form, InputNumber, Popconfirm, Row, message } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

import { DataContext } from '../RedPacketTable';
import { ActionType, ActivityStatus, PrizeType } from '../constant';
import '../index.scss';

import CouponCard from './CouponCard';
import RealPrizeCard from './RealPrizeCard';
import { SelectType } from './WinnerInfoModal';

interface PrizeItemProps {
    /** 活动详情 */
    activityDetail: RedPacket.Response.GetActiveRedPacketRainDetail | null;
    /** 奖品编号 */
    prizeNo: number;
    prize: RedPacket.PrizeConfig;
    /** 修改 */
    onChange(prizeId: number, updateValues: Partial<RedPacket.PrizeConfig>): void;
    /** 删除 */
    onDelete(prizeId: number): void;
    /** 中奖信息 */
    onShowWinnerInfo(prize: RedPacket.PrizeConfig, filterType: SelectType): void;
}

enum PrizeSource {
    RealPrize = 1,
    Coupon = 2,
}

const PrizeItem: React.FC<PrizeItemProps> = ({ prize, prizeNo, onChange, onDelete, activityDetail, onShowWinnerInfo }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [editPrize, setEditPrize] = useState(prize);
    const [form] = Form.useForm();
    const { actionType } = useContext(DataContext);
    const [stock, setStock] = useState<number | undefined>(undefined);

    const isView = actionType === ActionType.View;
    const isCreate = actionType === ActionType.Add;

    useEffect(() => {
        if (prize && !isEditing) {
            setEditPrize(prize);
            form.setFieldsValue({
                prizeName: prize.prizeName,
                prizeSpec: prize.prizeSpec,
                allNum: prize.allNum || 0,
            });
        }
    }, [prize, isEditing]);

    const handleFieldChange = (prizeId: number, updateValues: Partial<RedPacket.PrizeConfig>) => {
        setEditPrize(prev => ({ ...prev, ...updateValues }));
        form?.setFieldsValue(updateValues);
    };

    const handleSave = async () => {
        try {
            await form?.validateFields();
            const values = form?.getFieldsValue();
            const updateId = editPrize.prizeId as number;
            const updateValues = {
                prizeName: values.prizeName || '',
                prizeSpec: values.prizeSpec || '',
                allNum: values.allNum || 0,
            };
            form?.setFieldsValue(updateValues);
            onChange(updateId, updateValues);
            setIsEditing(false);
        } catch (error) {
            console.error('Validation failed:', error);
        }
    };

    const handleCancel = () => {
        form?.setFieldsValue({
            prizeName: prize.prizeName,
            prizeSpec: prize.prizeSpec,
            allNum: prize.allNum || 0,
        });
        setEditPrize(prize);
        setIsEditing(false);
    };

    const handleAllNumChange = (value: number) => {
        form?.setFieldsValue({
            allNum: value,
        });
        onChange(editPrize?.prizeId as number, { allNum: value });
    };

    const handleEdit = () => {
        if (editPrize?.prizeSource === PrizeSource.RealPrize) {
            message.warning('OMS申请的活动实物商品不支持修改');
            return;
        }
        setIsEditing(true);
    };

    const isCoupon = editPrize?.prizeType === PrizeType.Coupon;

    /** 当前库存 活动待开始时，库存为发放数量+当前库存，活动进行中时，库存为当前库存 */
    useEffect(() => {
        // 活动待开始
        const isPending = activityDetail?.activityStatus === ActivityStatus.Pending;
        if (isCreate) {
            setStock(prize?.usableNum || 0);
        }

        if (activityDetail && prize && !stock) {
            const { allNum = 0, usableNum = 0 } = prize;
            setStock(isPending ? usableNum + allNum : usableNum);
        }
    }, [activityDetail, prize, stock]);

    return (
        <Card
            style={{ backgroundColor: '#F7F8F9', marginBottom: 16, width: 440, border: 'none' }}
            styles={{ body: { padding: 12 } }}
        >
            <Form form={form} layout='horizontal'>
                <Row align='middle' justify='space-between' style={{ marginBottom: 12 }}>
                    <Col style={{ fontSize: 14, fontWeight: 600, color: '#333' }}>奖品{prizeNo + 1}</Col>
                    <Col style={{ display: isView ? 'block' : 'none' }}>
                        <p style={{ color: '#666' }}>
                            奖品发放数量：<span style={{ color: '#FF8F16' }}>{prize?.allNum}</span>
                        </p>
                    </Col>
                    {!isView && (
                        <Col>
                            {!isCoupon ? (
                                isEditing ? (
                                    <>
                                        <Button style={{ padding: 0 }} type='link' onClick={handleSave}>
                                            保存
                                        </Button>
                                        <Divider type='vertical' />
                                        <Button style={{ padding: 0 }} type='link' danger onClick={handleCancel}>
                                            取消
                                        </Button>
                                    </>
                                ) : (
                                    <Button
                                        type='link'
                                        style={{ padding: 0 }}
                                        onClick={handleEdit}
                                        disabled={editPrize?.prizeSource === PrizeSource.RealPrize}
                                    >
                                        编辑
                                    </Button>
                                )
                            ) : null}
                            {!isCoupon && !isEditing && <Divider type='vertical' />}
                            {!isEditing && (
                                <Popconfirm title='确定删除该奖品吗？' onConfirm={() => onDelete(editPrize?.prizeId as number)}>
                                    <Button type='link' danger style={{ padding: 0 }}>
                                        删除
                                    </Button>
                                </Popconfirm>
                            )}
                        </Col>
                    )}
                </Row>
                <Row>
                    {editPrize.prizeType === PrizeType.Coupon && editPrize?.couponBaseRes ? (
                        <CouponCard key={editPrize?.couponBaseRes?.id} coupon={editPrize?.couponBaseRes} />
                    ) : editPrize.prizeType === PrizeType.RealPrize && editPrize ? (
                        <RealPrizeCard prize={editPrize} isEditing={isEditing} onChange={handleFieldChange} />
                    ) : null}
                </Row>
                {isView &&
                    [ActivityStatus.InProgress, ActivityStatus.Ended].includes(activityDetail?.activityStatus as number) && (
                        <Row align='middle' className='prize-item-info'>
                            <Col span={isCoupon ? 6 : 4} className='prize-item-info-item'>
                                <span>{editPrize?.allNum}</span>
                                <p>奖品数量</p>
                            </Col>
                            <Col
                                span={6}
                                className='prize-item-info-item'
                                onClick={() => onShowWinnerInfo(editPrize, SelectType.WINNER)}
                            >
                                <span>{editPrize?.prizeNum || 0}</span>
                                <p>
                                    中奖用户
                                    <RightOutlined />
                                </p>
                            </Col>
                            <Col
                                span={isCoupon ? 6 : 4}
                                className='prize-item-info-item'
                                onClick={() => onShowWinnerInfo(editPrize, isCoupon ? SelectType.USED : SelectType.RECEIVED)}
                            >
                                <span>{(isCoupon ? editPrize?.usedNum : editPrize?.receivedPrizeNum) || 0}</span>
                                <p>
                                    {isCoupon ? '已使用' : '已领取'}
                                    <RightOutlined />
                                </p>
                            </Col>
                            <Col
                                span={isCoupon ? 6 : 4}
                                className='prize-item-info-item'
                                onClick={() => onShowWinnerInfo(editPrize, isCoupon ? SelectType.UNUSED : SelectType.WAITING)}
                            >
                                <span>{(isCoupon ? editPrize?.notUsedNum : editPrize?.beReceivedPrizeNum) || 0}</span>
                                <p>
                                    {isCoupon ? '未使用' : '待领取'}
                                    <RightOutlined />
                                </p>
                            </Col>
                            {editPrize.prizeType !== PrizeType.Coupon && (
                                <Col
                                    span={isCoupon ? 6 : 4}
                                    className='prize-item-info-item'
                                    onClick={() => onShowWinnerInfo(editPrize, SelectType.EXPIRED)}
                                >
                                    <span>{editPrize?.expiredPrizeNum || 0}</span>
                                    <p>
                                        已过期
                                        <RightOutlined />
                                    </p>
                                </Col>
                            )}
                        </Row>
                    )}
                {!isView && (
                    <Row align='middle' justify='space-between' style={{ marginTop: 12 }}>
                        <Col>
                            {(editPrize?.prizeSource === PrizeSource.RealPrize || isCoupon) && (
                                <span style={{ color: '#FF8F16' }}>当前库存：{stock}</span>
                            )}
                        </Col>
                        <Col>
                            <Form.Item
                                label='奖品发放数量'
                                colon={false}
                                style={{ marginBottom: 0 }}
                                name='allNum'
                                rules={[
                                    { required: true, message: '请输入奖品发放数量' },
                                    {
                                        validator: (_rule, value, callback) => {
                                            if (
                                                (editPrize?.prizeSource === PrizeSource.RealPrize || isCoupon) &&
                                                value > (stock || 0)
                                            ) {
                                                callback('发放数量不能大于当前库存');
                                            }
                                            callback();
                                        },
                                    },
                                ]}
                            >
                                <InputNumber
                                    suffix={isCoupon ? '张' : '份'}
                                    // max={stock || 0}
                                    // value={editPrize?.allNum}
                                    style={{ width: 160 }}
                                    onChange={value => handleAllNumChange(value as number)}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                )}
            </Form>
        </Card>
    );
};

export default PrizeItem;
