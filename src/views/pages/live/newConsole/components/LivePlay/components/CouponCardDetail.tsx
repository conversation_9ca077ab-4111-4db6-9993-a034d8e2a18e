/**
 * @Owners wyy
 * @Title 优惠券卡片
 */
import { type CouponCardDetailProps } from '../types';
const CouponCard: React.FC<CouponCardDetailProps> = ({ key, coupon }) => (
    <div key={key} style={{ position: 'relative' }}>
        <div
            style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '4px',
            }}
        >
            <span style={{ fontSize: '14px', color: '#333' }}>奖品1</span>
        </div>
        <div
            style={{
                position: 'relative',
                background: '#FFF6F2',
                borderRadius: '12px',
                border: '1px solid #FFE7E7',
                overflow: 'hidden',
                display: 'flex',
            }}
        >
            <div
                style={{
                    width: '120px',
                    background: '#FFF6F2',
                    padding: '20px 16px',
                    borderRight: '1px dashed #FFE7E7',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    position: 'relative',
                }}
            >
                <div
                    style={{
                        position: 'absolute',
                        top: 8,
                        left: 8,
                        background: '#FF4D4F',
                        color: '#fff',
                        padding: '2px 8px',
                        borderRadius: '4px',
                        fontSize: '12px',
                    }}
                >
                    会员专享
                </div>
                <div
                    style={{
                        color: '#FF4D4F',
                        marginTop: '20px',
                        textAlign: 'center',
                    }}
                >
                    <span style={{ fontSize: '14px' }}>¥</span>
                    <span style={{ fontSize: '32px', fontWeight: 'bold' }}>20</span>
                </div>
                <div style={{ fontSize: '12px', color: '#FA2B19' }}>满3件可用</div>
            </div>
            <div style={{ flex: 1, padding: '16px', position: 'relative' }}>
                <div style={{ marginBottom: '12px' }}>
                    <div
                        style={{
                            fontSize: '16px',
                            color: '#333',
                            marginBottom: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                        }}
                    >
                        <img
                            src={coupon?.icon}
                            alt=''
                            style={{
                                width: '40px',
                                height: '40px',
                                borderRadius: '4px',
                                objectFit: 'cover',
                            }}
                        />
                        <div>
                            <div>{coupon?.couponList?.[0]?.name || '--'}</div>
                        </div>
                    </div>
                </div>
                <div style={{ fontSize: '12px', color: '#999' }}>
                    有效期至 {coupon?.couponList?.[0]?.expiryEnd?.toLocaleString()}
                </div>
            </div>
        </div>
    </div>
);

export default CouponCard;
