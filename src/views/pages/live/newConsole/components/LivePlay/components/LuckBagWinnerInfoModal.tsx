/**
 * @Owners felix.cai
 * @Title 奖励信息
 */
import { type RedPacket } from '@/api/interface/live/redpacket';
import { getLuckBagReceiveDetail } from '@/api/modules/live/console';
import { Avatar, Input, Modal, Row, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import debounce from 'lodash/debounce';
import React, { useEffect, useState } from 'react';

import '../index.scss';

interface WinnerInfoModalProps {
    /** 是否打开 */
    open: boolean;
    /** 活动ID */
    activityId: number;
    /** 查询列表类型 */
    filterType: SelectType;
    /** 奖品名称 */
    prizeName: string;
    /** 奖品ID */
    bagId: number;
    onClose(): void;
}

type WinnerRecord = RedPacket.Response.GetActiveLuckBagReceiveDetail['dataList'][0];

/** 状态 2-已使用 3-未使用 4-已领取 5-待领取 6-已过期 */
export const DISTRIBUTION_STATUS_MAP: Record<number, string> = {
    2: '已使用',
    3: '未使用',
    4: '已领取',
    5: '待领取',
    6: '已过期',
};

/** 状态 2-已使用 3-未使用 4-已领取 5-待领取 6-已过期 */
export const DISTRIBUTION_STATUS_COLOR_MAP: Record<number, string> = {
    2: '#00B68F',
    3: '#333',
    4: '#00B68F',
    5: '#333',
    6: '#f5222d',
};

/** 查询类型 1:全部 2:已使用 3:未使用 */
export enum SelectType {
    /** 中奖用户 */
    ALL = 1,
    /** 已使用 */
    USED = 2,
    /** 未使用 */
    UNUSED = 3,
}

const LuckBagWinnerInfoModal: React.FC<WinnerInfoModalProps> = ({ open, onClose, bagId, filterType, prizeName, activityId }) => {
    const [searchKeyword, setSearchKeyword] = useState('');

    const [loading, setLoading] = useState(false);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(20);
    const [total, setTotal] = useState(0);

    const [winnerList, setWinnerList] = useState<WinnerRecord[]>([]);

    const fetchWinnerList = async () => {
        if (!activityId) return;

        setLoading(true);

        const res = await getLuckBagReceiveDetail({
            bagId,
            liveId: activityId,
            page,
            receiverUserMsg: searchKeyword,
            rows: pageSize,
            type: filterType,
        });

        setWinnerList(res.data?.dataList || []);
        setTotal(res.data?.total || 0);
        setLoading(false);
    };

    useEffect(() => {
        if (activityId && open) {
            fetchWinnerList();
        }
    }, [activityId, page, pageSize, open, searchKeyword]);

    const columns: ColumnsType<WinnerRecord> = [
        {
            title: '序号',
            render: (_text: unknown, _record: WinnerRecord, index: number) => index + 1 + (page - 1) * pageSize,
        },
        {
            title: '用户昵称',
            render: (_text: string, record: WinnerRecord) => (
                <Row align='middle'>
                    <Avatar src={record?.avatar} size={48} />
                    <span style={{ marginLeft: 12 }}>{record?.nickName}</span>
                </Row>
            ),
        },
        {
            title: '用户手机号',
            dataIndex: 'phone',
            key: 'phone',
        },
        {
            title: '奖品状态',
            key: 'status',
            render: (_: number, record: WinnerRecord) => {
                const isUsed = (record?.usedNum || 0) > 0;

                return (
                    <span style={{ color: isUsed ? '#00B68F' : '#333' }}>
                        {isUsed ? '已使用' : '未使用'} ({record?.usedNum || 0}/{record?.notUsedNum || 0})
                    </span>
                );
            },
        },
    ];

    // 添加防抖函数
    const debouncedSetPrizeName = debounce((value: string) => {
        setPage(1);
        setSearchKeyword(value);
    }, 500);

    return (
        <Modal
            title={
                <div className='title' style={{ fontWeight: 600, fontSize: 20, marginBottom: 24 }}>
                    {prizeName}中奖名单
                </div>
            }
            open={open}
            onCancel={onClose}
            footer={null}
            width={1000}
        >
            <Row className='winner-info-header' justify='start'>
                <Input
                    style={{ width: '40%' }}
                    placeholder='请输入用户昵称或手机号进行搜索'
                    onInput={e => debouncedSetPrizeName((e.target as HTMLInputElement).value)}
                />
            </Row>
            <Table
                columns={columns}
                dataSource={winnerList}
                loading={loading}
                pagination={{
                    current: page,
                    pageSize,
                    total,
                    onChange: (newPage, newPageSize) => {
                        setPage(newPage);
                        if (newPageSize !== pageSize) {
                            setPageSize(newPageSize);
                        }
                    },
                    showSizeChanger: true,
                    showTotal: total => `共 ${total} 条`,
                }}
            />
        </Modal>
    );
};

export default LuckBagWinnerInfoModal;
