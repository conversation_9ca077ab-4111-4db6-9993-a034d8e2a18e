/**
 * @Owners wyy
 * @Title 优惠券弹窗列表
 */
import { type Coupon } from '@/api/interface/coupon';
import { supplierLiveCouponPage } from '@/api/modules/coupon';
import { Button, Input, Modal, Space, Table, message, type TableColumnsType } from 'antd';
import { useEffect, useState } from 'react';

import { useCouponInfo } from '../../../hooks/useCouponInfo';

interface SelectCouponModalProps {
    open: boolean;
    /** 是否可以多选 */
    multiple?: boolean;
    selectedIds?: number[];
    mockData?: SupplierLiveCouponPageItem[];
    onOk(selectedCoupons: SupplierLiveCouponPageItem[]): void;
    onCancel(): void;
}

const COUPON_TYPE_MAP: Record<number, string> = {
    1: '满减券',
    2: '折扣券',
    3: '无门槛',
} as const;

const CouponDescCom = ({ couponInfo }: { couponInfo: Coupon.Response.supplierLiveCouponPage['dataList'][number] }) => {
    const info = useCouponInfo(couponInfo);
    return info?.couponDesc || '-';
};

type SupplierLiveCouponPageItem = Coupon.Response.supplierLiveCouponPage['dataList'][number];

const SelectCouponModal: React.FC<SelectCouponModalProps> = ({ open, onOk, onCancel, selectedIds, multiple = false }) => {
    const [searchValue, setSearchValue] = useState('');
    const [tempSelected, setTempSelected] = useState<SupplierLiveCouponPageItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState<SupplierLiveCouponPageItem[]>([]);

    // 添加分页相关状态
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    const fetchCouponList = async (page = 1, pageSize = 10) => {
        try {
            setLoading(true);
            const params = {
                page,
                rows: pageSize,
                status: 2,
                name: searchValue,
            };
            const res = await supplierLiveCouponPage(params);
            setDataSource(res?.data?.dataList || []);
            setPagination({
                ...pagination,
                current: page,
                total: res?.data?.total || 0,
            });
        } catch (error) {
            console.error('获取优惠券列表失败：', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (open) {
            fetchCouponList();
        }
    }, [open, searchValue]);

    const handleSearch = (value: string) => {
        setSearchValue(value);
    };

    const handleReset = () => {
        setSearchValue('');
    };

    // const filteredData = dataSource.filter(item => item.name.toLowerCase().includes(searchValue.toLowerCase()));

    const couponColumns: TableColumnsType<SupplierLiveCouponPageItem> = [
        {
            title: '优惠券',
            dataIndex: 'id',
            width: 80,
        },
        {
            title: '优惠券名称',
            dataIndex: 'name',
            width: 120,
            ellipsis: true,
        },
        {
            title: '优惠券类型',
            dataIndex: 'discountType',
            width: 100,
            render: (type: number) => COUPON_TYPE_MAP[type] || '-',
        },
        {
            title: '优惠规则',
            dataIndex: 'rule',
            width: 120,
            ellipsis: true,
            render: (_discountRuleModels, record) => <CouponDescCom couponInfo={record} />,
        },
        {
            title: '使用有效期',
            dataIndex: 'validityPeriod',
            width: 160,
            render: (_discountRuleModels, record) => {
                const { expiryStart, expiryEnd, sameDay, nextDay } = record.timeModel;
                if (nextDay) {
                    return `领取次日起,${nextDay}天内可用`;
                }
                if (sameDay) {
                    return `领取当日起,${sameDay}天内可用`;
                }
                return `${expiryStart}~${expiryEnd}`;
            },
        },
        // {
        //     title: '使用规则',
        //     dataIndex: 'desc',
        //     width: 120,
        //     ellipsis: true,
        // },
        {
            title: '券库存',
            dataIndex: 'usableNum',
            width: 80,
        },
    ];

    const handleConfirm = () => {
        if (!tempSelected.length) {
            message.warning('请选择优惠券');
            return;
        }

        if (tempSelected[0]?.usableNum !== undefined && tempSelected[0].usableNum <= 0) {
            message.warning('该优惠券库存为0，请重新选择');
            return;
        }

        const formatCouponList = tempSelected.map(item => ({
            ...item,
            spuInfoList: item?.goodsInfoList || [],
        }));

        onOk(formatCouponList);
    };

    return (
        <Modal
            title='选择优惠券'
            open={open}
            onOk={handleConfirm}
            onCancel={onCancel}
            width={1000}
            destroyOnClose
            footer={[
                <Button key='submit' type='primary' onClick={handleConfirm}>
                    确认选择
                </Button>,
            ]}
        >
            <Space style={{ marginBottom: 16 }}>
                <Input.Search
                    placeholder='请输入优惠券名称'
                    allowClear
                    onSearch={handleSearch}
                    onChange={e => handleSearch(e.target.value)}
                    style={{ width: 372 }}
                    value={searchValue}
                />
                <Button onClick={handleReset}>重置</Button>
            </Space>
            <Table
                loading={loading}
                columns={couponColumns}
                dataSource={dataSource}
                scroll={{ y: 400 }}
                rowKey='id'
                rowSelection={{
                    type: multiple ? 'checkbox' : 'radio',
                    onChange: (_, selectedRows) => {
                        setTempSelected(selectedRows);
                    },
                    getCheckboxProps: (record: SupplierLiveCouponPageItem) => ({
                        disabled: selectedIds?.includes(record?.id as number),
                    }),
                }}
                pagination={{
                    ...pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: total => `共 ${total} 条`,
                    onChange: (page, pageSize) => {
                        fetchCouponList(page, pageSize);
                    },
                }}
            />
        </Modal>
    );
};

export default SelectCouponModal;
