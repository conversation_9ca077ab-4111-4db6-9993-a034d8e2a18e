/**
 * @Owners wuze
 * @Title 奖品选择弹窗
 */

import { type Goods } from '@/api/interface/goods';
import { type RedPacket } from '@/api/interface/live/redpacket';
import { getSupplierGoodsPage } from '@/api/modules/goods';
import { getActiveRedPacketRainRealPrizeList } from '@/api/modules/live/console';
import { Button, Col, Image, Input, Modal, Row, Table, Tabs, message } from 'antd';
import debounce from 'lodash/debounce';
import { useEffect, useState } from 'react';

type RealPrizeDataItem = RedPacket.Response.ActiveRedPacketRealPrizeListRespVO;

type OfficialPrizeDataItem = Goods.Response.getSupplierGoodsPage['dataList'][number];

type VirtualPrizeProps = {
    /** 是否显示 */
    visible: boolean;
    /** 选中的奖品ID */
    selectedPrizeIds?: number[];
    /** 取消 */
    onCancel(): void;
    /** 确定 */
    onOk(realPrize: RealPrizeDataItem[], officialPrize: OfficialPrizeDataItem[]): void;
};

export enum PrizeType {
    /** 实物奖品 */
    RealPrize = '0',
    /** 正式库商品 */
    OfficialPrize = '1',
}

// 实物商品表格列配置
const realPrizeColumns = [
    {
        title: '序号',
        dataIndex: 'realPrizeId',
        key: 'realPrizeId',
    },
    {
        title: '奖品名称',
        key: 'prizeName',
        render: (_text: string, record: RealPrizeDataItem) => (
            // 显示商品图片+标题
            <Row>
                <Col span={6}>
                    <Image src={record?.prizeImage} width={50} height={50} />
                </Col>
                <Col span={18}>{record?.prizeName}</Col>
            </Row>
        ),
    },
    {
        title: '商品规格',
        dataIndex: 'prizeSpec',
        key: 'prizeSpec',
        ellipsis: true,
    },
    {
        title: '剩余库存',
        dataIndex: 'num',
        key: 'num',
    },
];

// 正式库商品表格列配置
const officialPrizeColumns = [
    {
        title: '序号',
        dataIndex: 'spuId',
        key: 'spuId',
    },
    {
        title: '奖品名称',
        key: 'spuName',
        render: (_text: string, record: OfficialPrizeDataItem) => (
            // 显示商品图片+标题
            <Row>
                <Col span={6}>
                    <Image src={record?.url} width={50} height={50} />
                </Col>
                <Col span={18}>{record?.spuName}</Col>
            </Row>
        ),
    },
    {
        title: '商品规格',
        dataIndex: 'minSkuDesc',
        key: 'minSkuDesc',
        ellipsis: true,
    },
    // {
    //     title: '剩余库存',
    //     dataIndex: 'num',
    //     key: 'num',
    // },
];

const SelectVirtualPrizeModal = ({ visible, onCancel, onOk, selectedPrizeIds }: VirtualPrizeProps) => {
    // 选中的行数据
    const [realPrizeSelectedRowState, setRealPrizeSelectedRowState] = useState<{
        selectedRowKeys: number[];
        selectedRow: RealPrizeDataItem[];
    }>({
        selectedRowKeys: [],
        selectedRow: [],
    });
    const [officialPrizeSelectedRowState, setOfficialPrizeSelectedRowState] = useState<{
        selectedRowKeys: number[];
        selectedRow: OfficialPrizeDataItem[];
    }>({
        selectedRowKeys: [],
        selectedRow: [],
    });
    // 分页参数
    const [realPrizePagination, setRealPrizePagination] = useState<{
        current: number;
        pageSize: number;
        total: number;
    }>({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [officialPrizePagination, setOfficialPrizePagination] = useState<{
        current: number;
        pageSize: number;
        total: number;
    }>({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    const [activeTab, setActiveTab] = useState<PrizeType>(PrizeType.RealPrize);

    const [loading, setLoading] = useState(false);
    /** 实物商品列表 */
    const [realPrizeList, setRealPrizeList] = useState<RealPrizeDataItem[]>([]);
    /** 正式库商品列表 */
    const [officialPrizeList, setOfficialPrizeList] = useState<OfficialPrizeDataItem[]>([]);
    const [prizeName, setPrizeName] = useState<string | undefined>(undefined);

    const fetchRealPrizeList = async (page: number) => {
        setLoading(true);
        const rsp = await getActiveRedPacketRainRealPrizeList({
            condition: {
                prizeName,
                status: 1,
            },
            page,
            rows: realPrizePagination.pageSize ?? 0,
        });
        setLoading(false);
        setRealPrizeList(rsp?.data?.dataList || []);
        setRealPrizePagination({
            current: page,
            pageSize: realPrizePagination.pageSize ?? 0,
            total: rsp?.data?.total || 0,
        });
    };

    const fetchOfficialPrizeList = async (page: number) => {
        setLoading(true);
        const rsp = await getSupplierGoodsPage({
            searchText: prizeName,
            page,
            rows: officialPrizePagination.pageSize ?? 0,
        });
        setOfficialPrizeList(rsp?.data?.dataList || []);
        setOfficialPrizePagination({
            current: page,
            pageSize: officialPrizePagination.pageSize ?? 0,
            total: rsp?.data?.total || 0,
        });
        setLoading(false);
    };

    useEffect(() => {
        if (visible) {
            fetchRealPrizeList(1);
            setRealPrizePagination({
                current: 1,
                pageSize: realPrizePagination.pageSize ?? 0,
                total: 0,
            });
        }
    }, [visible]);

    useEffect(() => {
        if (selectedPrizeIds && selectedPrizeIds?.length > 0) {
            setRealPrizeSelectedRowState(pre => ({
                ...pre,
                selectedRowKeys: selectedPrizeIds,
            }));
            setOfficialPrizeSelectedRowState(pre => ({
                ...pre,
                selectedRowKeys: selectedPrizeIds,
            }));
        }
    }, [selectedPrizeIds]);

    // 行选择配置
    const rowSelection = {
        type: 'checkbox' as const,
        selectedRowKeys: realPrizeSelectedRowState.selectedRowKeys,
        onChange: (selectedKeys: React.Key[], selectedRows: RealPrizeDataItem[]) => {
            setRealPrizeSelectedRowState({
                selectedRowKeys: selectedKeys as number[],
                selectedRow: selectedRows,
            });
        },
        getCheckboxProps: (record: RealPrizeDataItem) => ({
            disabled: selectedPrizeIds?.includes(record.realPrizeId as number),
        }),
    };

    const officialRowSelection = {
        type: 'checkbox' as const,
        selectedRowKeys: officialPrizeSelectedRowState.selectedRowKeys,
        onChange: (selectedKeys: React.Key[], selectedRows: OfficialPrizeDataItem[]) => {
            setOfficialPrizeSelectedRowState({
                selectedRowKeys: selectedKeys as number[],
                selectedRow: selectedRows,
            });
        },
        getCheckboxProps: (record: OfficialPrizeDataItem) => ({
            disabled: selectedPrizeIds?.includes(record.spuId as number),
        }),
    };

    // 处理分页变化
    const handleTableChange = (page: number, size: number) => {
        if (activeTab === PrizeType.RealPrize) {
            // 这里应该调用接口重新获取数据
            fetchRealPrizeList(page);
            setRealPrizePagination({
                current: page,
                pageSize: size,
                total: realPrizePagination.total,
            });
        } else {
            fetchOfficialPrizeList(page);
            setOfficialPrizePagination({
                current: page,
                pageSize: size,
                total: officialPrizePagination.total,
            });
        }
    };
    // 处理确认按钮点击
    const handleConfirm = () => {
        if (activeTab === PrizeType.RealPrize && realPrizeSelectedRowState.selectedRow.length === 0) {
            message.error('请选择一个实物奖品');
            return;
        }
        if (activeTab === PrizeType.OfficialPrize && officialPrizeSelectedRowState.selectedRow.length === 0) {
            message.error('请选择一个正式库商品');
            return;
        }

        // 检查库存
        const realPrizeStock = realPrizeSelectedRowState.selectedRow.find(item => (item?.num || 0) <= 0);

        if (realPrizeStock) {
            message.error(`实物奖品[${realPrizeStock?.prizeName}]库存不足`);
            return;
        }

        onOk(realPrizeSelectedRowState.selectedRow, officialPrizeSelectedRowState.selectedRow);
    };

    useEffect(() => {
        if (activeTab === PrizeType.RealPrize) {
            fetchRealPrizeList(1);
            setRealPrizePagination({
                current: 1,
                pageSize: realPrizePagination.pageSize ?? 0,
                total: 0,
            });
        } else {
            fetchOfficialPrizeList(1);
            setOfficialPrizePagination({
                current: 1,
                pageSize: officialPrizePagination.pageSize ?? 0,
                total: 0,
            });
        }
    }, [prizeName]);

    // 添加防抖函数
    const debouncedSetPrizeName = debounce((value: string) => {
        setPrizeName(value);
    }, 500);

    const handleTabChange = (key: string) => {
        setActiveTab(key as PrizeType);
        if (key === PrizeType.RealPrize) {
            fetchRealPrizeList(1);
        } else {
            fetchOfficialPrizeList(1);
        }
    };

    const tabBarExtraContent = (
        // 搜索框
        <Input
            style={{ width: 300 }}
            // value={prizeName}
            placeholder='请输入商品名称'
            allowClear
            onChange={e => debouncedSetPrizeName(e.target.value)}
        />
    );

    return (
        <Modal title='选择实物奖品' open={visible} onCancel={onCancel} footer={null} maskClosable={false} width={1200}>
            <Tabs defaultActiveKey={PrizeType.RealPrize} tabBarExtraContent={tabBarExtraContent} onChange={handleTabChange}>
                <Tabs.TabPane tab='实物奖品' key={PrizeType.RealPrize}>
                    <Table
                        columns={realPrizeColumns}
                        dataSource={realPrizeList}
                        rowKey='realPrizeId'
                        rowSelection={rowSelection}
                        loading={loading}
                        scroll={{ y: 400 }}
                        pagination={{
                            current: realPrizePagination.current,
                            pageSize: realPrizePagination.pageSize ?? 0,
                            total: realPrizePagination.total,
                            showTotal: total => `共 ${total} 条`,
                            onChange: handleTableChange,
                            onShowSizeChange: handleTableChange,
                        }}
                    />
                </Tabs.TabPane>
                <Tabs.TabPane tab='正式库商品' key={PrizeType.OfficialPrize}>
                    <Table
                        columns={officialPrizeColumns}
                        dataSource={officialPrizeList}
                        rowKey='spuId'
                        rowSelection={officialRowSelection}
                        loading={loading}
                        scroll={{ y: 400 }}
                        pagination={{
                            current: officialPrizePagination.current,
                            pageSize: officialPrizePagination.pageSize ?? 0,
                            total: officialPrizePagination.total,
                            showTotal: total => `共 ${total} 条`,
                            onChange: handleTableChange,
                            onShowSizeChange: handleTableChange,
                        }}
                    />
                </Tabs.TabPane>
            </Tabs>
            <Row justify='space-between' align='middle'>
                <Col className='red-packet-edit-modal-prize-count'>
                    {realPrizeSelectedRowState.selectedRow.length > 0 ||
                        (officialPrizeSelectedRowState.selectedRow.length > 0 && (
                            <>
                                已选
                                <strong>
                                    {realPrizeSelectedRowState.selectedRow.length +
                                        officialPrizeSelectedRowState.selectedRow.length}
                                </strong>
                                个奖品
                            </>
                        ))}
                </Col>
                <Col>
                    <Button type='primary' onClick={handleConfirm}>
                        确定
                    </Button>
                </Col>
            </Row>
        </Modal>
    );
};

export default SelectVirtualPrizeModal;
