/**
 * @Owners wyy
 * @Title 预览示意图
 */
import { Button } from 'antd';
import { type FC, useState } from 'react';

interface PreviewImageProps {
    images: {
        src: string;
        title: string;
    }[];
}

const PreviewImage: FC<PreviewImageProps> = ({ images }) => {
    const [currentImageIndex, setCurrentImageIndex] = useState(0);

    const handlePrevImage = () => {
        setCurrentImageIndex(prev => (prev > 0 ? prev - 1 : images.length - 1));
    };

    const handleNextImage = () => {
        setCurrentImageIndex(prev => (prev < images.length - 1 ? prev + 1 : 0));
    };

    return (
        <div style={{ width: '335px' }}>
            <h3 style={{ margin: '0 0 16px', fontWeight: 600, fontSize: 16 }}>用户预览实例</h3>
            <div
                style={{
                    background: '#f5f5f5',
                    width: 'fit-content',
                    borderRadius: '6px',
                    padding: '0',
                }}
            >
                <img style={{ width: '320px' }} src={images[currentImageIndex].src} alt='预览图' />
            </div>
            <div
                style={{
                    display: 'flex',
                    width: '320px',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginTop: '10px',
                }}
            >
                <Button
                    type='text'
                    icon={
                        <img
                            src='https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/activity-arrow-left.png'
                            style={{ width: 20, height: 20 }}
                        />
                    }
                    onClick={handlePrevImage}
                    style={{ color: '#999' }}
                />
                <span style={{ color: '#999' }}>{images[currentImageIndex].title}</span>
                <Button
                    type='text'
                    icon={
                        <img
                            src='https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/activity-arrow-right.png'
                            style={{ width: 20, height: 20 }}
                        />
                    }
                    onClick={handleNextImage}
                    style={{ color: '#999' }}
                />
            </div>
        </div>
    );
};

export default PreviewImage;
