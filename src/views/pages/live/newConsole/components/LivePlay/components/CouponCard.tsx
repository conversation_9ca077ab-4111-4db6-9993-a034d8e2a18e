/**
 * @Owners wyy
 * @Title 优惠券卡片
 */
import { useCouponInfo } from '../../../hooks/useCouponInfo';
import { type CouponCardProps } from '../types';

const CouponCard: React.FC<CouponCardProps> = ({ coupon }) => {
    /** 优惠券信息 */
    const {
        /** 满减券 */
        // isDecreaseActivity,
        /** 折扣券 */
        isDiscountActivity,
        /** 无门槛券 */
        // isCashActivity,
        /** 面额 */
        denomination,
        /** 优惠券描述 */
        couponDesc,
        /** 优惠券商品可用范围 */
        goodsScopeInfo,
        /** 优惠券可用类型 */
        couponTypeText,
    } = useCouponInfo(coupon);

    return (
        <div key={coupon.id} className='coupon'>
            <div className='coupon-content'>
                <div className='coupon-content-nav'>
                    {coupon?.couponTab && <div className='coupon-content-active'>{coupon?.couponTab}</div>}
                    <div className='coupon-content-price'>
                        {!isDiscountActivity && <span className='coupon-content-price-symbol'>¥</span>}
                        <span className='coupon-content-price-num'>{denomination}</span>
                        {isDiscountActivity && <span className='coupon-content-price-discount'>折</span>}
                    </div>
                    <div className='coupon-content-desc'>{couponDesc || ''}</div>
                </div>
                <div className='coupon-content-box'>
                    <div className='coupon-content-box-container'>
                        <div className='coupon-content-box-content'>
                            <div>
                                {/* <div>{coupon.name}</div> */}
                                <div className='coupon-content-box-content-desc'>{couponDesc}</div>
                                <div className='coupon-content-box-content-scope-container'>
                                    {goodsScopeInfo?.spuPic && (
                                        <img className='coupon-content-box-pic' src={goodsScopeInfo?.spuPic} alt='' />
                                    )}
                                    {couponTypeText && (
                                        <div className='coupon-content-box-content-scope'>{couponTypeText || ''}</div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className='coupon-content-box-footer' style={{}}>
                        有效期至 {coupon?.expiryEnd}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CouponCard;
