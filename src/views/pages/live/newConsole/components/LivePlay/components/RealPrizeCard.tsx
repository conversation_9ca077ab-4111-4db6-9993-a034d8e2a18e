/**
 * @Owners felix.cai
 * @Title 实物奖品卡片
 */

import { type RedPacket } from '@/api/interface/live/redpacket';
import { getSkuDescBySpuId } from '@/api/modules/live/console';
import { Col, Form, Image, Input, Radio, Row, Select, type FormInstance, type RadioChangeEvent } from 'antd';
import { Space } from 'antd/lib';
import { forwardRef, useEffect, useState } from 'react';

import { PrizeType } from '../constant';

interface RealPrizeCardProps {
    prize: RedPacket.PrizeConfig;
    /** 是否编辑 */
    isEditing: boolean;
    /** 修改 */
    onChange(prizeId: number, updateValues: Partial<RedPacket.PrizeConfig>): void;
}

export enum PrizeNameSource {
    /** OMS商品规格 */
    OMS = 1,
    /** 自定义 */
    Custom = 2,
}

// 商品名称来源： OMS 或 自定义
const prizeNameSourceOptions = [
    { label: 'OMS商品规格', value: PrizeNameSource.OMS },
    { label: '自定义', value: PrizeNameSource.Custom },
];

const RealPrizeCard = forwardRef<FormInstance, RealPrizeCardProps>(({ prize, onChange, isEditing }) => {
    const { prizeName, prizeSpec, prizeImage } = prize;
    const [specSource, setSpecSource] = useState(PrizeNameSource.OMS);
    const [specs, setSpecs] = useState<RedPacket.Response.GetSkuDescBySpuId['data']>([]);

    // useEffect(() => {
    //     if (specSource === PrizeNameSource.Custom) {
    //         onChange(prize.prizeId as number, { prizeSpec: prize?.prizeSpec as string });
    //     }
    //     if (specSource === PrizeNameSource.OMS) {
    //         onChange(prize.prizeId as number, { prizeSpec: specs[0]?.skuDesc as string });
    //     }
    // }, [specSource]);

    useEffect(() => {
        if (isEditing && specs.length > 0) {
            // 判断当前specs是否跟prizeSpec一致
            if (specs.some(sku => sku.skuDesc === prizeSpec)) {
                setSpecSource(PrizeNameSource.OMS);
            } else {
                setSpecSource(PrizeNameSource.Custom);
            }
        }
    }, [isEditing, specs]);

    useEffect(() => {
        if (prize && prize?.prizeType === PrizeType.RealPrize) {
            fetchSkuDescBySpuId();
        }
    }, [prize]);

    /** 获取规格列表 */
    const fetchSkuDescBySpuId = async () => {
        const res = await getSkuDescBySpuId({ spuId: Number(prize?.prizeKey) });
        if (Array.isArray(res?.data)) {
            setSpecs(res.data);
        } else {
            setSpecs([]);
        }
    };

    const handleSpecSourceChange = (e: RadioChangeEvent) => {
        setSpecSource(e.target.value);
        const isCustomValue = e.target.value === PrizeNameSource.Custom;
        onChange(prize.prizeId as number, {
            prizeSpec: isCustomValue ? '' : (specs[0]?.skuDesc as string),
        });
    };

    return (
        <Row style={{ width: '100%', borderRadius: 12, backgroundColor: '#FFF', padding: 12 }} align='top'>
            <Col span={5}>
                <Image src={prizeImage} alt={prizeName} width={80} height={80} style={{ borderRadius: 8 }} />
            </Col>
            <Col span={17} style={{ marginLeft: 10 }}>
                {isEditing ? (
                    <Space direction='vertical' style={{ width: '100%' }}>
                        <Form.Item
                            label='商品名称'
                            name='prizeName'
                            style={{ marginBottom: 8 }}
                            rules={[
                                { required: true, message: '请输入商品名称' },
                                { max: 20, message: '商品名称不能超过20个字' },
                            ]}
                        >
                            <Input placeholder='请输入商品名称' style={{ width: '100%' }} showCount maxLength={20} />
                        </Form.Item>
                        <Form.Item style={{ marginBottom: 0 }} wrapperCol={{ span: 24 }}>
                            <Radio.Group value={specSource} options={prizeNameSourceOptions} onChange={handleSpecSourceChange} />
                        </Form.Item>
                        <Form.Item
                            label='商品规格'
                            name='prizeSpec'
                            style={{ marginBottom: 12 }}
                            rules={
                                specSource === PrizeNameSource.OMS
                                    ? [{ required: true, message: '请输入商品规格' }]
                                    : [
                                          { required: true, message: '请输入商品规格' },
                                          { max: 20, message: '商品规格不能超过20个字' },
                                      ]
                            }
                        >
                            {specSource === PrizeNameSource.OMS ? (
                                <Select>
                                    {specs.map(sku => (
                                        <Select.Option key={sku.skuId} value={sku.skuId}>
                                            {sku.skuDesc}
                                        </Select.Option>
                                    ))}
                                </Select>
                            ) : (
                                <Input placeholder='请输入商品规格' style={{ width: '100%' }} showCount maxLength={20} />
                            )}
                        </Form.Item>
                    </Space>
                ) : (
                    <Row justify='start' align='top'>
                        <Col span={24}>
                            <h3 style={{ fontSize: 14, fontWeight: 600, color: '#333' }}>{prizeName}</h3>
                        </Col>
                        <Col span={24}>
                            <p style={{ fontSize: 12, color: '#999' }}>{prizeSpec}</p>
                        </Col>
                    </Row>
                )}
            </Col>
        </Row>
    );
});

export default RealPrizeCard;
