/**
 * @Owners wyy
 * @Title 福袋详情
 */
import { type Coupon } from '@/api/interface/coupon';
import { type LuckyBag } from '@/api/interface/luckyBag';
import { getLiveLuckyBagDetail } from '@/api/modules/luckyBag';
import { RightOutlined } from '@ant-design/icons';
import { Col, Descriptions, Row, message } from 'antd';
import { type FC, useEffect, useState } from 'react';

import CouponCard from './CouponCard';
import LuckBagWinnerInfoModal, { SelectType } from './LuckBagWinnerInfoModal';
interface LuckBagDetailProps {
    liveId: number;
    bagId: number;
}

type LuckBagPageItem = LuckyBag.Response.getLiveLuckyBagDetail;

const LuckBagDetail: FC<LuckBagDetailProps> = ({ liveId, bagId }) => {
    const [detailData, setDetailData] = useState<LuckBagPageItem>();
    // const [currentPrize, setCurrentPrize] = useState<LuckBagPageItem['luckyBagInfo'] | null>(null);
    const [currentFilterType, setCurrentFilterType] = useState<SelectType | null>(null);
    const [winnerInfoVisible, setWinnerInfoVisible] = useState(false);

    const fetchDetail = async () => {
        try {
            const res = await getLiveLuckyBagDetail({ bagId, liveId });
            console.log('🚀 ~ fetchDetail ~ res:', res);
            if (Number(res?.code) === 0) {
                setDetailData(res.data);
            } else {
                message.error(res?.msg || '获取福袋详情失败');
            }
        } catch (error) {
            console.error('获取福袋详情失败:', error);
            message.error('获取福袋详情失败');
        }
    };

    useEffect(() => {
        if (liveId) {
            fetchDetail();
        }
    }, [liveId]);

    const DOORSILL_MAP: { [key: number]: string } = {
        0: '不限',
        1: '未关注主播',
        2: '全部关注主播',
        3: '分享直播间可领',
        4: '邀请进入直播间',
    };

    function onShowWinnerInfo(editPrize: LuckBagPageItem, filterType: SelectType): void {
        console.log('🚀 ~ onShowWinnerInfo ~ editPrize:', editPrize);
        // setCurrentPrize(editPrize);
        setCurrentFilterType(filterType);
        setWinnerInfoVisible(true);
    }

    return (
        <>
            <Descriptions
                column={1}
                bordered={false}
                colon={false}
                labelStyle={{ width: '100px', textAlign: 'right', padding: '8px 16px' }}
                contentStyle={{ width: '80%', padding: '8px 16px' }}
            >
                <Descriptions.Item label='活动备注'>{detailData?.luckyBagInfo?.name}</Descriptions.Item>
                <Descriptions.Item label='活动门槛'>
                    {' '}
                    {DOORSILL_MAP[detailData?.luckyBagInfo?.doorsill as number] || '--'}
                </Descriptions.Item>
                <Descriptions.Item label='限制张数'>{detailData?.luckyBagInfo?.limitNum ?? '不限'}</Descriptions.Item>
                <Descriptions.Item label='活动时间'>{detailData?.luckyBagInfo?.gmtCreate?.toLocaleString()}</Descriptions.Item>
                {detailData?.luckyBagInfo?.couponList && (
                    <Descriptions.Item label='活动奖品'>
                        <div className='luck-bag-detail'>
                            {/* <CouponCardDetail key={String(detailData?.luckyBagInfo?.bagId)} coupon={detailData?.luckyBagInfo} /> */}
                            <CouponCard
                                coupon={
                                    detailData?.luckyBagInfo
                                        ?.couponList[0] as unknown as Coupon.Response.supplierLiveCouponPage['dataList'][number]
                                }
                            />
                            <Row align='middle' className='prize-item-info'>
                                <Col span={6} className='prize-item-info-item'>
                                    <span>{detailData?.totalNum}</span>
                                    <p>奖品数量</p>
                                </Col>
                                <Col
                                    span={6}
                                    className='prize-item-info-item'
                                    onClick={() => onShowWinnerInfo(detailData, SelectType.ALL)}
                                >
                                    <span>{detailData?.receiveNum || 0}</span>
                                    <p>
                                        中奖用户
                                        <RightOutlined />
                                    </p>
                                </Col>
                                <Col
                                    span={6}
                                    className='prize-item-info-item'
                                    onClick={() => onShowWinnerInfo(detailData, SelectType.USED)}
                                >
                                    <span>{detailData?.usedNum || 0}</span>
                                    <p>
                                        已使用
                                        <RightOutlined />
                                    </p>
                                </Col>
                                <Col
                                    span={6}
                                    className='prize-item-info-item'
                                    onClick={() => onShowWinnerInfo(detailData, SelectType.UNUSED)}
                                >
                                    <span>{detailData?.notUsedNum || 0}</span>
                                    <p>
                                        未使用
                                        <RightOutlined />
                                    </p>
                                </Col>
                            </Row>
                        </div>
                    </Descriptions.Item>
                )}
            </Descriptions>
            <LuckBagWinnerInfoModal
                open={winnerInfoVisible}
                activityId={liveId}
                bagId={bagId}
                filterType={currentFilterType as SelectType}
                prizeName={detailData?.luckyBagInfo?.couponList?.[0]?.name as string}
                onClose={() => setWinnerInfoVisible(false)}
            />
        </>
    );
};

export default LuckBagDetail;
