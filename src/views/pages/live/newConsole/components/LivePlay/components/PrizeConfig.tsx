/**
 * @Owners wuze
 * @Title 奖品数量和概率配置组件
 */
import { Form, Input, InputNumber, Space } from 'antd';
import type { FormInstance } from 'antd/lib/form';
import { useContext } from 'react';

import { DataContext } from '../RedPacketTable';
import { ActionType } from '../constant';

interface PrizeConfigProps {
    form: FormInstance;
    record: {
        key: number;
        usableNum?: number; // 奖品库存
    };
    type: 'all' | 'member' | 'nonMember';
    userStatus?: 'new' | 'old';
    estimateNum: number;
    totalPrizeNum: number;
    renderType: 'prizeNum' | 'probability';
}

const PrizeConfig = ({ form, record, type, userStatus, estimateNum, totalPrizeNum, renderType }: PrizeConfigProps) => {
    const { actionType } = useContext(DataContext);

    // 构建表单字段路径
    const getFieldPath = () => {
        const basePath = ['ruleConfig', record.key, type];
        return userStatus ? [...basePath, userStatus] : basePath;
    };

    // 获取当前行的投放数量
    const getCurrentPrizeNum = () => {
        const fieldPath = [...getFieldPath(), 'prizeNum'];
        return form.getFieldValue(fieldPath) || 0;
    };

    // 计算当前行在所有用户类型中的总投放数量/总初始投放数量
    const getCurrentRowTotalPrizeNum = (isInit = false) => {
        const currentKey = record.key;
        let total = 0;

        // 获取所有用户类型的投放数量
        const curPrizeNumField = isInit ? 'initPrizeNum' : 'prizeNum';
        const allPrizeNum = form.getFieldValue(['ruleConfig', currentKey, 'all', curPrizeNumField]) || 0;
        const memberNewPrizeNum = form.getFieldValue(['ruleConfig', currentKey, 'member', 'new', curPrizeNumField]) || 0;
        const memberOldPrizeNum = form.getFieldValue(['ruleConfig', currentKey, 'member', 'old', curPrizeNumField]) || 0;
        const memberPrizeNum = form.getFieldValue(['ruleConfig', currentKey, 'member', curPrizeNumField]) || 0;
        const nonMemberNewPrizeNum =
            form.getFieldValue(['ruleConfig', currentKey, 'nonMember', 'new', isInit ? 'initPrizeNum' : curPrizeNumField]) || 0;
        const nonMemberOldPrizeNum = form.getFieldValue(['ruleConfig', currentKey, 'nonMember', 'old', curPrizeNumField]) || 0;
        const nonMemberPrizeNum = form.getFieldValue(['ruleConfig', currentKey, 'nonMember', curPrizeNumField]) || 0;

        // 根据用户类型分别计算
        if (type === 'all') {
            total = allPrizeNum;
        } else {
            if (type === 'member') {
                if (userStatus) {
                    total += memberNewPrizeNum + memberOldPrizeNum;
                } else {
                    total += memberPrizeNum;
                }
            }
            if (type === 'nonMember') {
                if (userStatus) {
                    total += nonMemberNewPrizeNum + nonMemberOldPrizeNum;
                } else {
                    total += nonMemberPrizeNum;
                }
            }
        }

        return total;
    };

    // 计算最大可设置数量
    const getMaxPrizeNum = () => {
        const curPrizeNum = getCurrentPrizeNum();
        const currentRowTotal = getCurrentRowTotalPrizeNum();

        // 考虑参与人数预估的限制
        const maxByEstimate = estimateNum - (totalPrizeNum - curPrizeNum);

        // 考虑奖品库存的限制
        const remainingStock = (record.usableNum || 0) - (currentRowTotal - curPrizeNum);

        // 取两个限制中的较小值
        if (actionType === ActionType.Edit) {
            // 如果是编辑状态，则可填写的最大值为奖品库存加上当前行的初始投放数量和
            const getCurrentRowTotalInitPrizeNum = getCurrentRowTotalPrizeNum(true);
            return Math.min(maxByEstimate, remainingStock + getCurrentRowTotalInitPrizeNum);
        }

        return Math.min(maxByEstimate, remainingStock);
    };

    // 更新概率
    const updateProbability = (value: number | null) => {
        const probabilityPath = [...getFieldPath(), 'probability'];
        form.setFieldValue(probabilityPath, Number(((Number(value) / estimateNum) * 100).toFixed(3)));
    };

    if (renderType === 'prizeNum') {
        return (
            <>
                <Form.Item name={[...getFieldPath(), 'prizeNum']} initialValue={0} noStyle>
                    <InputNumber min={0} max={getMaxPrizeNum()} style={{ width: 120 }} onChange={updateProbability} />
                </Form.Item>
                <Form.Item name={[...getFieldPath(), 'initPrizeNum']} initialValue={0} hidden>
                    <Input />
                </Form.Item>
            </>
        );
    }

    return (
        <Form.Item name={[...getFieldPath(), 'probability']} initialValue={0} noStyle>
            <Space>{form.getFieldValue([...getFieldPath(), 'probability']) || 0}%</Space>
        </Form.Item>
    );
};

export default PrizeConfig;
