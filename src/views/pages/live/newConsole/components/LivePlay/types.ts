/**
 * @Owners wyy
 * @Title 营销工具接口定义
 */
import { type Coupon } from '@/api/interface/coupon';
import { type LuckyBag } from '@/api/interface/luckyBag';
export type SupplierLiveCouponPageItem = Coupon.Response.supplierLiveCouponPage['dataList'][number];
type LuckBagPageItem = LuckyBag.Response.getLiveLuckyBags['dataList'];
type addLuckyBagItem = LuckyBag.Response.addLuckyBag;
type getLiveLuckyBagDetailItem = LuckyBag.Response.getLiveLuckyBagDetail['luckyBagInfo'];

// 添加状态映射
export const STATUS_MAP = {
    1: '待开始',
    2: '进行中',
    3: '已结束',
} as const;
export interface LuckBagItemProps {
    open: boolean;
    mode?: 'add' | 'edit' | 'view';
    initialValues?: LuckBagPageItem;
    status?: 0 | 1 | 2 | undefined;
    liveId: number; // 添加 liveId
    bagId: number;
    onOk(values: addLuckyBagItem): void;
    onCancel(): void;
    onSuccess(): void;
}

export interface LuckyBagItem {
    id: string;
    remark: string;
    condition: string;
    conditionName: string;
    countdown: string;
    status: '已结束' | '未开始' | '进行中';
    startType: 'auto' | 'manual';
    isCurrentPlatform: boolean;
    participantData: {
        total: number;
        received: number;
    };
    updatedAt: string;
    awards?: {
        amount: number;
        condition: number;
        validityPeriod: number;
        useCondition: number;
    };
}

export interface LuckyBagTableProps {
    liveId: number;
}

export const STATUS_OPTIONS = [
    { label: '全部', key: 0 },
    { label: '待开始', key: 1 },
    { label: '进行中', key: 2 },
    { label: '已结束', key: 3 },
] as const;

export interface CouponItem {
    id: string;
    name: string;
    type: string;
    rule: string;
    validityPeriod: string;
    useRule: string;
    stock: number;
    issueCount?: number;
}

export interface CouponCardProps {
    coupon: SupplierLiveCouponPageItem;
}
export interface CouponCardDetailProps {
    key?: string;
    coupon?: getLiveLuckyBagDetailItem;
}

export interface LuckBagFormValues {
    condition: string;
    conditionName: string;
    limitType: string;
    countdown: string;
    startType: 'auto' | 'manual';
    remark: string;
    awards?: {
        amount: number;
        condition: number;
        validityPeriod: number;
        useCondition: number;
    };
}
