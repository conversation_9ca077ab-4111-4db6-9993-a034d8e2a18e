/**
 * @Owners wyy
 * @Title 新增福袋表单
 */
import { type Coupon } from '@/api/interface/coupon';
import { type LuckyBag } from '@/api/interface/luckyBag';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Card, Form, Input, InputNumber, Radio, Row, Space, TimePicker, message } from 'antd';
import { type FormInstance } from 'antd/es/form';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

import CouponCard from './components/CouponCard';
import SelectCouponModal from './components/SelectCouponModal';

interface LuckBagFormProps {
    form: FormInstance;
    mode?: 'add' | 'edit' | 'view';
    disabled?: boolean;
    couponData?: LuckyBag.Response.getLiveLuckyBags['dataList'];
}

type SupplierLiveCouponPageItem = Coupon.Response.supplierLiveCouponPage['dataList'][number];

const LuckBagForm: React.FC<LuckBagFormProps> = ({ form, mode = 'add', disabled, couponData }) => {
    const isViewMode = mode === 'view' || disabled;
    const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
    const [selectedCoupons, setSelectedCoupons] = useState<SupplierLiveCouponPageItem[]>([]);

    const handleOpenModal = () => {
        setIsCouponModalOpen(true);
    };

    // 取消选择时，清空临时选择
    const handleCancelSelect = () => {
        setIsCouponModalOpen(false);
    };

    // 添加 useEffect 处理编辑状态下的数据回显
    useEffect(() => {
        if (mode === 'edit') {
            // 从表单中获取已选择的优惠券列表
            const processedCoupons = couponData?.couponList.map(item => {
                const goodsInfoList = [
                    {
                        categoryName: item.goods?.[0]?.categoryName || '',
                        spuId: Number(item.goods?.[0]?.brandId) || 0,
                        spuName: item.goods?.[0]?.shortTitle || '',
                        spuPic: item.goods?.[0]?.goodsCardPic || '',
                    },
                ];

                return {
                    ...item,
                    goodsInfoList,
                    spuInfoList: item.spuInfoList ?? [],
                    name: item.name ?? '',
                    status: item.status ?? 0,
                    discountType: item.discountType ?? 0,
                    expiryEnd: item.expiryEnd ? item.expiryEnd.toString() : undefined,
                    expiryStart: item.expiryStart ? item.expiryStart.toString() : undefined,
                    gmtModified: item.gmtModified ? item.gmtModified.toString() : undefined,
                };
            });

            setSelectedCoupons(processedCoupons || []);
            form.setFieldsValue({
                couponId: couponData?.couponList?.[0]?.couponId,
                couponList: couponData?.couponList?.[0]?.applyNum,
            });
        }
    }, [mode, couponData, form]);

    const handleDeleteCoupon = () => {
        // 更新选中的优惠券列表
        // const newSelectedCoupons = selectedCoupons.filter(item => item.id !== couponId);
        setSelectedCoupons([]);

        form.setFieldValue('couponList', undefined);
    };

    const handleIssueCountChange = (couponId: number, value: number | null) => {
        console.log('🚀 ~ handleIssueCountChange ~ couponId:', couponId, value);
        // 获取当前表单中的优惠券列表
        // const currentCouponList = form.getFieldValue('couponList') || [];

        // 更新指定优惠券的 transferNum
        // const updatedCouponList = (currentCouponList || [])?.map((item: CouponListItem) => {
        //     if (item.couponId === couponId) {
        //         return {
        //             ...item,
        //             transferNum: value,
        //         };
        //     }
        //     return item;
        // });

        // 更新表单数据
        form.setFieldsValue({
            couponList: value,
            couponId,
        });
    };

    /** 已选优惠券ID */
    const selectedCouponIds = useMemo(() => selectedCoupons.map(coupon => coupon?.id as number), [selectedCoupons]);

    const limitType = Form.useWatch('limitType', form);

    return (
        <>
            <Form
                form={form}
                layout='horizontal'
                validateTrigger={['onChange', 'onBlur']}
                initialValues={{ condition: 'unlimited', limitType: 'unlimited', startType: 'immediate' }}
                disabled={isViewMode}
                colon={false}
                labelAlign='right'
                labelCol={{ span: 3 }}
                wrapperCol={{ span: 20 }}
            >
                <Form.Item
                    label='福袋备注'
                    name='remark'
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                        {
                            max: 20,
                            message: '最多可输入20字',
                        },
                    ]}
                >
                    <Input
                        placeholder='活动备注用户不可见'
                        maxLength={20}
                        showCount={!isViewMode}
                        onChange={e => {
                            if (e.target.value.length > 20) {
                                message.warning('最多可输入20字');
                            }
                        }}
                    />
                </Form.Item>
                <Form.Item label='活动门槛' required>
                    <Form.Item
                        name='condition'
                        validateTrigger={['onChange', 'onBlur']}
                        rules={[{ required: true, message: '请选择领取条件' }]}
                        className='luck-bag-table-content-form'
                    >
                        <Radio.Group
                            onChange={e => {
                                if (e.target.value !== 'invite') {
                                    form.setFieldValue('inviteCount', undefined);
                                    form.setFields([{ name: 'inviteCount', errors: [] }]);
                                }
                            }}
                        >
                            <Radio value='unlimited'>不限</Radio>
                            <Radio value='follow'>关注主播可参与</Radio>
                            <Radio value='share'>仅分享直播间可参与</Radio>
                            <Row className='luck-bag-table-content-form-invite' align='middle'>
                                <Radio style={{ margin: 0 }} value='invite'>
                                    <span>邀请好友可参与</span>
                                </Radio>
                                <Form.Item
                                    noStyle
                                    shouldUpdate={(prevValues, currentValues) => prevValues.condition !== currentValues.condition}
                                >
                                    {({ getFieldValue }) => {
                                        const isInvite = getFieldValue('condition') === 'invite';
                                        return isInvite ? (
                                            <Form.Item
                                                name='inviteCount'
                                                style={{ margin: 0 }}
                                                className='luck-bag-table-content-form-count'
                                                rules={[
                                                    {
                                                        validator: (_, value) => {
                                                            if (value === null || value === undefined) {
                                                                return Promise.reject('请输入邀请人数');
                                                            }
                                                            if (value <= 0) {
                                                                return Promise.reject('请输入大于0的整数');
                                                            }
                                                            return Promise.resolve();
                                                        },
                                                    },
                                                ]}
                                            >
                                                <div className='luck-bag-table-content-form-inviteCount'>
                                                    邀请
                                                    <InputNumber
                                                        min={1}
                                                        precision={0}
                                                        defaultValue={mode === 'edit' ? couponData?.target : undefined}
                                                        className='luck-bag-table-content-form-number'
                                                        placeholder='请输入'
                                                        disabled={getFieldValue('condition') !== 'invite'}
                                                        onChange={value => {
                                                            form.setFieldValue('inviteCount', value);
                                                            if (getFieldValue('condition') === 'invite') {
                                                                form.validateFields(['inviteCount']);
                                                            }
                                                        }}
                                                    />
                                                    <span className='luck-bag-table-content-form-tip'>
                                                        位好友进入直播间可参与
                                                    </span>
                                                </div>
                                            </Form.Item>
                                        ) : null;
                                    }}
                                </Form.Item>
                            </Row>
                        </Radio.Group>
                    </Form.Item>
                </Form.Item>

                <Form.Item label='限领张数' required>
                    <Form.Item
                        name='limitType'
                        validateTrigger={['onChange', 'onBlur']}
                        rules={[{ required: true, message: '请选择限领次数' }]}
                        className='luck-bag-table-content-form'
                    >
                        <Radio.Group>
                            <Space direction='vertical'>
                                <Radio value='unlimited'>不限</Radio>
                                <Row className='luck-bag-table-content-form-invite' align='middle'>
                                    <Radio style={{ margin: 0 }} value='limited'>
                                        每人限领
                                    </Radio>
                                    {limitType === 'limited' && (
                                        <Form.Item
                                            noStyle
                                            shouldUpdate={(prevValues, currentValues) =>
                                                prevValues.limitType !== currentValues.limitType
                                            }
                                        >
                                            {({ getFieldValue }) => (
                                                <Form.Item
                                                    name='limitCount'
                                                    style={{ marginBottom: 0 }}
                                                    className='luck-bag-table-content-form-count'
                                                    rules={[
                                                        {
                                                            required: getFieldValue('limitType') === 'limited',
                                                            message: '请输入限领张数',
                                                        },
                                                        {
                                                            type: 'number',
                                                            min: 1,
                                                            max: 10,
                                                            message: '请输入1-10之间的整数',
                                                        },
                                                    ]}
                                                >
                                                    <InputNumber
                                                        min={1}
                                                        max={10}
                                                        precision={0}
                                                        className='luck-bag-table-content-form-number'
                                                        placeholder='请输入'
                                                        defaultValue={mode === 'edit' ? couponData?.limitNum : 0}
                                                        disabled={getFieldValue('limitType') !== 'limited'}
                                                        onChange={value => {
                                                            if (value && value > 10) {
                                                                message.warning('每人限领张数不能超过10');
                                                            }
                                                            form.setFieldValue('limitCount', value);
                                                            if (getFieldValue('limitType') === 'limited') {
                                                                form.validateFields(['limitCount']);
                                                            }
                                                        }}
                                                    />
                                                    <span className='luck-bag-table-content-form-tip'>张</span>
                                                </Form.Item>
                                            )}
                                        </Form.Item>
                                    )}
                                </Row>
                            </Space>
                        </Radio.Group>
                    </Form.Item>
                </Form.Item>

                <Form.Item label='活动时间' required>
                    <Form.Item
                        name='startType'
                        validateTrigger={['onChange', 'onBlur']}
                        rules={[{ required: true, message: '请选择开始时间' }]}
                        className='luck-bag-table-content-form'
                    >
                        <Radio.Group>
                            <Radio value='immediate'>立即开始</Radio>
                            <Radio value='countdown'>定时开始</Radio>
                            <Radio value='manual'>手动开始</Radio>
                            <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, currentValues) => prevValues.startType !== currentValues.startType}
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('startType') === 'countdown' && (
                                        <Form.Item
                                            name='countdownSeconds'
                                            className='luck-bag-table-content-form-date'
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择倒计时时间',
                                                },
                                            ]}
                                        >
                                            <TimePicker
                                                className='luck-bag-table-content-form-number'
                                                format='mm:ss'
                                                placeholder='请选择开始时间'
                                                defaultValue={
                                                    mode === 'edit' && couponData?.countDownNum
                                                        ? dayjs()
                                                              .minute(Math.floor(couponData.countDownNum / 60))
                                                              .second(couponData.countDownNum % 60)
                                                        : null
                                                }
                                                onChange={value => {
                                                    if (value) {
                                                        const seconds = value.minute() * 60 + value.second();
                                                        if (seconds > 600) {
                                                            message.warning('倒计时时间不能超过10分钟');
                                                            return;
                                                        }
                                                        form.setFieldValue('countdownSeconds', seconds);
                                                    } else {
                                                        form.setFieldValue('countdownSeconds', null);
                                                    }
                                                }}
                                                hideDisabledOptions
                                            />
                                            <span className='luck-bag-table-content-form-tip'>后开始</span>
                                            {getFieldValue('countdownSeconds') > 0 && (
                                                <div style={{ color: '#FF8F16', fontSize: 12, margin: 6 }}>
                                                    {dayjs()
                                                        .minute(Math.floor(getFieldValue('countdownSeconds') / 60))
                                                        .format('mm')}
                                                    分{' '}
                                                    {dayjs()
                                                        .second(getFieldValue('countdownSeconds') % 60)
                                                        .format('ss')}
                                                    秒
                                                </div>
                                            )}
                                        </Form.Item>
                                    )
                                }
                            </Form.Item>
                        </Radio.Group>
                    </Form.Item>
                </Form.Item>

                <Form.Item
                    label='活动奖品'
                    name='couponList'
                    required
                    className='luck-bag-table-content-form'
                    rules={[
                        {
                            validator: (_rule, value, callback) => {
                                if (selectedCoupons.length === 0) {
                                    callback('活动奖品不能为空');
                                }
                                callback();
                            },
                        },
                    ]}
                >
                    {!isViewMode ? (
                        <>
                            {selectedCoupons.length > 0 ? (
                                <div style={{ width: 440, padding: '12px', background: '#F7F8F9', borderRadius: '10px' }}>
                                    <Space direction='vertical' style={{ width: '100%' }}>
                                        {selectedCoupons.map(coupon => (
                                            <>
                                                <div className='lucky-coupon-header'>
                                                    <span className='lucky-coupon-header-title'>{coupon.name}</span>
                                                    <Button
                                                        type='link'
                                                        danger
                                                        onClick={handleDeleteCoupon}
                                                        style={{ padding: 0 }}
                                                    >
                                                        删除
                                                    </Button>
                                                </div>
                                                <CouponCard key={coupon.id} coupon={coupon} />
                                                <div className='lucky-coupon-footer'>
                                                    <div className='lucky-coupon-footer-left'>
                                                        当前库存：<span>{coupon.usableNum}</span>
                                                    </div>
                                                    <div className='lucky-coupon-footer-right'>
                                                        <span>奖品发放数量</span>
                                                        <InputNumber
                                                            min={1}
                                                            suffix='张'
                                                            max={coupon.usableNum}
                                                            precision={0}
                                                            style={{ width: '80px' }}
                                                            placeholder='请输入'
                                                            defaultValue={
                                                                mode === 'edit' ? couponData?.couponList[0].applyNum : 1
                                                            }
                                                            onChange={value => handleIssueCountChange(coupon.id as number, value)}
                                                        />
                                                    </div>
                                                </div>
                                            </>
                                        ))}
                                    </Space>
                                </div>
                            ) : null}
                            {selectedCoupons.length === 0 && (
                                <Button type='primary' ghost onClick={handleOpenModal}>
                                    <PlusOutlined />
                                    添加奖品
                                </Button>
                            )}
                        </>
                    ) : (
                        // 查看模式下的展示
                        <Space direction='vertical' style={{ width: '100%' }}>
                            {selectedCoupons.map(coupon => (
                                <Card key={coupon.id} size='small'>
                                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                                        <div>
                                            <div>
                                                <strong>{coupon.name}</strong>
                                            </div>
                                            <div style={{ color: '#666' }}>{coupon.desc}</div>
                                        </div>
                                        <div style={{ textAlign: 'right' }}>
                                            <div>{coupon.fullSubtractType}</div>
                                            <div style={{ color: '#666' }}>库存: {coupon.usableNum}</div>
                                        </div>
                                    </div>
                                </Card>
                            ))}
                        </Space>
                    )}
                </Form.Item>

                <Form.Item name='couponId' hidden>
                    <Input />
                </Form.Item>
            </Form>
            <SelectCouponModal
                open={isCouponModalOpen}
                selectedIds={selectedCouponIds}
                onOk={selectedRows => {
                    setSelectedCoupons(selectedRows);
                    setIsCouponModalOpen(false);

                    const couponInfo = selectedRows?.[0];
                    form.setFieldValue('couponId', couponInfo?.id);
                    form.setFieldValue('couponList', 1);
                }}
                onCancel={handleCancelSelect}
            />
        </>
    );
};

export default LuckBagForm;
