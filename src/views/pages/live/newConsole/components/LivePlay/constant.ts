/**
 * @Owners wuze
 * @Title 活动管理相关常量
 */

export const DefaultFloatingLayerImage =
    'https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/lucky-bag-style-1-v3.png';

// 红包雨预览弹窗
export const DEFAULT_POP_IMAGE_LIST =
    'https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/live-activity-default-pop-image1-v2.png';
export const DEFAULT_POP_IMAGE =
    'https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/live-activity-default-pop-image1-v2.png';
export const DEFAULT_ELEMENT_IMAGE = 'https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/red-packet-default-v2.png';

/** 奖项类型 */
export enum PrizeType {
    /** 实物奖品 */
    RealPrize = 1,
    /** 优惠券 */
    Coupon = 2,
}

export enum ActivityStatus {
    /** 待开始 */
    Pending = 1,
    /** 进行中 */
    InProgress = 2,
    /** 已结束 */
    Ended = 3,
}

export enum ActionType {
    /** 添加 */
    Add = 1,
    /** 编辑 */
    Edit = 2,
    /** 查看 */
    View = 3,
    /** 复制 */
    Copy = 4,
}

export const MODAL_TITLE_MAP = {
    [ActionType.Add]: '新增活动',
    [ActionType.Copy]: '复制活动',
    [ActionType.Edit]: '编辑活动',
    [ActionType.View]: '查看活动',
};

// 福袋预览图片数组
export const LuckBagPreviewImages = [
    { src: 'https://stantic.ifengqun.com/new-fengqun/fq-shop/icons/preview-luckbag1.png', title: '直播间福袋入口' },
    { src: 'https://stantic.ifengqun.com/new-fengqun/fq-shop/icons/preview-luckbag2.png', title: '福袋弹窗' }, // 这里可以替换为其他预览图
];

// 红包雨预览图片数组
export const RedPacketRainPreviewImages = [
    { src: 'https://stantic.ifengqun.com/new-fengqun/fq-shop/icons/preview-redpacket1.png', title: '直播间红包雨入口' },
    { src: 'https://stantic.ifengqun.com/new-fengqun/fq-shop/icons/preview-redpacket2.png', title: '红包雨通知弹窗' }, // 这里可以替换为其他预览图
];

/** 标签颜色 */
export const TAG_COLOR_MAP: { [key: number]: { color: string; border: string; background: string } } = {
    1: {
        color: '#fff',
        border: 'none',
        background: '#FF8F16',
    }, // 待开始
    2: {
        color: '#fff',
        border: 'none',
        background: 'linear-gradient( 135deg, #FD4A39 0%, #FD397E 100%)',
    }, // 进行中
    3: {
        color: '#999',
        border: 'none',
        background: '#EEE',
    }, // 已结束
};
