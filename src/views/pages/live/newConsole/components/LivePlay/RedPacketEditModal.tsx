/**
 * @Owners felix.cai
 * @Title 活动编辑弹窗
 */
import { type Coupon } from '@/api/interface/coupon';
import { type Goods } from '@/api/interface/goods';
import { type RedPacket } from '@/api/interface/live/redpacket';
import { CodeEnum } from '@/api/interface/request';
import { addActiveRedPacketRain, updateActiveRedPacketRain } from '@/api/modules/live/console';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, DatePicker, Drawer, Dropdown, Form, Input, Popconfirm, Radio, Row, Space, Tag, message } from 'antd';
import dayjs from 'dayjs';
import { uniqBy } from 'lodash';
import { useEffect, useMemo, useState } from 'react';

import { ACTIVITY_STATUS_MAP } from './RedPacketTable';
import PreviewImage from './components/PreviewImage';
import PrizeItem from './components/PrizeItem';
import SelectCouponModal from './components/SelectCouponModal';
import SelectVirtualPrizeModal from './components/SelectVirtualPrizeModal';
import WinnerInfoModal, { type SelectType } from './components/WinnerInfoModal';
import {
    ActionType,
    ActivityStatus,
    DEFAULT_ELEMENT_IMAGE,
    DEFAULT_POP_IMAGE_LIST,
    DefaultFloatingLayerImage,
    MODAL_TITLE_MAP,
    PrizeType,
    RedPacketRainPreviewImages,
    TAG_COLOR_MAP,
} from './constant';
import './index.scss';

const { RangePicker } = DatePicker;

// 活动门槛选项
const thresholdOptions = [
    { label: '不限', value: 0, style: { margin: 0 } },
    { label: '关注主播可参与', value: 1, style: { margin: 0 } },
    { label: '仅分享直播间可参与', value: 2, style: { margin: 0 } },
];

/** 红包雨默认活动配置 */
const defaultRedPacketConfig = {
    // 活动类型 1红包雨
    activityType: 1,
    // 倒计时时间
    countdownTime: 15,
    // 红包雨样式配置
    elementImages: [DEFAULT_ELEMENT_IMAGE],
    // 红包雨样式配置
    floatingLayerImage: DefaultFloatingLayerImage,
    floatingLayerType: 1,
    // 红包雨样式配置
    popImage: DEFAULT_POP_IMAGE_LIST,
    // 领取方式选项 活动开始后自动弹出
    receiveType: 2,
    // 奖励类型
    rewardType: 2,
    // 发货时间
    deliverDay: 7,
    // 领取时限
    receiveTime: 48,
};

interface RedPacketEditModalProps {
    liveId: number | undefined;
    actionType: ActionType;
    activityDetail: ActivityDetail | null;
    open: boolean;
    onOk(): void;
    onCopy(): void;
    onEdit(): void;
    onCancel(): void;
    onDelete(): void;
    onEndActivity(): void;
}

type ActivityDetail = RedPacket.Response.GetActiveRedPacketRainDetail;
type ValidateFieldsErr = {
    errorFields: { errors: string }[];
};
type SupplierLiveCouponPageItem = Coupon.Response.supplierLiveCouponPage['dataList'][number];

export type RealPrizeItem = RedPacket.Response.ActiveLotteryAnchorPrizeRespDTO;

const RedPacketEditModal = (props: RedPacketEditModalProps) => {
    const { liveId, actionType, activityDetail, open, onOk, onCancel, onDelete, onCopy, onEdit, onEndActivity } = props;
    // const [activityDetail, setActivityDetail] = useState<ActivityDetail | null>(null);
    const [form] = Form.useForm();
    const [prizes, setPrizes] = useState<RedPacket.PrizeConfig[]>([]);
    const [showSelectVirtualPrizeModal, setShowSelectVirtualPrizeModal] = useState<boolean>(false);
    const [isCouponModalOpen, setIsCouponModalOpen] = useState(false);
    const [winnerInfoVisible, setWinnerInfoVisible] = useState(false);

    const [currentPrize, setCurrentPrize] = useState<RedPacket.PrizeConfig | null>(null);
    const [currentFilterType, setCurrentFilterType] = useState<SelectType | null>(null);

    useEffect(() => {
        if (open && actionType === ActionType.Add) {
            form?.setFieldsValue({
                activityThreshold: 0,
                ...defaultRedPacketConfig,
            });
        }
        if (!open) {
            form?.resetFields();
        }
    }, [open]);

    useEffect(() => {
        if (activityDetail) {
            form?.setFieldsValue({
                activityName: activityDetail.activityName,
                activityThreshold: activityDetail.activityThreshold,
                // 复制活动需要将活动时间置空
                activityTime:
                    activityDetail.startTime && activityDetail.endTime && actionType !== ActionType.Copy
                        ? [dayjs(activityDetail?.startTime), dayjs(activityDetail?.endTime)]
                        : undefined,
                startTime: activityDetail.startTime,
                endTime: activityDetail.endTime,
                receiveType: activityDetail.receiveType,
                rewardType: activityDetail.rewardType,
                activityStatus: activityDetail.activityStatus,
                deliverDay: activityDetail.deliverDay,
                receiveTime: activityDetail.receiveTime,
            });

            if (activityDetail.activeRedPacketRainRule) {
                setPrizes(activityDetail.activeRedPacketRainRule?.prizeConfigList || []);
            }
        }
    }, [activityDetail]);

    // 打开弹窗时，将已选择的优惠券同步到临时选择
    const handleOpenModal = () => {
        setIsCouponModalOpen(true);
    };

    // 确认选择时，将临时选择同步到实际选择
    const handleConfirmSelect = (selectedRows: SupplierLiveCouponPageItem[]) => {
        const formattedSelectedRows = selectedRows.map(item => ({
            allNum: item?.usableNum,
            prizeId: item?.id,
            prizeKey: `${item?.id}`,
            prizeName: item?.name,
            usableNum: item?.usableNum,
            prizeSource: 0,
            prizeSpec: item?.desc,
            prizeType: PrizeType.Coupon,
            couponBaseRes: item,
        }));
        setPrizes(prevPrizes => uniqBy([...prevPrizes, ...formattedSelectedRows], 'prizeId'));
        setIsCouponModalOpen(false);
    };

    // 取消选择时，清空临时选择
    const handleCancelSelect = () => {
        setIsCouponModalOpen(false);
    };

    const [loading, setLoading] = useState(false);
    const handleOk = async () => {
        try {
            setLoading(true);
            const baseValues = await form?.validateFields();

            const values = {
                ...baseValues,
            };

            const params = {
                foreignId: liveId as number,
                activityName: values.activityName,
                activityThreshold: values.activityThreshold,

                startTime: values.startTime,
                endTime: values.endTime,

                ...defaultRedPacketConfig,

                activeRedPacketRainRule: {
                    prizeConfigList: prizes.map(prize => ({
                        allNum: prize?.allNum,
                        prizeImage: prize?.prizeImage,
                        prizeKey: prize?.prizeKey,
                        prizeName: prize?.prizeName,
                        prizeSource: prize?.prizeSource,
                        prizeSpec: prize?.prizeSpec,
                        prizeType: prize?.prizeType,
                    })),
                },
            };

            if (activityDetail?.id) {
                // 编辑场景
                const res = await updateActiveRedPacketRain({
                    id: activityDetail?.id,
                    ...params,
                });
                if (res.code !== CodeEnum.SUCCESS) {
                    message.error(res.msg || '编辑失败');
                } else {
                    resetData();
                    onOk();
                }
            } else {
                // 新增场景
                const res = await addActiveRedPacketRain({
                    ...params,
                });
                if (res.code !== CodeEnum.SUCCESS) {
                    message.error(res.msg || '新增失败');
                } else {
                    resetData();
                    onOk();
                }
            }
            setLoading(false);
        } catch (err) {
            console.error('Form validation failed:', err);
            const error = err as ValidateFieldsErr;
            const name = error?.errorFields?.[0]?.errors?.[0];
            if (name) {
                message.error(name);
            }
            setLoading(false);
        }
    };
    // 重置数据
    const resetData = () => {
        form?.resetFields();
    };

    const handleCancel = () => {
        resetData();
        setPrizes([]);
        onCancel();
    };

    const handleAddPrize = (type: PrizeType) => {
        if (type === PrizeType.RealPrize) {
            setShowSelectVirtualPrizeModal(true);
        }
        if (type === PrizeType.Coupon) {
            handleOpenModal();
        }
    };
    const handleFieldChange = (prizeId: number, updateValues: Partial<RedPacket.PrizeConfig>) => {
        // Update prize data logic here
        setPrizes(prevPrizes =>
            prevPrizes.map(prize => {
                if (Number(prize?.prizeId) === prizeId) {
                    return { ...prize, ...updateValues };
                }
                return prize;
            })
        );
    };

    const modalTitle = useMemo(() => {
        if (MODAL_TITLE_MAP[actionType]) return MODAL_TITLE_MAP[actionType];
        return '';
    }, [actionType]);

    // const handleEditPrize = (_index: number) => {
    //     // 编辑奖品逻辑
    // };

    // const handleDeletePrize = (index: number) => {
    //     const newPrizes = prizes.filter((_, i) => i !== index);
    //     setPrizes(newPrizes);
    // };

    const handleSelectVirtualPrize = (
        selectedRealPrize: RedPacket.Response.ActiveRedPacketRealPrizeListRespVO[],
        selectedOfficialPrize: Goods.Response.getSupplierGoodsPage['dataList'][number][]
    ) => {
        const formattedSelectedRow: RedPacket.PrizeConfig[] = selectedRealPrize.map(item => ({
            prizeSource: 1,
            usableNum: item?.num,
            prizeId: Number(item?.realPrizeId),
            prizeKey: `${item?.realPrizeId}`,
            prizeType: PrizeType.RealPrize,
            prizeName: item?.prizeName,
            prizeImage: item?.prizeImage,
            prizeSpec: item?.prizeSpec,
            probability: 0,
            couponBaseRes: undefined,
            usedNum: 0,
        }));
        const formattedSelectedOfficialPrize: RedPacket.PrizeConfig[] = selectedOfficialPrize.map(item => ({
            prizeSource: 2,
            usableNum: 0,
            prizeId: Number(item?.spuId),
            prizeKey: `${item?.spuId}`,
            prizeType: PrizeType.RealPrize,
            prizeName: item?.shortTitle || item?.spuName,
            prizeImage: item?.headPic,
            prizeSpec: item?.minSkuDesc,
            probability: 0,
            couponBaseRes: undefined,
            usedNum: 0,
        }));

        setPrizes(prevPrizes => uniqBy([...prevPrizes, ...formattedSelectedRow, ...formattedSelectedOfficialPrize], 'prizeId'));
        setShowSelectVirtualPrizeModal(false);
    };

    const handleDeletePrize = (prizeId: number) => {
        setPrizes(prevPrizes => prevPrizes.filter(prize => Number(prize?.prizeId) !== prizeId));
    };

    const handleShowWinnerInfo = (prize: RedPacket.PrizeConfig, filterType: SelectType) => {
        setCurrentPrize(prize);
        setCurrentFilterType(filterType);
        setWinnerInfoVisible(true);
    };

    /** 已选优惠券ID */
    const selectedCouponIds = useMemo(
        () => prizes.filter(prize => prize?.prizeType === PrizeType.Coupon).map(prize => prize?.prizeId as number),
        [prizes]
    );

    return (
        <Drawer
            title={
                <>
                    <span style={{ marginRight: 18 }}>{modalTitle}</span>
                    {[ActionType.Edit, ActionType.View].includes(actionType) &&
                        (activityDetail?.openStatus === 1 ? (
                            <Tag style={TAG_COLOR_MAP[activityDetail?.activityStatus as number] || {}}>
                                {activityDetail?.activityStatus === ActivityStatus.InProgress && (
                                    <img
                                        src='https://applet.ifengqun.com/fq-mall/prod/discover/discover-loading.gif'
                                        style={{
                                            width: '12px',
                                            height: '12px',
                                            marginRight: '4px',
                                        }}
                                        alt=''
                                    />
                                )}
                                {ACTIVITY_STATUS_MAP[activityDetail?.activityStatus as number]}
                            </Tag>
                        ) : (
                            <Tag color='#EEE' style={{ color: '#999' }}>
                                已结束
                            </Tag>
                        ))}
                </>
            }
            open={open}
            onClose={handleCancel}
            width={1100}
            maskClosable={false}
            destroyOnClose
            footer={
                <Row justify='end'>
                    <Space>
                        {actionType !== ActionType.View && (
                            <>
                                <Button onClick={handleCancel}>取消</Button>
                                <Button type='primary' onClick={handleOk} loading={loading}>
                                    确定
                                </Button>
                            </>
                        )}
                        {actionType === ActionType.View && (
                            <>
                                {activityDetail?.activityStatus === ActivityStatus.Pending && (
                                    <Popconfirm title='确定删除该活动吗？' onConfirm={onDelete}>
                                        <Button danger>删除</Button>
                                    </Popconfirm>
                                )}
                                <Button onClick={onCopy}>复制创建</Button>
                                {activityDetail?.activityStatus === ActivityStatus.Pending && (
                                    <Button type='primary' onClick={onEdit}>
                                        编辑
                                    </Button>
                                )}
                                {activityDetail?.activityStatus === ActivityStatus.InProgress && (
                                    <Popconfirm title='确定结束该活动吗？' onConfirm={onEndActivity}>
                                        <Button ghost danger>
                                            结束活动
                                        </Button>
                                    </Popconfirm>
                                )}
                            </>
                        )}
                    </Space>
                </Row>
            }
        >
            <Row gutter={12}>
                <Col span={16}>
                    <Form
                        form={form}
                        colon={false}
                        labelAlign='right'
                        layout='horizontal'
                        labelCol={{ span: 3 }}
                        wrapperCol={{ span: 20 }}
                        disabled={actionType === ActionType.View}
                    >
                        <Form.Item
                            label='活动备注'
                            name='activityName'
                            rules={[
                                // {
                                //     required: true,
                                //     message: '请输入活动备注',
                                // },
                                {
                                    validator: (_, value) => {
                                        // eslint-disable-next-line @typescript-eslint/tslint/config
                                        const emojiRegex = /[\u{1F600}-\u{1F64F}]/u; // 简单的 emoji 检测
                                        if (value && emojiRegex.test(value)) {
                                            return Promise.reject(new Error('活动备注不能包含表情符号'));
                                        }
                                        return Promise.resolve();
                                    },
                                },
                            ]}
                        >
                            <Input style={{ width: '400px' }} placeholder='请输入活动备注' maxLength={50} showCount />
                        </Form.Item>

                        <Form.Item
                            label='活动门槛'
                            name='activityThreshold'
                            rules={[{ required: true, message: '请选择活动门槛' }]}
                        >
                            <Radio.Group options={thresholdOptions} />
                        </Form.Item>

                        <Form.Item
                            label='活动时间'
                            name='activityTime'
                            rules={[{ required: true, message: '请选择活动时间' }]}
                            style={{ marginBottom: 32 }}
                        >
                            <RangePicker
                                showNow
                                allowClear
                                showTime={{
                                    format: 'HH:mm:ss',
                                    defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('00:00:00', 'HH:mm:ss')],
                                }}
                                style={{ width: '400px' }}
                                format='YYYY-MM-DD HH:mm:ss'
                                placeholder={['开始时间', '结束时间']}
                                separator='~'
                                getPopupContainer={triggerNode =>
                                    (triggerNode as unknown as { parentNode: HTMLElement }).parentNode
                                }
                                onChange={(_dateObj, dates) => {
                                    form.setFieldsValue({
                                        startTime: dates?.[0],
                                        endTime: dates?.[1],
                                    });
                                }}
                            />
                        </Form.Item>

                        <Form.Item name='startTime' hidden>
                            <Input />
                        </Form.Item>

                        <Form.Item name='endTime' hidden>
                            <Input />
                        </Form.Item>

                        {/* 活动奖品 */}
                        <Form.Item
                            label='活动奖品'
                            required
                            rules={[
                                {
                                    validator: (_rule, value, callback) => {
                                        if (prizes.length === 0) {
                                            callback('活动奖品不能为空');
                                        }
                                        callback();
                                    },
                                },
                            ]}
                        >
                            {actionType !== ActionType.View && (
                                <Row align='middle'>
                                    <Dropdown
                                        menu={{
                                            items: [
                                                {
                                                    label: '添加优惠券',
                                                    key: 'coupon',
                                                    onClick: () => handleAddPrize(PrizeType.Coupon),
                                                },
                                                {
                                                    label: '添加实物奖品',
                                                    key: 'realPrize',
                                                    onClick: () => handleAddPrize(PrizeType.RealPrize),
                                                },
                                            ],
                                        }}
                                    >
                                        <Button ghost type='primary'>
                                            <PlusOutlined />
                                            添加奖品
                                        </Button>
                                    </Dropdown>
                                    {prizes?.length > 0 && (
                                        <Col style={{ marginLeft: 12 }} className='red-packet-edit-modal-prize-count'>
                                            已选<strong>{prizes?.length}</strong>个奖品
                                        </Col>
                                    )}
                                </Row>
                            )}
                            <Row style={{ width: 440, maxHeight: 800, marginTop: 24, overflowY: 'auto', overflowX: 'hidden' }}>
                                {prizes.map((prize, index) => (
                                    <PrizeItem
                                        prizeNo={index}
                                        key={prize.prizeId}
                                        activityDetail={activityDetail}
                                        onChange={handleFieldChange}
                                        prize={prize}
                                        onShowWinnerInfo={handleShowWinnerInfo}
                                        onDelete={handleDeletePrize}
                                    />
                                ))}
                            </Row>
                        </Form.Item>
                    </Form>
                </Col>
                {actionType !== ActionType.View && (
                    <Col span={8} style={{ display: 'flex', justifyContent: 'center' }}>
                        <PreviewImage images={RedPacketRainPreviewImages} />
                    </Col>
                )}
            </Row>

            {showSelectVirtualPrizeModal && (
                <SelectVirtualPrizeModal
                    visible={showSelectVirtualPrizeModal}
                    onOk={handleSelectVirtualPrize}
                    onCancel={() => setShowSelectVirtualPrizeModal(false)}
                    selectedPrizeIds={prizes?.map((prize: RealPrizeItem) => prize?.prizeId as number) || []}
                />
            )}

            <SelectCouponModal
                multiple
                open={isCouponModalOpen}
                selectedIds={selectedCouponIds}
                onOk={handleConfirmSelect}
                onCancel={handleCancelSelect}
            />

            <WinnerInfoModal
                open={winnerInfoVisible}
                activityId={activityDetail?.id as number}
                filterType={currentFilterType as SelectType}
                prizeType={currentPrize?.prizeType as number}
                prizeName={currentPrize?.prizeName as string}
                prizeId={currentPrize?.prizeId as number}
                onClose={() => setWinnerInfoVisible(false)}
            />
        </Drawer>
    );
};

export default RedPacketEditModal;
