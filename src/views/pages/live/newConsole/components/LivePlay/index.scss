.live-console-bottom-left-collapse {
    &-item {
        display: flex;
        flex-direction: row;
        padding: 24px 0;
    }
    &-avatar {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100px;
        p {
            font-size: 14px;
            color: #666;
        }
    }
}
.live-console-bottom-left-collapse-item {
}
.luck-bag-table {
    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    &-content {
        &-form {
            margin-bottom: 0;
            &-invite {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
            }
            &-count {
                margin-bottom: 0;
                margin-left: 0;
            }
            &-inviteCount,
            &-date {
                margin-top: 8px;
                background: #f7f8f9;
                border-radius: 8px;
                padding: 10px;
            }
            &-number {
                width: 150px;
                margin-left: 7px;
            }
            &-tip {
                margin-left: 10px;
            }
        }
    }
    &-action {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;
        width: 144px;
    }
}
.lucky-coupon-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    &-title {
        font-size: 14px;
        color: #333;
    }
}
.lucky-coupon-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 12px;
    padding: 0 0 0 12px;
    background: #f7f8f9;
    border-radius: 4px;
    &-left {
        color: #666;
        span {
            color: #ff8f16;
        }
    }
    &-right {
        display: flex;
        align-items: center;
        gap: 8px;
        span {
            color: '#666';
        }
    }
}
.ant-btn-variant-solid {
    background: #00b68f;
}
.highlight-row {
    background-color: #fff1f0;
    & > td {
        background-color: #fff1f0 !important;
    }
}
.ant-radio-wrapper {
    margin-bottom: 8px;
}

.red-packet-edit-modal-prize-count {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 22px;
    strong {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #ff8f16;
        line-height: 22px;
    }
}

.prize-item-info {
    justify-content: center;
    margin: 12px 0 0px;
    .prize-item-info-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 6px 0;
        cursor: pointer;
        transition: background-color 0.3s ease;
        user-select: none;
        &:hover {
            background-color: #dddddd;
        }

        span {
            font-family: DINPro, DINPro;
            font-weight: 500;
            font-size: 20px;
            color: #3d3d3d;
            line-height: 20px;
            margin-bottom: 6px;
        }
        p {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            line-height: 18px;

            .anticon-right {
                margin-left: 2px;
                font-size: 6px;
                color: #999999;
                vertical-align: baseline;
            }
        }
    }
}

.winner-info-header {
    margin-bottom: 16px;

    .stats {
        margin-bottom: 16px;

        span {
            margin-right: 24px;
            color: rgba(0, 0, 0, 0.85);
        }
    }

    .searchArea {
        display: flex;
        align-items: center;
    }
}

.prize-list-name-warp {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.coupon {
    position: relative;
    width: 100%;
    background: url('https://stantic.ifengqun.com/new-fengqun/fq-ecmiddle-sys/icons/coupon-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }
    &-content {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        display: flex;
        &-nav {
            width: 100px;
            background: transparent;
            padding: 20px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }
        &-active {
            position: absolute;
            top: 0;
            left: 0;
            color: #fff;
            padding: 2px 8px;
            border-radius: 12px 0px 12px 0px;
            font-size: 12px;

            background: linear-gradient(90deg, #fb1e6b 5%, #fb1a70 42%, #fe3838 98%);
        }
        &-price {
            color: #ff4d4f;
            text-align: center;
            &-symbol {
                font-size: 14px;
            }
            &-num {
                font-size: 32px;
                font-weight: bold;
            }
            &-discount {
                font-size: 14px;
            }
        }
        &-desc {
            font-size: 12px;
            color: #fa2b19;
        }
        &-box {
            flex: 1;
            padding: 16px;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            &-container {
            }
            &-content {
                font-size: 16px;
                color: #333;
                margin-bottom: 8px;
                display: flex;
                align-items: center;
                gap: 8px;
                &-scope-container {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    margin-top: 6px;
                }

                &-desc {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 600;
                    font-size: 14px;
                    color: #333333;
                    line-height: 18px;
                }
                &-scope {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #999999;
                    line-height: 18px;
                }
            }
            &-pic {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                object-fit: cover;
            }
            &-footer {
                font-size: 12px;
                color: #999;
            }
        }
    }
}
.luck-bag-detail {
    display: flex;
    width: 440px;
    flex-direction: column;
    padding: 16px 12px;
    background: #fff;
    background: #f7f8f9;
    border-radius: 10px 10px 10px 10px;
}

.table-tabs {
    .ant-tabs-nav {
        &::before {
            display: none !important;
        }
    }
}
