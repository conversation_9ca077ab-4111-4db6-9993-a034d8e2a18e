/**
 * @Owners felix.cai
 * @Title 活动管理
 */
import { type RedPacket } from '@/api/interface/live/redpacket';
import { CodeEnum } from '@/api/interface/request';
import {
    deleteActiveRedPacketRain,
    getActiveRedPacketRainCount,
    getActiveRedPacketRainDetail,
    getActiveRedPacketRainList,
    updateActiveRedPacketRainOpenStatus,
} from '@/api/modules/live/console';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Modal, Popconfirm, Row, Space, Table, Tabs, Tag, Tooltip, message } from 'antd';
import { type ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { createContext, useEffect, useState } from 'react';

import RedPacketEditModal from './RedPacketEditModal';
import { ActionType, ActivityStatus, TAG_COLOR_MAP } from './constant';
import './index.scss';

type PropsType = {
    liveId: number | undefined;
};

type ActivityItem = RedPacket.Response.GetActiveRedPacketRainList['dataList'][0];
type ActivityDetail = RedPacket.Response.GetActiveRedPacketRainDetail;

const ACTIVITY_STATUS_OPTIONS = [
    { label: '全部', value: undefined, count: 0 },
    { label: '待开始', value: 1, count: 0 },
    { label: '进行中', value: 2, count: 0 },
    { label: '已结束', value: 3, count: 0 },
];

export const ACTIVITY_STATUS_MAP: {
    [key: number]: string;
} = {
    1: '待开始',
    2: '进行中',
    3: '已结束',
};

const ACTIVITY_THRESHOLD_MAP: {
    [key: number]: string;
} = {
    0: '无门槛',
    1: '已关注主播用户可领取',
    2: '仅分享可领取',
};

export const DataContext = createContext<{
    actionType?: ActionType;
}>({});

const RedPacketTable = (props: PropsType) => {
    const { liveId } = props;

    const [loading, setLoading] = useState(false);
    const [paginationState, setPaginationState] = useState<{
        current: number;
        pageSize: number;
        total: number;
        filterActivityStatus: number | undefined;
    }>({
        current: 1,
        pageSize: 10,
        total: 0,
        filterActivityStatus: undefined,
    });
    const [activityList, setActivityList] = useState<ActivityItem[]>([]);
    const [deleteModalVisible, setDeleteModalVisible] = useState(false);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [currentActivity, setCurrentActivity] = useState<ActivityDetail | null>(null);
    const [actionType, setActionType] = useState(-1);
    const [statusConfirmVisible, setStatusConfirmVisible] = useState(false);
    const [activityStatusOptions, setActivityStatusOptions] =
        useState<{ label: string; value: number | undefined; count: number }[]>(ACTIVITY_STATUS_OPTIONS);
    const [pendingStatusChange, setPendingStatusChange] = useState<{
        record?: ActivityItem;
    }>();

    /** 获取当前活动tab的数量 */
    const fetchActivityStatusCount = async () => {
        if (!liveId) return;
        const res = await getActiveRedPacketRainCount({
            foreignId: liveId,
        });
        if (res?.data) {
            const { count: allCount = 0, endedCount = 0, inProgressCount = 0, toBeginCount = 0 } = res?.data;
            const newActivityStatusOptions = [
                {
                    label: ACTIVITY_STATUS_OPTIONS[0].label,
                    value: ACTIVITY_STATUS_OPTIONS[0].value,
                    count: allCount,
                },
                {
                    label: ACTIVITY_STATUS_OPTIONS[1].label,
                    value: ACTIVITY_STATUS_OPTIONS[1].value,
                    count: toBeginCount,
                },
                {
                    label: ACTIVITY_STATUS_OPTIONS[2].label,
                    value: ACTIVITY_STATUS_OPTIONS[2].value,
                    count: inProgressCount,
                },
                {
                    label: ACTIVITY_STATUS_OPTIONS[3].label,
                    value: ACTIVITY_STATUS_OPTIONS[3].value,
                    count: endedCount,
                },
            ];
            setActivityStatusOptions(newActivityStatusOptions);
        }
    };

    // 获取活动列表
    const getActivityList = async () => {
        if (!liveId) return;

        setLoading(true);
        try {
            const res = await getActiveRedPacketRainList({
                condition: {
                    activityStatus: paginationState.filterActivityStatus,
                    foreignId: liveId,
                },
                order: '',
                sort: '',
                page: paginationState.current,
                rows: paginationState.pageSize,
            });
            if (res) {
                setActivityList(res.data?.dataList || []);
                setPaginationState(prev => ({ ...prev, total: res.data?.total || 0 }));
            }
        } catch (error) {
            console.error('Failed to fetch activity list:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchActivityDetail = async (id: number): Promise<ActivityDetail | undefined> => {
        setLoading(true);
        try {
            const res = await getActiveRedPacketRainDetail({ id });
            setLoading(false);
            return res.data;
        } catch (err) {
            message.error('获取活动详情失败');
            setLoading(false);
            return undefined;
        }
    };

    // 获取活动列表数据
    useEffect(() => {
        getActivityList();
        fetchActivityStatusCount();
    }, [liveId, paginationState.current, paginationState.pageSize, paginationState.filterActivityStatus]);

    // 活动列表过滤条件
    const handleFilterChange = (key: string) => {
        setPaginationState(prev => ({
            ...prev,
            total: 0,
            current: 1,
            filterActivityStatus: Number(key),
        }));
    };
    // 开关状态切换
    const handleStatusChange = async (checked: boolean, record: ActivityItem) => {
        if (!checked) {
            setStatusConfirmVisible(true);
            setPendingStatusChange({ record });
            return;
        }

        const matchItem = activityList.find(item => item.id === record.id);
        try {
            if (matchItem) {
                matchItem.openStatus = checked ? 1 : 0;
            }
            setActivityList([...activityList]);
            const res = await updateActiveRedPacketRainOpenStatus({
                id: record.id,
                openStatus: checked ? 1 : 0,
            });
            if (res.code !== CodeEnum.SUCCESS) {
                message.error(res.msg || '状态更新失败');
            }
            getActivityList();
            fetchActivityStatusCount();
        } catch (error) {
            if (matchItem) {
                matchItem.openStatus = matchItem?.openStatus ? 0 : 1;
                setActivityList([...activityList]);
            }
            console.error('Failed to update status:', error);
        }
    };
    // 确认关闭状态
    const handleStatusConfirm = async () => {
        if (pendingStatusChange?.record) {
            const record = pendingStatusChange.record;
            const matchItem = activityList.find(item => item.id === record.id);
            try {
                if (matchItem) {
                    matchItem.openStatus = 0;
                }
                setActivityList([...activityList]);
                const res = await updateActiveRedPacketRainOpenStatus({
                    id: record.id,
                    openStatus: 0,
                });
                if (res.code !== CodeEnum.SUCCESS) {
                    message.error(res.msg || '状态更新失败');
                }
                getActivityList();
                fetchActivityStatusCount();
            } catch (error) {
                if (matchItem) {
                    matchItem.openStatus = matchItem?.openStatus ? 0 : 1;
                    setActivityList([...activityList]);
                }
                console.error('Failed to update status:', error);
            }
        }
        setStatusConfirmVisible(false);
        setPendingStatusChange(undefined);
    };
    // 取消关闭状态
    const handleStatusCancel = () => {
        setStatusConfirmVisible(false);
        setPendingStatusChange(undefined);
    };
    // 删除当前活动
    const handleDeleteClick = (record: ActivityItem) => {
        setCurrentActivity(record);
        setDeleteModalVisible(true);
    };
    // 确认删除
    const handleDeleteConfirm = async () => {
        if (!currentActivity) return;

        const res = await deleteActiveRedPacketRain({
            id: currentActivity.id as number,
        });

        if (res.code !== CodeEnum.SUCCESS) {
            message.error(res.msg || '删除失败');
            return;
        }

        setDeleteModalVisible(false);
        getActivityList();
        fetchActivityStatusCount();
        message.success('删除成功');
    };

    const handleCopy = () => {
        setEditModalVisible(false);
        handleCopyActivity(currentActivity as ActivityItem);
    };

    const handleEdit = () => {
        setEditModalVisible(false);
        handleEditClick(currentActivity as ActivityItem);
    };

    const handleEndActivity = () => {
        handleStatusChange(false, currentActivity as ActivityItem);
    };

    const handleEditClick = async (record: ActivityItem) => {
        if (!record.id) return;
        // 获取活动详情
        setActionType(ActionType.Edit);
        const detail = await fetchActivityDetail(record.id);
        if (detail) setCurrentActivity(detail);
        setEditModalVisible(true);
    };

    const handleEditModalOk = () => {
        switch (actionType) {
            case ActionType.Add:
                message.success('添加成功');
                break;
            case ActionType.Edit:
                message.success('编辑成功');
                break;
            case ActionType.View:
                break;
            case ActionType.Copy:
                message.success('复制成功');
        }

        setEditModalVisible(false);
        setCurrentActivity(null);
        getActivityList();
        fetchActivityStatusCount();
    };

    const handleEditModalCancel = () => {
        switch (actionType) {
            case ActionType.Add:
                break;
            case ActionType.Edit:
                break;
            case ActionType.View:
                break;
            case ActionType.Copy:
                activityList.pop();
                setActivityList([...activityList]);
        }

        setEditModalVisible(false);
        setCurrentActivity(null);
    };

    const handleAddActivity = () => {
        setActionType(ActionType.Add);
        setEditModalVisible(true);
    };

    const handleCopyActivity = async (record: ActivityItem) => {
        if (!record.id) return;
        try {
            setActionType(ActionType.Copy);
            // 获取活动详情
            const detail = await fetchActivityDetail(record.id);

            // 创建新的活动数据，移除 id 字段
            const newActivity = {
                ...detail,
                id: undefined,
                activityName: `${detail?.activityName}_复制`,
                // activeLotteryPrizeList: detail?.activeLotteryPrizeList?.map(item => ({
                //     ...item,
                //     num: 0,
                // })),
            };

            // 更新活动列表
            setActivityList([...activityList, newActivity]);

            // 打开编辑弹窗
            setCurrentActivity(newActivity);
            setEditModalVisible(true);
        } catch (error) {
            console.error('复制活动失败:', error);
        }
    };

    const handleCheckActivity = async (record: ActivityItem) => {
        if (!record.id) return;
        // 获取活动详情
        const detail = await fetchActivityDetail(record.id);
        // const detail = MOCK_DATA.find(item => item.id === record.id);
        setActionType(ActionType.View);
        if (detail) setCurrentActivity(detail);
        setEditModalVisible(true);
    };

    const columns: ColumnsType<ActivityItem> = [
        {
            title: 'ID',
            align: 'left',
            render: (_text: unknown, _record: ActivityItem, index: number) =>
                index + 1 + (paginationState.current - 1) * paginationState.pageSize,
        },
        // {
        //     title: '活动编码',
        //     dataIndex: 'id',
        //     align: 'left',
        //     key: 'id',
        // },
        {
            title: '活动备注',
            dataIndex: 'activityName',
            key: 'activityName',
            align: 'left',
            width: 200,
            ellipsis: true,
            render: (activityName: string) => <Tooltip title={activityName}>{activityName}</Tooltip>,
        },
        {
            title: '活动门槛',
            align: 'left',
            key: 'activityThreshold',
            render: (_: unknown, record: ActivityItem) => ACTIVITY_THRESHOLD_MAP[record.activityThreshold as number],
        },
        {
            title: '活动时间',
            align: 'left',
            key: 'activityTime',
            width: 160,
            render: (_: unknown, record: ActivityItem) => (
                <span>
                    {dayjs(record.startTime).format('HH:mm')}开始 ~ {dayjs(record.endTime).format('HH:mm')}结束
                </span>
            ),
        },
        {
            title: '状态',
            align: 'left',
            key: 'activityStatus',
            render: (_: unknown, record: ActivityItem) =>
                record?.openStatus === 1 ? (
                    <Tag style={TAG_COLOR_MAP[record?.activityStatus as number] || {}}>
                        {record?.activityStatus === ActivityStatus.InProgress && (
                            <img
                                src='https://applet.ifengqun.com/fq-mall/prod/discover/discover-loading.gif'
                                style={{
                                    width: '12px',
                                    height: '12px',
                                    marginRight: '4px',
                                }}
                                alt=''
                            />
                        )}
                        {ACTIVITY_STATUS_MAP[record.activityStatus as number]}
                    </Tag>
                ) : (
                    <Tag color='#EEE' style={{ color: '#999' }}>
                        已结束
                    </Tag>
                ),
        },
        {
            title: '参与人数',
            align: 'left',
            key: 'partNum',
            dataIndex: 'partNum',
            render: (partNum: number) => `${partNum || 0}人已参与`,
        },
        {
            title: '奖品',
            align: 'left',
            key: 'prizeCount',
            width: 220,
            render: (_: unknown, _record: ActivityItem) => {
                const prizeListByNames = _record.activeLotteryPrizeList?.map(item => {
                    if (item?.prizeType === 1) {
                        return item.prizeInfoDTO?.prizeName;
                    }
                    if (item?.prizeType === 2) {
                        return item.couponBaseResDTO?.name;
                    }
                    return '';
                });
                return (
                    <Tooltip
                        title={
                            <div style={{ whiteSpace: 'pre-wrap' }}>
                                {prizeListByNames?.map((item, index) => (
                                    <p key={index} style={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis', overflow: 'hidden' }}>
                                        {item}
                                    </p>
                                ))}
                            </div>
                        }
                        overlayStyle={{ maxWidth: 240 }} // 设置最大宽度
                    >
                        {prizeListByNames?.map((item, index) => (
                            <p className='prize-list-name-warp' key={index}>
                                {item}
                            </p>
                        ))}
                    </Tooltip>
                );
            },
        },
        // {
        //     title: '活动状态',
        //     key: 'openStatus',
        //     render: (_: unknown, record: ActivityItem) => (
        //         <Switch
        //             checked={record.openStatus === 1}
        //             onChange={checked => handleStatusChange(checked, record)}
        //             disabled={record.openStatus === 1 && record.activityStatus === 3 && record.source !== 1}
        //         />
        //     ),
        // },
        {
            title: '操作',
            fixed: 'right',
            width: 152,
            key: 'action',
            align: 'center',
            render: (_: unknown, record: ActivityItem) => (
                <Space wrap direction='vertical'>
                    {record.activityStatus === ActivityStatus.Pending ? (
                        <>
                            <Button type='link' onClick={() => handleCheckActivity(record)}>
                                详情
                            </Button>
                            <Button type='link' onClick={() => handleEditClick(record)}>
                                编辑
                            </Button>
                            <Button type='link' onClick={() => handleCopyActivity(record)}>
                                复制创建
                            </Button>
                            <Button type='link' onClick={() => handleDeleteClick(record)}>
                                删除
                            </Button>
                        </>
                    ) : null}
                    {record.activityStatus === ActivityStatus.InProgress || record.activityStatus === ActivityStatus.Ended ? (
                        <>
                            <Button type='link' onClick={() => handleCheckActivity(record)}>
                                查看
                            </Button>
                            <Button type='link' onClick={() => handleCopyActivity(record)}>
                                复制创建
                            </Button>
                        </>
                    ) : null}
                    {record?.activityStatus === ActivityStatus.InProgress && (
                        <Popconfirm title='确定结束该活动吗？' onConfirm={() => handleStatusChange(false, record)}>
                            <Button type='link' danger disabled={record?.openStatus === 0}>
                                结束活动
                            </Button>
                        </Popconfirm>
                    )}
                </Space>
            ),
        },
    ];

    return (
        <DataContext.Provider value={{ actionType }}>
            <Row justify='space-between' style={{ position: 'relative' }}>
                <Col style={{ flex: 1 }}>
                    <Tabs onChange={handleFilterChange} className='table-tabs'>
                        {activityStatusOptions.map(option => (
                            <Tabs.TabPane key={option.value} tab={`${option.label}(${option?.count})`} />
                        ))}
                    </Tabs>
                </Col>
                <Col style={{ position: 'absolute', right: 0 }}>
                    <Button type='primary' icon={<PlusOutlined />} onClick={handleAddActivity}>
                        新增红包雨
                    </Button>
                </Col>
            </Row>
            <Table
                columns={columns}
                dataSource={activityList.map((item, index) => ({ ...item, key: index }))}
                loading={loading}
                rowKey='key'
                pagination={{
                    current: paginationState.current,
                    pageSize: paginationState.pageSize,
                    total: paginationState.total,
                    onChange: (newPage, newPageSize) => {
                        setPaginationState(prev => ({ ...prev, current: newPage, pageSize: newPageSize }));
                    },
                    showSizeChanger: true,
                    showTotal: total => `共 ${total} 条`,
                }}
                rowClassName={record => (record.activityStatus === ActivityStatus.InProgress ? 'highlight-row' : '')}
            />
            <Modal
                title='确认删除'
                open={deleteModalVisible}
                onOk={handleDeleteConfirm}
                onCancel={() => setDeleteModalVisible(false)}
            >
                <p>确定要删除该活动吗？</p>
            </Modal>
            <Modal
                title='活动状态确认'
                open={statusConfirmVisible}
                onOk={handleStatusConfirm}
                onCancel={handleStatusCancel}
                okText='我确认'
                cancelText='取消'
            >
                <p style={{ color: '#FF4D4F' }}>请确认活动是否要关闭，关闭后，正在进行中的活动也将在直播间结束后消失</p>
            </Modal>
            <RedPacketEditModal
                liveId={liveId}
                actionType={actionType}
                activityDetail={currentActivity}
                open={editModalVisible}
                onOk={handleEditModalOk}
                onCancel={handleEditModalCancel}
                onDelete={handleDeleteConfirm}
                onCopy={handleCopy}
                onEdit={handleEdit}
                onEndActivity={handleEndActivity}
            />
        </DataContext.Provider>
    );
};

export default RedPacketTable;
