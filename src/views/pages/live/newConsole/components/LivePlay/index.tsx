/**
 * @Owners wyy
 * @Title 直播营销工具
 */
import { hasLiveActive } from '@/api/modules/live/console';
import { CloseOutlined } from '@ant-design/icons';
import { Avatar, Badge, Drawer } from 'antd';
import React, { useEffect, useState } from 'react';

import LuckyBagTable from './LuckBagTable';
import RedPacketTable from './RedPacketTable';
import './index.scss';

interface LivePlayTool {
    title: string;
    key: string;
    icon: string;
    drawerWidth?: number | string; // 抽屉宽度
    drawerTitle?: string; // 抽屉标题
    isActive?: boolean; // 是否有进行中的活动
}

/** 福袋默认图标 */
const LuckyBagDefaultIcon = 'https://stantic.ifengqun.com/new-fengqun/fq-shop/icons/lucky-bag.png';
/** 红包雨默认图标 */
const RedPacketDefaultIcon = 'https://stantic.ifengqun.com/new-fengqun/fq-shop/icons/red-packet.png';

const LivePlayTool: LivePlayTool[] = [
    {
        title: '直播福袋',
        key: 'lucky-bag',
        drawerWidth: '1100px',
        icon: LuckyBagDefaultIcon,
        drawerTitle: '直播间福袋',
        isActive: false,
    },
    {
        title: '直播间红包雨',
        key: 'red-packet',
        drawerWidth: '1100px',
        icon: RedPacketDefaultIcon,
        drawerTitle: '直播间红包雨',
        isActive: false,
    },
];

const LivePlay: React.FC<{ liveId: number }> = ({ liveId }) => {
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [livePlayTools, setLivePlayTools] = useState<LivePlayTool[]>(LivePlayTool);
    const [currentTool, setCurrentTool] = useState<LivePlayTool | null>(null);

    const handleToolClick = (tool: LivePlayTool) => {
        setCurrentTool(tool);
        setDrawerVisible(true);
    };

    const handleDrawerClose = () => {
        setDrawerVisible(false);
        setCurrentTool(null);
    };

    useEffect(() => {
        fetchHasLiveActive();
    }, [liveId]);

    /** 获取当前是否有进行中的活动 */
    const fetchHasLiveActive = async () => {
        const { data } = await hasLiveActive({ liveId });

        setLivePlayTools(prev =>
            prev.map(tool => {
                if (data?.hasLottery === 1 && tool.key === 'red-packet') {
                    return {
                        ...tool,
                        isActive: true,
                    };
                }
                if (data?.hasLuckyBag === 1 && tool.key === 'lucky-bag') {
                    return {
                        ...tool,
                        isActive: true,
                    };
                }
                return tool;
            })
        );
    };

    const renderDrawerContent = () => {
        if (!currentTool) return null;

        switch (currentTool.key) {
            case 'lucky-bag':
                return <LuckyBagTable liveId={liveId} />;
            case 'red-packet':
                return <RedPacketTable liveId={liveId} />;
            default:
                return null;
        }
    };

    return (
        <div className='live-console-bottom-left-collapse-item'>
            {livePlayTools.map(tool => (
                <div
                    key={tool.key}
                    className='live-console-bottom-left-collapse-avatar'
                    onClick={() => handleToolClick(tool)}
                    style={{ cursor: 'pointer' }}
                >
                    <Badge count={tool.isActive ? '进行中' : 0} offset={[5, -1]} style={{ borderRadius: '8px 8px 8px 1px' }}>
                        <Avatar
                            src={tool.icon}
                            style={{
                                verticalAlign: 'middle',
                                marginBottom: 4,
                                width: 40,
                                height: 40,
                            }}
                            size='large'
                        />
                    </Badge>
                    <p>{tool.title}</p>
                </div>
            ))}
            <Drawer
                title={currentTool?.drawerTitle || currentTool?.title}
                placement='right'
                onClose={handleDrawerClose}
                open={drawerVisible}
                styles={{ body: { padding: '16px 24px' } }}
                width={currentTool?.drawerWidth || 500}
                closeIcon={null}
                extra={<CloseOutlined onClick={handleDrawerClose} style={{ fontSize: 16, cursor: 'pointer' }} />}
            >
                {renderDrawerContent()}
            </Drawer>
        </div>
    );
};

export default LivePlay;
