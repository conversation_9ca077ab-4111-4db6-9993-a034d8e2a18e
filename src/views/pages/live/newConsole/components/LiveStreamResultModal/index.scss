.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5); // 半透明背景
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
        background: linear-gradient(180deg, #e0fff0 0%, #ffffff 100%);
        padding: 40px;
        border-radius: 6px;
        width: 1000px;
        text-align: center;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
        position: relative;
    }

    .modal-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .modal-time {
        font-size: 14px;
        color: #666;
        margin-bottom: 16px;
    }

    .modal-stats {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 16px;
        background: #fff;
        border-radius: 8px;
    }

    .stat-item {
        flex: 1;
        min-width: 100px;
        text-align: center;
        margin-bottom: 12px;
    }

    .stat-label {
        font-size: 14px;
        color: #666;
    }

    .stat-value {
        font-size: 20px;
        font-weight: bold;
        color: #333;
    }

    .close-button {
        width: 48px;
        height: 48px;
        position: absolute;
        bottom: -72px;
        right: calc(50% - 24px);
        border: none;
        background: transparent;
        // font-size: 20px;
        cursor: pointer;
        // color: #888;
        // transition: color 0.2s;

        &:hover {
            color: #333;
        }
    }

    .ant-statistic-title {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 18px;
        color: #666666;
        line-height: 23px;
        margin-bottom: 8px;
    }
    .ant-statistic-content-value {
        font-family: DIN-BOLD;
        font-weight: 700;
        font-size: 32px;
        color: #333333;
        line-height: 40px;
    }
}
