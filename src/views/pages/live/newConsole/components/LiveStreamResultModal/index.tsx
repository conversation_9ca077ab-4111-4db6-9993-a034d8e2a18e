/**
 * @Owners lzy
 * @Title 直播结束弹窗
 */

import { type Console } from '@/api/interface/live/console';
import { overviewData } from '@/api/modules/live/console';
import closeIcon from '@/assets/images/closeIcon.png';
import { formatDuration, uNumber } from '@/utils/uNumber';
import { cLive } from '@consts';
import { Statistic } from 'antd';
import React, { useEffect, useState } from 'react';

import './index.scss';

type Props = {
    liveId: number;
};

const LiveStreamResultModal: React.FC<Props> = ({ liveId }) => {
    const [isOpen, setIsOpen] = useState(true); // 控制弹框显示/隐藏
    const [data, setData] = useState<Console.Response.LiveRuntimeBigStatisticsDTO>();

    useEffect(() => {
        getLiveInfo();
    }, []);
    const getLiveInfo = async () => {
        // const newTime = dayjs().format('YYYY-MM-DD HH:mm');
        // setTime(newTime);
        overviewData({ liveId }).then(res => {
            setData(res.data);
        });
    };

    return isOpen ? (
        <div className='modal-overlay' onClick={() => setIsOpen(false)}>
            <div className='modal-content' onClick={e => e.stopPropagation()}>
                <h2 className='modal-title'>直播已结束</h2>
                <p className='modal-time'>
                    {/* 时间返回的单位为毫秒 需要转成秒 */}
                    {data?.liveStartTime} 直播时长：{formatDuration((data?.totalLiveTimes || 0) / 1000)}
                </p>

                <div className='modal-stats'>
                    <Statistic
                        title='直播间成交金额'
                        value={
                            (data?.liveType === cLive.LiveTypeMap.PUBLIC ? data?.fullOrderTotalGmv : data?.pureOrderTotalGmv) ||
                            '-'
                        }
                        suffix={<span style={{ fontSize: '14px' }}>元</span>}
                    />
                    <Statistic
                        title='成交件数'
                        value={
                            (data?.liveType === cLive.LiveTypeMap.PUBLIC
                                ? data?.fullOrderSkuNumbers
                                : data?.pureOrderSkuNumbers) || '-'
                        }
                    />
                    <Statistic
                        title='成交会员数'
                        value={
                            (data?.liveType === cLive.LiveTypeMap.PUBLIC ? data?.fullOrderDerUsers : data?.pureOrderDerUsers) ||
                            '-'
                        }
                    />
                    <Statistic
                        title='成交人数'
                        value={(data?.liveType === cLive.LiveTypeMap.PUBLIC ? data?.fullOrderUsers : data?.pureOrderUsers) || '-'}
                    />
                    <Statistic
                        title='用户单价'
                        value={
                            (data?.liveType === cLive.LiveTypeMap.PUBLIC
                                ? uNumber.centToYuan(data?.fullUnitPrice || 0)
                                : uNumber.centToYuan(data?.pureUnitPrice || 0)) || '-'
                        }
                        precision={2}
                        suffix={<span style={{ fontSize: '14px' }}>元</span>}
                    />
                </div>

                <img className='close-button' src={closeIcon} onClick={() => setIsOpen(false)} />
            </div>
        </div>
    ) : null;
};

export default LiveStreamResultModal;
