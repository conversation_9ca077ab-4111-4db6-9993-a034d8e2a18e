/**
 * @Owners ljh
 * @Title 直播上下文
 */
import { type Console } from '@/api/interface/live/console';
import { cLive } from '@consts';
import { type ChatSDK } from '@tencentcloud/chat';
import { type MessageInstance } from 'antd/es/message/interface';
import { createContext, type Dispatch, type MutableRefObject, type SetStateAction } from 'react';

export interface LiveContextProps {
    liveToast?: MessageInstance;
    /** 当前tabkey */
    tabActiveKey: string;
    /** 聊天 */
    chat?: MutableRefObject<ChatSDK | null>;
    /** 聊天是否初始化 */
    chatInited: boolean;
    /** 直播id */
    liveId?: number;
    /** 直播详情 */
    liveDetail?: Console.Response.LiveDetailInfo;
    /** 设置直播详情 */
    setLiveDetail?: Dispatch<SetStateAction<Console.Response.LiveDetailInfo | undefined>>;
    /** 获取直播详情 */
    getLiveDetail?(): Promise<Console.Response.LiveDetailInfo | undefined>;
}

export const LiveContext = createContext<LiveContextProps>({
    tabActiveKey: cLive.ConsoleInteractiveTabs.Live,
    chatInited: false,
});
