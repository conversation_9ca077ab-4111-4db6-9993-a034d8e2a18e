/**
 * @Owners zp
 * @Title 角色管理 - 编辑
 */
import { CodeEnum } from '@/api/interface/request';
import { type Roles, type SysMenu } from '@/api/interface/system';
import { getMenuList } from '@/api/modules/system/menu';
import { addRoles, updateRoles } from '@/api/modules/system/roles';
import { ModalForm, ProFormSwitch, ProFormText, ProFormTextArea, ProFormTreeSelect } from '@ant-design/pro-components';
import { createMenuNormalData, getCheckedMenuId } from '@utils';
import { Form, TreeSelect, message, type TreeSelectProps } from 'antd';
import { useEffect, useMemo, useState } from 'react';

type IAddRoles = Roles.IAddRoles;

// type ITreeSelectValue = { label: string; value: number };
type IAddRolesForm = Omit<IAddRoles, 'menuIds'> & {
    menuIds: number[];
};

interface Props {
    show?: boolean;
    rowData?: Roles.RolesItem;
    onHide(needRefresh?: boolean): void;
}

const EditModal: React.FC<Props> = (props: Props) => {
    const { onHide, show, rowData } = props;
    const [menuList, setMenuList] = useState<TreeSelectProps['treeData']>();
    const [form] = Form.useForm<IAddRolesForm>();
    const btnText = `${rowData ? '修改' : '新增'}`;

    // 获取真正选中的id，组件开启父子关系关联后，选中父级就会默认全部选中子级，
    // 但是在选中个别子级的情况下treeSelect是不需要pid的，所以这里要找出需要过滤的pid
    const realCheckIds = useMemo(() => {
        if (!rowData?.menuIds || !menuList) return [];
        const checkedIds = rowData?.menuIds.split(',').map(Number);
        const hasEmptyChildPIds: number[] = [];
        // 递归children是否都在checkedIds中
        const everyChildHasChecked = (list: typeof menuList = []): boolean =>
            list?.every(child => checkedIds.includes(child.id) && (child.children ? everyChildHasChecked(child.children) : true));
        for (const item of menuList) {
            // 比较父级
            if (checkedIds.includes(item.id)) {
                // 比较子级
                if (item.children && !everyChildHasChecked(item.children)) {
                    hasEmptyChildPIds.push(item.id);
                }
            }
        }
        return hasEmptyChildPIds;
    }, [rowData?.menuIds, menuList]);

    useEffect(() => {
        if (show) {
            getMenuData();
        }
    }, [show]);

    useEffect(() => {
        if (rowData && menuList) {
            // 回显数据
            const { menuIds, name, status, desc } = rowData;
            const ids = menuIds
                ? menuIds
                      .split(',')
                      .map(Number)
                      .filter(id => getAllMenuId(menuList).includes(id) && !realCheckIds.includes(id))
                : [];
            form.setFieldsValue({
                menuIds: ids,
                name,
                status: Number(status),
                desc,
            });
        }
    }, [rowData, menuList]);

    const getAllMenuId = (menus: typeof menuList) => {
        const idList: number[] = [];
        const loop = (menusArr: typeof menuList) => {
            if (!menusArr) return [];
            for (const item of menusArr) {
                idList.push(item.id);
                if (item.children) loop(item.children);
            }
        };
        loop(menus);
        return idList;
    };

    const getMenuData = async () => {
        const { data = [] } = await getMenuList<SysMenu.MenuItem[]>();
        setMenuList(createMenuNormalData(data, false));
    };

    // 提交事件
    const onFinish = async (values: IAddRolesForm) => {
        let res = undefined;
        // 特殊处理：没有全部选中子级，组件是不会返回pid的，但是要带上父级id
        // 如果不带pid，getInfo接口，后端不会返回父级菜单，也就不会展示子级，
        // 引发另一个问题：回显的时候需要单独处理(见realCheckIds方法)，如果子级没有全部选中，回显的时候要过滤掉pid，因为treeSelect组件认为只要有pid就默认子级全部选中
        const mergeIds = [...values.menuIds, ...getCheckedMenuId(values.menuIds, menuList as SysMenu.MenuItem[])];
        const strMenuIds = mergeIds.join(',');
        const params = { ...values, status: Number(values.status), menuIds: strMenuIds };
        if (rowData) {
            res = await updateRoles({ ...params, id: rowData.id });
        } else {
            res = await addRoles({ ...params });
        }
        if (res?.code !== CodeEnum.SUCCESS) {
            message.error(res.msg);
            return false;
        }
        message.success(`${btnText}成功！`);
        onHide(true);
        return true;
    };

    const onOpenChange = (v: boolean) => {
        if (!v) {
            onHide();
            setMenuList(undefined);
        }
    };

    const treeSelectProps: TreeSelectProps = {
        treeData: menuList,
        allowClear: true,
        autoClearSearchValue: true,
        treeCheckable: true,
        treeLine: true,
        showSearch: false,
        // checkable 状态下节点选择完全受控（父子节点选中状态不再关联），会使得 labelInValue 强制为 true
        // treeCheckStrictly: true,
        // 指定搜索过滤的属性
        treeNodeFilterProp: 'name',
        // 子级全部选中情况下，只显示父级
        showCheckedStrategy: TreeSelect.SHOW_ALL,
        style: {
            width: '100%',
        },
        fieldNames: {
            label: 'name',
            value: 'id',
            children: 'children',
        },
    };

    const formItemLayout = {
        labelCol: { span: 7 },
        wrapperCol: { span: 14 },
    };

    return (
        <ModalForm<IAddRolesForm>
            title={`${btnText}角色`}
            open={show}
            onFinish={onFinish}
            form={form}
            autoFocusFirstInput
            layout='horizontal'
            width={700}
            {...formItemLayout}
            onOpenChange={onOpenChange}
            modalProps={{ destroyOnClose: true }}
            submitter={{
                // 配置按钮文本
                searchConfig: {
                    resetText: '取消',
                    submitText: btnText,
                },
            }}
            initialValues={{ status: 1 }}
        >
            <ProFormTreeSelect
                // request={async () => menuList as unknown as Promise<RequestOptionsType[]>}
                name='menuIds'
                label='选择菜单'
                fieldProps={treeSelectProps}
                placeholder='请选择菜单'
                rules={[{ required: true }]}
            />
            <ProFormText
                name='name'
                label='角色名称'
                tooltip=''
                placeholder='请输入角色名称'
                fieldProps={{ maxLength: 8 }}
                rules={[{ required: true }]}
            />
            <ProFormTextArea name='desc' fieldProps={{ maxLength: 30 }} label='角色描述' placeholder='请输入角色描述' />
            <ProFormSwitch name='status' label='是否启用' />
        </ModalForm>
    );
};

export default EditModal;
