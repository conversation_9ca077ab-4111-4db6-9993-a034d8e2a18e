/**
 * @Owners zp
 * @Title 角色管理
 */
import { CodeEnum } from '@/api/interface/request';
import { type Roles } from '@/api/interface/system';
import { getRolesList, updateRoles } from '@/api/modules/system/roles';
import { Button, Col, Popconfirm, Row, Space, Switch, Table, message } from 'antd';
import { type ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';

import EditModal from './components/EditModal';
import createColumnsConfig from './createColumnsConfig';

type RolesItem = Roles.RolesItem;
const RolesManagement = () => {
    const [rolesList, setRolesList] = useState<RolesItem[]>();
    const [showEditModal, setShowEditModal] = useState(false);
    const [curRow, setCurRow] = useState<Roles.RolesItem>();

    useEffect(() => {
        initData();
    }, []);

    // 初始化列表数据
    const initData = async () => {
        const { data = [] } = await getRolesList<RolesItem[]>();
        setRolesList(data);
    };

    // 编辑
    const onRowEdit = (row: Roles.RolesItem) => {
        setCurRow({ ...row });
        setShowEditModal(true);
    };

    // 新增
    const onEditModalAdd = () => {
        setShowEditModal(true);
    };

    // 隐藏弹窗
    const onEditModalHide = (needRefresh: boolean) => {
        setShowEditModal(false);
        setCurRow(undefined);
        if (needRefresh) initData();
    };

    // 修改菜单状态
    const onChangeRow = async (row: Roles.RolesItem, key: 'isDelete' | 'status', tip: string) => {
        const res = await updateRoles({ ...row, [key]: Number(!row[key]) });
        if (res?.code !== CodeEnum.SUCCESS) {
            message.error(res.msg);
            return;
        }
        message.success(tip);
        // todo 是否需要刷新页面
        // if (key === 'status') {
        //     location.reload();
        // }
        initData();
    };

    // 修改菜单状态
    const columns: ColumnsType<RolesItem> = createColumnsConfig([
        {
            title: '启用',
            dataIndex: 'status',
            key: 'status',
            render: (v, row) =>
                row.name !== 'admin' ? <Switch checked={v} onClick={() => onChangeRow(row, 'status', '切换成功！')} /> : null,
        },
        {
            title: '操作',
            render: (_v: unknown, row: Roles.RolesItem) =>
                row.name !== 'admin' ? (
                    <>
                        <Button type='link' onClick={() => onRowEdit(row)}>
                            编辑
                        </Button>
                        <Popconfirm
                            title='警告'
                            description={`确定删除角色 ${row.name} 吗？`}
                            okText='确定'
                            cancelText='取消'
                            onConfirm={() => onChangeRow(row, 'isDelete', '删除成功！')}
                        >
                            <Button type='link'>删除</Button>
                        </Popconfirm>
                    </>
                ) : null,
        },
    ]);

    return (
        <div className='card'>
            <EditModal show={showEditModal} onHide={onEditModalHide} rowData={curRow} />
            <Row justify='end' style={{ marginBottom: '16px' }}>
                <Col xs={{ span: 6 }} lg={{ span: 2 }}>
                    <Button type='primary' onClick={onEditModalAdd}>
                        新增
                    </Button>
                </Col>
            </Row>
            <Space direction='horizontal' align='end' style={{ marginBottom: 16 }} />
            <Table rowKey='id' columns={columns} dataSource={rolesList} />
        </div>
    );
};
export default RolesManagement;
