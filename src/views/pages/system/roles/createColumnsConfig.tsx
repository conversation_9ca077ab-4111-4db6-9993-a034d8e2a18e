/**
 * @Owners zp
 * @Title 角色管理 - 列配置
 */

import { type Roles } from '@/api/interface/system';
import { type ColumnsType } from 'antd/es/table';

// 列配置
const createColumnsConfig = (config: ColumnsType<Roles.RolesItem>): ColumnsType<Roles.RolesItem> => [
    {
        title: '名称',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '更新人',
        dataIndex: 'updateBy',
        key: 'updateBy',
    },
    {
        title: '创建人',
        dataIndex: 'createBy',
        key: 'createBy',
    },
    {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        key: 'gmtCreate',
    },
    {
        title: '更新时间',
        dataIndex: 'gmtModified',
        key: 'gmtModified',
    },
    ...config,
];

export default createColumnsConfig;
