/**
 * @Owners zp
 * @Title 菜单管理 - 编辑
 */
import { CodeEnum } from '@/api/interface/request';
import { type SysMenu } from '@/api/interface/system/SysMenu';
import { addMenu, updateMenu } from '@/api/modules/system/menu';
import IconSelect from '@/views/components/common/IconsSelect';
import { ModalForm, ProFormDigit, ProFormText, ProFormTreeSelect } from '@ant-design/pro-components';
import { getIconsOption } from '@utils';
import { Form, message, type TreeSelectProps } from 'antd';
import { type ReactNode, useEffect, useMemo } from 'react';

import { type RowDataType } from '../index';

type MenuItem = SysMenu.MenuItem;

type MenuForm = Omit<MenuItem, 'icon'> & { icon: ReactNode | string };
interface Props {
    menuList?: RowDataType[];
    show?: boolean;
    rowData?: RowDataType;
    onHide(needRefresh?: boolean): void;
}

const EditModal: React.FC<Props> = (props: Props) => {
    const { menuList, onHide, show, rowData } = props;
    const [form] = Form.useForm<MenuForm>();
    const btnText = `${rowData ? '修改' : '新增'}`;
    const iconsOption = useMemo(getIconsOption, []);

    useEffect(() => {
        if (rowData) {
            // 回显数据
            const { parentId, id, name, iconStr, sort, path, perssion } = rowData;
            form.setFieldsValue({
                parentId: !parentId ? '' : String(parentId),
                id: String(id),
                name,
                icon: iconStr?.toLowerCase(),
                sort,
                path,
                perssion,
            });
        }
    }, [rowData]);

    const createTreeData = (list: RowDataType[]): TreeSelectProps['treeData'] =>
        list.map(item => ({
            title: item.name,
            value: item.id,
            key: item.id,
            disabled: item.path === '/system/menu', // 禁止操作菜单管理，以免不能管理菜单
            children: item.children ? createTreeData(item.children) : [],
        })) as TreeSelectProps['treeData'];

    const realTreeData = useMemo(() => (menuList ? createTreeData(menuList) : []), [menuList]);

    const formItemLayout = {
        labelCol: { span: 7 },
        wrapperCol: { span: 14 },
    };

    // 提交事件
    const onFinish = async (values: MenuForm) => {
        let res = undefined;
        const submitData = {
            ...values,
            parentId: values.parentId || '',
            permission: '',
            icon: iconsOption.find(i => i.value === values.icon)?.realValue,
        };
        if (rowData) {
            res = await updateMenu({ ...submitData, id: String(rowData.id) });
        } else {
            res = await addMenu({ ...submitData });
        }
        if (res?.code !== CodeEnum.SUCCESS) {
            message.error(res.msg);
            return false;
        }
        message.success(`${btnText}成功！`);
        onHide(true);
        return true;
    };

    const onOpenChange = (v: boolean) => {
        !v && onHide();
    };

    const treeSelectProps: TreeSelectProps = {
        treeData: realTreeData,
        allowClear: true,
        treeLine: true,
        style: {
            width: '100%',
        },
    };

    return (
        <ModalForm<MenuForm>
            title={`${btnText}菜单`}
            open={show}
            onFinish={onFinish}
            form={form}
            autoFocusFirstInput
            layout='horizontal'
            width={700}
            {...formItemLayout}
            onOpenChange={onOpenChange}
            modalProps={{ destroyOnClose: true }}
            submitter={{
                // 配置按钮文本
                searchConfig: {
                    resetText: '取消',
                    submitText: btnText,
                },
            }}
        >
            <ProFormTreeSelect name='parentId' label='上级菜单' fieldProps={treeSelectProps} placeholder='请选择上级菜单' />
            <ProFormText
                name='name'
                label='菜单名称'
                tooltip=''
                placeholder='请输入菜单名称'
                fieldProps={{ maxLength: 20 }}
                rules={[{ required: true }]}
            />
            <ProFormText name='path' label='菜单路由' placeholder='请输入菜单路由' rules={[{ required: true }]} />
            <ProFormDigit name='sort' label='排序' placeholder='请输入排序' rules={[{ required: true }]} />
            <ProFormText name='perssion' label='权限字符' placeholder='请输入权限字符' rules={[{ required: false }]} />
            <IconSelect name='icon' label='icon' />
        </ModalForm>
    );
};

export default EditModal;
