/**
 * @Owners zp
 * @Title 菜单管理
 */
import { CodeEnum } from '@/api/interface/request';
import { type SysMenu } from '@/api/interface/system';
import { getMenuList, updateMenu } from '@/api/modules/system/menu';
import * as Icons from '@ant-design/icons';
import { Button, Col, Popconfirm, Row, Switch, Table, Tooltip, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { ExpandableConfig } from 'antd/es/table/interface';
import React, { type ReactNode, useEffect, useState } from 'react';

import { ExpandAnimation, ExpandIconBlock, MenuListContainer } from './Style';
import EditModal from './components/EditModal';

type MenuItem = SysMenu.MenuItem;
export interface RowDataType {
    key: number;
    name: string;
    sort: number;
    path: string;
    id?: number;
    perssion?: string;
    parentId?: string;
    icon?: ReactNode | null;
    iconStr?: keyof typeof Icons;
    children?: RowDataType[];
}

const MenuManagement: React.FC = () => {
    const [dataList, setDataList] = useState<RowDataType[]>();
    const [showEditModal, setShowEditModal] = useState(false);
    const [curRow, setCurRow] = useState<RowDataType>();

    useEffect(() => {
        initData();
    }, []);

    const initData = async () => {
        const { data = [] } = await getMenuList<MenuItem[]>();
        setDataList(transformToTableData(data));
    };

    // 转换成table数据格式
    const transformToTableData = (d: MenuItem[]): RowDataType[] => {
        const tableData = d.map(item => ({
            ...item,
            key: Number(item.id),
            id: Number(item.id),
            iconStr: item.icon,
            icon: item.icon && Icons[item.icon] ? React.createElement(Icons[item.icon] as React.FC) : null,
            children: item.chidrenMenuList ? transformToTableData(item.chidrenMenuList) : undefined,
        }));
        return tableData;
    };

    // 展开行配置
    const expandable: ExpandableConfig<RowDataType> = {
        expandIcon: ({ expanded = false, onExpand, record }) =>
            record.children?.length ? (
                <ExpandAnimation expanded={expanded ? 1 : 0} onClick={e => onExpand(record, e)} />
            ) : (
                <ExpandIconBlock />
            ),
    };

    // 新增事件
    const onEditModalAdd = () => {
        setShowEditModal(true);
    };

    // 关闭编辑弹窗
    const onEditModalHide = (needRefresh: boolean) => {
        setShowEditModal(false);
        setCurRow(undefined);
        if (!needRefresh) return;
        initData();
    };

    // 修改行数据
    const onEditRow = (row: RowDataType) => {
        setCurRow(row);
        setShowEditModal(true);
    };

    // 修改菜单状态
    const onChangeRow = async (row: MenuItem, key: 'isDelete' | 'status', tip: string) => {
        delete row.chidrenMenuList;
        delete row.children;
        delete row.icon;
        const res = await updateMenu({ ...row, [key]: Number(!row[key]) });
        if (res?.code !== CodeEnum.SUCCESS) {
            message.error(res.msg);
            return;
        }
        message.success(tip);
        // todo 是否需要刷新页面
        // if (key === 'status') {
        //     location.reload();
        // }
        initData();
    };

    const isSystem = (id?: number) => id === 1;

    const columns: ColumnsType<RowDataType> = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '图标',
            dataIndex: 'icon',
            key: 'icon',
        },
        {
            title: '路由',
            dataIndex: 'path',
            key: 'path',
            width: 200,
            ellipsis: {
                showTitle: true,
            },
            render: path => (
                <Tooltip placement='topLeft' title={path}>
                    {path}
                </Tooltip>
            ),
        },
        {
            title: '排序',
            dataIndex: 'sort',
            key: 'sort',
        },
        {
            title: '权限字符',
            dataIndex: 'perssion',
            key: 'perssion',
        },
        {
            title: '启用',
            dataIndex: 'status',
            key: 'status',
            render: (v, row) =>
                isSystem(row.id) ? null : (
                    <Switch checked={v} onChange={() => onChangeRow(row as unknown as MenuItem, 'status', '切换成功!')} />
                ),
        },
        {
            title: '操作',
            render: (_v, row) =>
                isSystem(row.id) ? null : (
                    <>
                        <Button type='link' onClick={() => onEditRow(row)}>
                            编辑
                        </Button>
                        <Popconfirm
                            title='警告'
                            description={`确定删除此菜单${row.children ? '及其以下的子菜单' : ''}吗？`}
                            okText='确定'
                            cancelText='取消'
                            onConfirm={() => onChangeRow(row as unknown as MenuItem, 'isDelete', '删除成功！')}
                        >
                            <Button type='link'>删除</Button>
                        </Popconfirm>
                    </>
                ),
        },
    ];

    return (
        <MenuListContainer className='card'>
            <EditModal show={showEditModal} onHide={onEditModalHide} menuList={dataList} rowData={curRow} />
            <Row justify='end' className='mb16'>
                <Col xs={{ span: 6 }} lg={{ span: 2 }}>
                    <Button type='primary' onClick={onEditModalAdd}>
                        新增
                    </Button>
                </Col>
            </Row>
            {dataList?.length ? <Table rowKey='key' columns={columns} dataSource={dataList} expandable={expandable} /> : null}
        </MenuListContainer>
    );
};
export default MenuManagement;
