/**
 * @Owners zp
 * @Title 菜单样式
 */
import * as Icons from '@ant-design/icons';
import styled from 'styled-components';

const ExpandIconBlock = styled.div`
    width: 17px;
    height: 10px;
    display: inline-block;
`;

// 展开动画
const ExpandAnimation = styled(Icons.RightOutlined)<{ expanded: 0 | 1 }>`
    width: 12px;
    transform: ${props => (props.expanded ? 'rotate(90deg)' : 'rotate(0)')};
    margin-right: 5px;
    transition: all 0.3s;
`;

const MenuListContainer = styled.div`
    .ant-table-wrapper {
        .ant-table-thead {
            th {
                &:not(:first-child) {
                    text-align: center !important;
                }
            }
        }
        .ant-table-tbody {
            td {
                &:not(:first-child) {
                    text-align: center !important;
                }
            }
        }
    }
    .mb16 {
        margin-block-end: 16px;
    }
`;

export { ExpandIconBlock, ExpandAnimation, MenuListContainer };
