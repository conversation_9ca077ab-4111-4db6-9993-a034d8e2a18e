/**
 * @Owners mzh
 * @Title 近期直播预告
 */
import { type LivePreview } from '@/api/interface/livePreview';
import { getPastLivePage } from '@/api/modules/livePreview';
import M3U8Player from '@/views/components/M3U8Player';
import { DownloadOutlined } from '@ant-design/icons';
import { ProTable, type ProColumns } from '@ant-design/pro-components';
import { uString } from '@utils';
import { Button, Card, Col, Image, Row } from 'antd';
import { memo } from 'react';
import { useNavigate } from 'react-router-dom';

type DataItem = LivePreview.Response.getLiveList['dataList'][number];
type ParamsItem = LivePreview.Params.Condition;

const PreviousLiveDetail = memo(() => {
    const navigate = useNavigate();

    const goPage = (row: DataItem) => {
        navigate(`/live/newConsole?liveId=${row.liveId}`);
    };

    const columns: ProColumns<DataItem>[] = [
        {
            title: '直播间信息',
            dataIndex: 'roomCode',
            width: 270,
            renderText: (_, row: DataItem) => (
                <Row style={{ flexWrap: 'nowrap' }} align={'middle'}>
                    <Col>
                        <Image width={80} src={row.coverUrl} />
                        {row.liveType === 1 && (
                            <div
                                style={{
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    background: '#FFFBDC',
                                    padding: '0 4px',
                                    borderRadius: '4px',
                                }}
                            >
                                公开
                            </div>
                        )}
                        {row.liveType === 2 && (
                            <div
                                style={{
                                    position: 'absolute',
                                    left: 0,
                                    top: 0,
                                    background: '#FFFBDC',
                                    padding: '0 4px',
                                    borderRadius: '4px',
                                }}
                            >
                                私密
                            </div>
                        )}
                    </Col>
                    <Col flex={1} offset={1}>
                        <div>{row.liveTheme || '-'}</div>
                        <div className='sub-text-color'>房间号：{row.roomCode || '-'}</div>
                        <div className='sub-text-color'>直播时长：{row.totalLiveTimeStr || '-'}</div>
                    </Col>
                </Row>
            ),
        },
        {
            title: '成交金额',
            dataIndex: 'fullTotalGmv',
            renderText: (_, { fullTotalGmv }) => <div>¥ {fullTotalGmv || 0}</div>,
        },
        {
            title: '销售件数',
            dataIndex: 'fullSkuNumbers',
            renderText: (_, record: DataItem) => <div>{record.fullSkuNumbers || 0}件</div>,
        },
        {
            title: '观看人数',
            dataIndex: 'totalOnlineUsers',
            align: 'center',
            renderText: text => `${text || 0}人`,
        },
        {
            title: '观看人次',
            align: 'center',
            dataIndex: 'totalOnlineCount',
            renderText: text => `${text || 0}人`,
        },
        {
            title: 'UV价值',
            align: 'center',
            dataIndex: 'uvValue',
        },
        {
            title: '成交转化率',
            dataIndex: 'orderRate',
            align: 'center',
            renderText: text => (text ? `${text}%` : '-'),
        },
        {
            title: '操作',
            key: 'option',
            align: 'center',
            render: (_, row: DataItem) => [
                <Button key='btn-1' type='link' onClick={() => goPage(row)}>
                    详情
                </Button>,
                row.videoUrl ? <M3U8Player key='btn-2' src={row.videoUrl} /> : null,
                row.videoDownloadUrl ? (
                    <Button
                        key='btn-2'
                        icon={<DownloadOutlined />}
                        type='link'
                        // href={row.videoDownloadUrl}
                        onClick={() => {
                            uString.forceDownload(row.videoDownloadUrl);
                        }}
                        target='_blank'
                        variant='dashed'
                        color='default'
                        style={{
                            marginLeft: '12px',
                        }}
                    >
                        回放下载
                    </Button>
                ) : null,
            ],
        },
    ];

    return (
        <Card title='往期直播场次明细' headStyle={{ marginTop: '15px' }} bordered={false}>
            <ProTable<DataItem, ParamsItem>
                rowKey='roomCode'
                columns={columns}
                dateFormatter='string'
                options={false}
                search={false}
                ghost
                request={async params => {
                    const { current = 1, pageSize = 20, ...rest } = params;
                    const data = await getPastLivePage({
                        condition: {
                            ...rest,
                        },
                        page: current,
                        rows: pageSize,
                    });
                    return {
                        data: data.data?.dataList || [],
                        total: data.data?.total || 0,
                    };
                }}
            />
        </Card>
    );
});

export default PreviousLiveDetail;
