/**
 * @Owners mzh
 * @Title 往期直播统计
 */

import { type LivePreview } from '@/api/interface/livePreview';
import { getPastLiveStatistics } from '@/api/modules/livePreview';
import { Card, Row, Statistic } from 'antd';
import { memo, type CSSProperties, useEffect, useState } from 'react';

type StatisticsData = LivePreview.Response.getPastLiveStatistics;

const PreviousLiveStatistics = memo(() => {
    const [pastLiveStatistics, setPastLiveStatistics] = useState<StatisticsData>({} as StatisticsData);

    useEffect(() => {
        const getStatistics = async () => {
            const res = await getPastLiveStatistics();

            setPastLiveStatistics(res.data as StatisticsData);
        };

        getStatistics();
    }, []);

    const valueStyle: CSSProperties = {
        textAlign: 'center',
    };

    return (
        <Card title='往期直播统计' bordered={false}>
            <Row align={'middle'} justify={'space-around'}>
                <Statistic valueStyle={valueStyle} title='开播次数' value={pastLiveStatistics.liveNumbers} />
                <Statistic valueStyle={valueStyle} title='平均开播时长' value={pastLiveStatistics.avgLiveTimeStr} />
                <Statistic valueStyle={valueStyle} title='本周开播场次' value={pastLiveStatistics.weekLiveCounts} />
                <Statistic valueStyle={valueStyle} title='本月开播场次' value={pastLiveStatistics.monthLiveCounts} />
                <Statistic valueStyle={valueStyle} title='周均开播场次' value={pastLiveStatistics.weekAvgLives} />
                <Statistic valueStyle={valueStyle} title='月均开播场次' value={pastLiveStatistics.monthAvgLives} />
            </Row>
        </Card>
    );
});

export default PreviousLiveStatistics;
