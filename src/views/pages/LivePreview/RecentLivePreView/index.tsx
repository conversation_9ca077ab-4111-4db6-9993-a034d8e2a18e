/**
 * @Owners mzh
 * @Title 近期直播预告
 */
import { type LivePreview } from '@/api/interface/livePreview';
import { getPreLiveList } from '@/api/modules/livePreview';
import { PushStatusTextMap } from '@/consts/cLive';
import { ProTable, type ProColumns } from '@ant-design/pro-components';
import { Button, Card, Col, Image, Row } from 'antd';
import { memo } from 'react';
import { useNavigate } from 'react-router-dom';

type DataItem = LivePreview.Response.getLiveList['dataList'][number];
type ParamsItem = LivePreview.Params.Condition;

const RecentLivePreView = memo(() => {
    const navigate = useNavigate();

    const goPage = (row: DataItem) => {
        navigate(`/live/newConsole?liveId=${row.liveId}`);
    };

    const columns: ProColumns<DataItem>[] = [
        {
            title: '直播间信息',
            dataIndex: 'roomCode',
            width: 350,
            renderText: (_, row: DataItem) => (
                <Row style={{ flexWrap: 'nowrap' }} align={'middle'}>
                    <Col>
                        <Image width={80} src={row.coverUrl} />
                    </Col>
                    <Col flex={1} offset={1}>
                        <div>{row.liveTheme || '-'}</div>
                        <div className='sub-text-color'>房间号：{row.roomCode || '-'}</div>
                    </Col>
                </Row>
            ),
        },
        {
            title: '直播状态',
            dataIndex: 'pushStatus',
            align: 'center',
            width: 180,
            renderText: (test: number) => <div>{PushStatusTextMap[test]?.text}</div>,
        },
        {
            title: '橱窗商品',
            dataIndex: 'goodNumber',
            width: 180,
            align: 'center',
        },
        {
            title: '预告开播时间',
            dataIndex: 'preStartTime',
            width: 200,
            align: 'center',
        },
        {
            title: '预约人次',
            dataIndex: 'subscribeNumber',
            width: 180,
            align: 'center',
        },
        {
            title: '操作',
            key: 'option',
            align: 'center',
            width: 200,
            render: (_, row: DataItem) => [
                <Button key='btn-1' type='link' onClick={() => goPage(row)}>
                    直播详情
                </Button>,
            ],
        },
    ];

    return (
        <Card title='近期直播预告' bordered={false}>
            <ProTable<DataItem, ParamsItem>
                rowKey='roomCode'
                columns={columns}
                dateFormatter='string'
                options={false}
                search={false}
                ghost
                request={async () => {
                    const data = await getPreLiveList();
                    return {
                        data: data.data || [],
                    };
                }}
                pagination={false}
            />
        </Card>
    );
});

export default RecentLivePreView;
