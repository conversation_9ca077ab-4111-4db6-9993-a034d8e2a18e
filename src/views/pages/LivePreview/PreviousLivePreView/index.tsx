/**
 * @Owners mzh
 * @Title 往期直播场次
 */
import { type LivePreview } from '@/api/interface/livePreview';
import { getPastPreLivePage } from '@/api/modules/livePreview';
import { PushStatusTextMap } from '@/consts/cLive';
import { ProTable, type ProColumns } from '@ant-design/pro-components';
import { Button, Card, Col, Image, Row } from 'antd';
import { memo } from 'react';
import { useNavigate } from 'react-router-dom';

type DataItem = LivePreview.Response.getLiveList['dataList'][number];
type ParamsItem = LivePreview.Params.Condition;

const PreviousLivePreView = memo(() => {
    const navigate = useNavigate();

    const goPage = (row: DataItem) => {
        navigate(`/live/newConsole?liveId=${row.liveId}`);
    };

    const columns: ProColumns<DataItem>[] = [
        {
            title: '直播间信息',
            dataIndex: 'roomCode',
            width: 270,
            renderText: (_, row) => (
                <Row style={{ flexWrap: 'nowrap' }} align={'middle'}>
                    <Col>
                        <Image width={80} src={row.coverUrl} />
                    </Col>
                    <Col flex={1} offset={1}>
                        <div>{row.liveTheme || '-'}</div>
                        <div className='sub-text-color'>房间号：{row.roomCode || '-'}</div>
                        <div className='sub-text-color'>直播时长：{row.totalLiveTimeStr || '-'}</div>
                    </Col>
                </Row>
            ),
        },
        {
            title: '直播状态',
            dataIndex: 'pushStatus',
            align: 'center',
            renderText: (test: number) => <div>{PushStatusTextMap[test]?.text}</div>,
        },
        {
            title: '橱窗商品',
            dataIndex: 'goodNumber',
            align: 'center',
        },
        {
            title: '预告开播时间',
            dataIndex: 'preStartTime',
        },
        {
            title: '预约人数',
            dataIndex: 'subscribeNumber',
            align: 'center',
        },
        {
            title: '操作',
            key: 'option',
            align: 'center',
            render: (_, row: DataItem) => [
                <Button key='btn-1' type='link' onClick={() => goPage(row)}>
                    详情
                </Button>,
            ],
        },
    ];

    return (
        <Card title='往期直播预告' headStyle={{ marginTop: '15px' }} bordered={false}>
            <ProTable<DataItem, ParamsItem>
                rowKey='roomCode'
                columns={columns}
                dateFormatter='string'
                options={false}
                search={false}
                ghost
                request={async params => {
                    const { current = 1, pageSize = 20, ...rest } = params;
                    const data = await getPastPreLivePage({
                        condition: {
                            ...rest,
                        },
                        page: current,
                        rows: pageSize,
                    });
                    return {
                        data: data.data?.dataList || [],
                        total: data.data?.total || 0,
                    };
                }}
            />
        </Card>
    );
});

export default PreviousLivePreView;
