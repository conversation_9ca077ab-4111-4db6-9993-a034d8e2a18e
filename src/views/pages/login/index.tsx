/**
 * @Owners init
 * @Title 登录页
 */

import loginLeft from '@/assets/images/login_left.png';
import logo from '@/assets/images/logo.png';
import { type RootState, useSelector } from '@/redux';
import { Navigate } from 'react-router-dom';

import LoginForm from './components/LoginForm';
import LoginContainer from './style';

const appTitle = import.meta.env.VITE_GLOB_APP_TITLE;

const Login = () => {
    // 若存在登录态，重定向到首页
    const { token } = useSelector((state: RootState) => state.global);
    if (token) return <Navigate to='/home/<USER>' />;

    return (
        <LoginContainer>
            <div className='login-box'>
                <div className='login-left'>
                    <img src={loginLeft} alt='login' />
                </div>
                <div className='login-form'>
                    <div className='login-logo'>
                        <img className='login-icon' src={logo} alt='logo' />
                        <span className='logo-text'>{appTitle}</span>
                    </div>
                    <LoginForm />
                </div>
            </div>
        </LoginContainer>
    );
};

export default Login;
