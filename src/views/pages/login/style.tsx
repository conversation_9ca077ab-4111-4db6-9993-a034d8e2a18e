/**
 * @Owners init
 * @Title 登录页-样式
 */

import LoginBg from '@/assets/images/login_bg.svg';
import styled from 'styled-components';

const LoginContainer = styled.div`
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 550px;
    height: 100%;
    min-height: 500px;
    background-image: url(${LoginBg});
    background-position: 50%;
    background-size: 100% 100%;
    background-color: #eee !important;
    display: flex;
    .dark {
        position: absolute;
        top: 5%;
        right: 3.2%;
    }
    .login-box {
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 96%;
        height: 94%;
        padding: 0 4% 0 20px;
        overflow: hidden;
        border-radius: 10px;
        background-color: #fffc !important;
        .login-left {
            width: 750px;
            img {
                width: 100%;
                height: 100%;
            }
        }
        .login-form {
            padding: 40px 45px 25px;
            border-radius: 10px;
            background-color: transparent !important;
            box-shadow: 2px 3px 7px #0003 !important;
            .login-logo {
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 30px 40px;
                .login-icon {
                    width: 50px;
                    margin-right: 12px;
                }
                .logo-text {
                    font-size: 36px;
                    font-weight: 400;
                    white-space: nowrap;
                }
            }
            .ant-form-item {
                height: 75px;
                margin-bottom: 0;
                .ant-input-prefix {
                    margin-right: 10px;
                }
                .ant-input-affix-wrapper-lg {
                    padding: 8.3px 11px;
                }
                .ant-input-affix-wrapper,
                .ant-input-lg {
                    font-size: 14px;
                }
                .ant-input-affix-wrapper {
                    color: #bfbfbf;
                }
            }
            .login-btn {
                width: 100%;
                margin-top: 10px;
                white-space: nowrap;
                .ant-form-item-control-input-content {
                    display: flex;
                    justify-content: space-between;
                    .ant-btn {
                        width: 160px;
                        span {
                            font-size: 14px;
                        }
                    }
                }
            }
        }
    }
`;

export default LoginContainer;
