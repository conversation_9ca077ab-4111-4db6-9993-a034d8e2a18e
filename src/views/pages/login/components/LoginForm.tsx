/**
 * @Owners init
 * @Title 登录页-表单
 */

import { type User } from '@/api/interface/system';
import { getUserInfo, login } from '@/api/modules/system/user';
import { HOME_URL } from '@/config/config';
import { store, useDispatch } from '@/redux';
import { type UserInfoType } from '@/redux/interface';
import { setToken, setUserInfo } from '@/redux/modules/global';
import { setTabsList } from '@/redux/modules/tabs';
import { modal } from '@/views/components/UseAppPrompt';
import { CloseCircleOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { uCookies, uCryptoJS } from '@utils';
import { Button, Form, Input } from 'antd';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
const LoginForm = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState<boolean>(false);
    const location = useLocation();

    /** 跳转 */
    const navigateBack = () => {
        const { pathname } = location.state || {};

        if (pathname) navigate(pathname);
        else navigate(HOME_URL);
    };

    // 获取用户信息
    const init = async (userInfo: Partial<UserInfoType>) => {
        const res = await getUserInfo();
        store.dispatch(setUserInfo({ ...userInfo, ...res.data }));
        navigateBack();
    };

    const onFinish = async (loginForm: User.LoginReq) => {
        try {
            setLoading(true);
            // 请求登录
            // 先清除 cookie 中的旧 token，防止存在不同 domain 的 cookie-token
            uCookies.remove('token');
            const { username, password } = loginForm;
            const res = await login({
                username: uCryptoJS.Encrypt(username),
                password: uCryptoJS.Encrypt(password),
            });

            if (res.code === '0' && res.data) {
                const { token, id, userId } = res.data;
                store.dispatch(setToken(token || ''));
                store.dispatch(setUserInfo({ loginId: id, liveUserId: userId }));
                // 清空标签页
                dispatch(setTabsList([]));
                await init({ loginId: id, liveUserId: userId });
            }
        } finally {
            setLoading(false);
        }
    };

    const onFinishFailed = (errorInfo: unknown) => {
        console.log('Failed:', errorInfo);
    };

    const forgotPassword = () => {
        modal.confirm({
            title: '温馨提示',
            content: '请联系客服重置密码',
            okText: '好的',
            cancelButtonProps: { style: { display: 'none' } },
        });
    };
    return (
        <Form
            form={form}
            name='basic'
            labelCol={{ span: 5 }}
            initialValues={{ remember: true }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            size='large'
            autoComplete='off'
        >
            <Form.Item name='username' rules={[{ required: true, message: '请输入用户名' }]}>
                <Input autoComplete='username' placeholder='用户名：' prefix={<UserOutlined />} />
            </Form.Item>
            <Form.Item name='password' rules={[{ required: true, message: '请输入密码' }]}>
                <Input.Password autoComplete='current-password' placeholder='密码：' prefix={<LockOutlined />} />
            </Form.Item>
            <div style={{ textAlign: 'right', paddingBottom: '10px', color: '#1677ff', cursor: 'default' }}>
                <span onClick={forgotPassword}>忘记密码</span>
            </div>
            <Form.Item className='login-btn'>
                <Button type='primary' htmlType='submit' loading={loading} icon={<UserOutlined />}>
                    登录
                </Button>
                <Button
                    onClick={() => {
                        form.resetFields();
                    }}
                    icon={<CloseCircleOutlined />}
                >
                    清空
                </Button>
            </Form.Item>
        </Form>
    );
};

export default LoginForm;
