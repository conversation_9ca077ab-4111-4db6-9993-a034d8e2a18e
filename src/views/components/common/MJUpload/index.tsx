/**
 * @Owners zmd
 * @Title 上传文件-组件
 */

import { PlusOutlined } from '@ant-design/icons';
import { cConfig } from '@consts';
import { Modal, Upload, message } from 'antd';
import type { RcFile, UploadChangeParam } from 'antd/es/upload';
import { type UploadFile as _UploadFile } from 'antd/es/upload/interface';
import { useEffect, useRef, useState } from 'react';

import StyleContainer from './Style';

export type UploadFile = _UploadFile;
export type Props = {
    action?: string;
    name: string;
    fileList: UploadFile[];
    acceptExt: string;
    listType: 'picture-card' | 'picture' | 'text';
    maxFileLength?: number; // 最大上传的文件个数
    maxFileSize?: number; // 上传文件最大大小
    tipDesc?: string; // 备注提示
    disable?: boolean; // 是否只读
    maxFileSizeTips?: string;
    aspectRatio?: number;
    onChange(fileList: UploadFile[]): void;
};
const MJUpload = (props: Props) => {
    const {
        // action = '/platform/utils/files/upload',
        disable = false,
        fileList = [],
        maxFileLength = 10,
        maxFileSize,
        tipDesc = '',
        listType,
        acceptExt,
        maxFileSizeTips = '上传的文件过大！',
        aspectRatio,
        onChange,
    } = props;
    const [fileData, setFileData] = useState<UploadFile[]>([]);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const fileDataRef = useRef<UploadFile[]>([]);
    const [files, setFiles] = useState<UploadFile[]>([]);
    const fileStatus = useRef<boolean>(true);

    const getImageDimensions = (file: RcFile): Promise<{ width: number; height: number }> =>
        new Promise((resolve, reject) => {
            const image = new Image();

            image.onload = () => {
                const { width, height } = image;
                resolve({ width, height });
            };

            image.onerror = () => {
                reject(new Error('无法读取图片'));
            };

            image.src = URL.createObjectURL(file);
        });

    const handleBeforeUpload = async (file: RcFile, _fileList: UploadFile[]) => {
        fileStatus.current = true;
        if (maxFileSize && file.size > maxFileSize) {
            message.error(maxFileSizeTips);
            fileStatus.current = false;
            return false;
        }
        const imgInfo = await getImageDimensions(file);
        const width = imgInfo.width;
        const height = imgInfo.height;
        if (aspectRatio && aspectRatio !== width / height) {
            message.error('图片尺寸有误，请重新上传');
            fileStatus.current = false;
            return false;
        }

        setFiles([...files, file]);
        onChange && onChange(_fileList);
        return false;
    };

    const getOssConfig = async (type: string) =>
        fetch(`${cConfig.DOMAIN_FRONT}/configv2/getOssConfig`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', token: 'token' },
            mode: 'cors',
            body: JSON.stringify({ maxSize: 10, type }),
        }).then(
            result =>
                result.json().then(res => {
                    if (res.code === 0 || res.code === '0') {
                        return res.data;
                    }
                }),
            error => {
                console.warn('获取config失败', error);
            }
        );

    const getPictureType = (image: string) => {
        const arr = image.split('.');
        const index = arr.length - 1;
        return '.' + arr[index];
    };

    const handleOnChange = async (info: UploadChangeParam) => {
        if (!fileStatus.current) {
            // 校验失败，不执行上传操作
            return;
        }
        const filePath = info.file.name || '';
        const type = getPictureType(filePath);
        const {
            fileName,
            signature,
            policy,
            accessKeyId: OSSAccessKeyId,
        } = (await getOssConfig(type)) as unknown as {
            fileName: string;
            signature: string;
            policy: string;
            accessKeyId: string;
        };

        const aliyunFileKey = `${cConfig.ossConfig.dir}${fileName}${type}`;

        const formData = new FormData();
        formData.set('key', aliyunFileKey);
        formData.set('signature', signature);
        formData.set('policy', policy);
        formData.set('OSSAccessKeyId', OSSAccessKeyId);
        formData.set('name', filePath);
        formData.set('success_action_status', '200');
        formData.append('file', files[0] as RcFile);

        fetch(cConfig.ossConfig.host, {
            method: 'POST',
            mode: 'cors',
            body: formData,
        }).then(
            result => {
                if (result.status === 200) {
                    const files = info.fileList.map(item => ({
                        ...item,
                        url: result.url + aliyunFileKey,
                        status: 'success' as UploadFile['status'],
                    }));
                    onChange && onChange(files);
                }
            },
            error => {
                console.warn('上传失败', error);
            }
        );
    };

    const getBase64 = (file: RcFile): Promise<string> =>
        new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
        });

    const handlePreview = async (file: UploadFile) => {
        if (!file.url && !file.preview) {
            file.preview = await getBase64(file.originFileObj as RcFile);
        }

        setPreviewImage(file.url || (file.preview as string));
        setPreviewOpen(true);
    };

    const handleCancel = () => setPreviewOpen(false);

    useEffect(() => {
        fileDataRef.current = fileList;
        setFileData(fileList);
    }, [fileList]);

    return (
        <StyleContainer>
            <Upload
                name='file'
                fileList={fileData}
                beforeUpload={handleBeforeUpload}
                onChange={handleOnChange}
                onPreview={handlePreview}
                accept={acceptExt}
                listType={listType}
                maxCount={maxFileLength}
                disabled={disable}
            >
                {fileData.length >= maxFileLength ? null : <PlusOutlined />}
            </Upload>
            {!disable && tipDesc && <span className='tip-desc'>{tipDesc}</span>}
            <Modal open={previewOpen} title='图片预览' footer={null} onCancel={handleCancel}>
                <img alt='example' style={{ width: '100%' }} src={previewImage} />
            </Modal>
        </StyleContainer>
    );
};

export default MJUpload;
