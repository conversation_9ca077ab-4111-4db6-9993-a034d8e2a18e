/**
 * @Owners init
 * @Title 加载中-样式
 */

import themeConfig from '@/styles/themeConfig';
import { Spin } from 'antd';
import styled from 'styled-components';

const SpinContainer = styled(Spin)`
    /* 请求 Loading 样式 */
    .request-loading {
        .ant-spin-text {
            margin-top: 5px;
            font-size: 18px;
            color: ${themeConfig.colors.primaryColor} !important;
        }
        .ant-spin-dot-item {
            background-color: ${themeConfig.colors.primaryColor} !important;
        }
    }
`;

export default SpinContainer;
