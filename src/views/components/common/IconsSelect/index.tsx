/**
 * @Owners zp
 * @Title icon选择器
 */
import type * as Icons from '@ant-design/icons';
import { ProFormSelect } from '@ant-design/pro-components';
import { getIconsOption } from '@utils';
import { memo } from 'react';

type Props = {
    label: string;
    name: string;
    value?: keyof typeof Icons;
    onChange?(v: string): void;
};

const IconSelect: React.FC<Props> = memo((props: Props) => {
    const options = getIconsOption();

    return (
        <ProFormSelect
            placeholder='请选择icon'
            width={300}
            {...props}
            fieldProps={{
                showSearch: true,
                options,
                maxLength: 20,
                popupClassName: 'sys-menu-icon-select',
                virtual: false,
                dropdownMatchSelectWidth: 380,
                filterOption: (input, option) => {
                    const hasRes = ((option?.value as string) ?? '').includes(input);
                    return hasRes;
                },
            }}
        />
    );
});

export default IconSelect;
