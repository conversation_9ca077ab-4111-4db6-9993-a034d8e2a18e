/**
 * @Owners init
 * @Title 图标-组件
 */

import { QuestionCircleOutlined } from '@ant-design/icons';
import { Popover, Tooltip } from 'antd';
import React, { type ReactNode } from 'react';

import './index.scss';

interface TipsIconProps {
    /**
     * 提示内容，可以是字符串、字符串数组或React节点
     */
    content: ReactNode | string[];

    tipsTitle?: string;

    /**
     * 图标大小
     */
    size?: number;

    /**
     * 图标颜色
     */
    color?: string;

    /**
     * 弹出位置
     */
    placement?:
        | 'bottom'
        | 'bottomLeft'
        | 'bottomRight'
        | 'left'
        | 'leftBottom'
        | 'leftTop'
        | 'right'
        | 'rightBottom'
        | 'rightTop'
        | 'top'
        | 'topLeft'
        | 'topRight';

    /**
     * 自定义图标
     */
    icon?: ReactNode;

    /**
     * 自定义类名
     */
    className?: string;

    /**
     * 是否使用Tooltip代替Popover (当内容简单时推荐使用)
     */
    useTooltip?: boolean;
}

const TipsIcon: React.FC<TipsIconProps> = ({
    content,
    size = 16,
    color = '#999',
    placement = 'top',
    icon,
    className = '',
    useTooltip = false,
    tipsTitle,
}) => {
    const iconElement = icon || <QuestionCircleOutlined className={`tips-icon ${className}`} style={{ fontSize: size, color }} />;

    // 处理content为字符串数组的情况
    const renderContent = () => {
        if (Array.isArray(content)) {
            return (
                <div className='tips-content'>
                    <div className={'tips-title'}>{tipsTitle}</div>
                    {content.map((item, index) => (
                        <p key={index}>{item}</p>
                    ))}
                </div>
            );
        }
        return <div className='tips-content'>{content}</div>;
    };

    if (useTooltip) {
        return (
            <Tooltip
                title={
                    Array.isArray(content) ? (
                        <div>
                            <div className={'tips-title'}>{tipsTitle}</div>
                            {content.map((item, index) => (
                                <div key={index}>{item}</div>
                            ))}
                        </div>
                    ) : (
                        content
                    )
                }
                placement={placement}
            >
                {iconElement}
            </Tooltip>
        );
    }

    return (
        <Popover content={renderContent()} placement={placement} trigger='hover' classNames={{ root: 'tips-popover' }}>
            {iconElement}
        </Popover>
    );
};

export default TipsIcon;
