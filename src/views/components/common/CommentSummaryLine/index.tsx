/**
 * @Owners zgy
 * @Title 评论总结行
 */
import iconRight from '@/assets/images/icon-right.png';
import { Popover } from 'antd';
import React from 'react';

import './index.scss';

interface CommentSummaryLineProps {
    comments: string[];
    maxWidth?: number;
    className?: string;
}

const CommentSummaryLine: React.FC<CommentSummaryLineProps> = ({ comments, className }) => {
    if (!comments || comments.length === 0) {
        return null;
    }

    const renderSummary = () => (
        <div className={`comment-summery-line ${className}`}>
            <div className={'comment-container'}>
                <span>相关评论：</span>
                {comments.map((comment, index) => (
                    <React.Fragment key={index}>
                        <span>{comment}</span>
                        {index < comments.length - 1 && <span className='comment-divider'>/</span>}
                    </React.Fragment>
                ))}
            </div>
            <img src={iconRight} alt='' className='icon-right' />
        </div>
    );

    const renderPopoverContent = () => (
        <div className='popover-content'>
            <div className='popover-title'>相关评论</div>
            {comments.map((comment, index) => (
                <div className='comment-item' key={index}>
                    {comment}
                </div>
            ))}
        </div>
    );

    return (
        <Popover content={renderPopoverContent()} placement='bottom' trigger='hover' overlayStyle={{ maxWidth: '400px' }}>
            {renderSummary()}
        </Popover>
    );
};

export default CommentSummaryLine;
