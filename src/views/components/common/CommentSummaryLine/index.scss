.comment-summery-line {
    display: flex;
    overflow: auto;
    cursor: pointer;
    background: rgba(247, 248, 249, 0.6);
    border-radius: 6px 6px 6px 6px;
    align-items: center;
    padding: 8px 12px;
}

.icon-right {
    flex-shrink: 0;
    flex-grow: 0;
    width: 16px;
}

.comment-container {
    // display: flex;
    // align-items: center;
    flex: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    color: #999999;

}

.comment-divider {
    margin: 0 5px;
    color: #999;
}

.popover-content {
    max-width: 400px;
    max-height: 400px;
    overflow-y: auto;
}

.popover-title {
    font-weight: bold;
    margin-bottom: 10px;
}

.comment-item {
    padding: 8px 0;
    border-bottom: 1px solid #eeeeee;
    font-size: 12px;
    color: #333333;

    &:last-child {
        border-bottom: none;
    }
}