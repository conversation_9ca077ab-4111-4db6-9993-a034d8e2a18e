/**
 * @Owners zp
 * @Title KeepAlive - 路由缓存
 */
import { type RootState, useSelector } from '@/redux';
import { memo, useEffect, useMemo, useReducer, useRef } from 'react';
import { useLocation, useOutlet } from 'react-router-dom';

import KeepAliveContainer from './KeepAliveContainer';
import KeepAliveContent from './KeepAliveContent';

export default memo((props: { includes: string[]; excludes?: string[] }) => {
    const outlet = useOutlet();
    const { includes, excludes = [] } = props;
    const { pathname } = useLocation();
    const { tabsList } = useSelector((state: RootState) => state.tabs);
    // 缓存组件
    const componentList = useRef(new Map());
    // 强制渲染
    const forceUpdate = useReducer((bool: boolean) => !bool, true)[1];
    const cacheKey = useMemo(() => `${pathname}__${includes.findIndex(p => p === pathname)}`, [pathname]);
    // 是否使用默认渲染
    const useDefaultRender = useMemo(() => excludes.includes(pathname), [pathname]);
    // 当前选中页面
    const activeKey = useRef<string>('');

    useEffect(() => {
        if (!useDefaultRender) {
            componentList.current.forEach((_value, key) => {
                const _key = key.split('__')[0];
                // 过滤当前path是否需要缓存, 注：删除tab，同时清除缓存
                if (!includes.includes(_key) || !tabsList.map(tab => tab.path).includes(_key)) {
                    componentList.current.delete(key);
                }
            });

            // 更新当前页面key
            activeKey.current = cacheKey;

            // 去重
            if (!componentList.current.has(activeKey.current)) {
                componentList.current.set(activeKey.current, outlet);
            }

            forceUpdate();
        }
    }, [cacheKey, includes, useDefaultRender, tabsList]);

    return (
        <KeepAliveContainer>
            {useDefaultRender
                ? outlet
                : Array.from(componentList.current).map(([key, component]) => (
                      <KeepAliveContent key={key} activeKey={activeKey.current} data-page-path={key}>
                          {component}
                      </KeepAliveContent>
                  ))}
        </KeepAliveContainer>
    );
});
