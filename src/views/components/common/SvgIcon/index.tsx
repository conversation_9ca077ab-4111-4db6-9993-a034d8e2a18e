/**
 * @Owners init
 * @Title 图标-组件
 */

interface SvgProps {
    className?: string; // 图标的类名 ==> 非必传
    name: string; // 图标的名称 ==> 必传
    color?: string; // 图标的颜色 ==> 非必传
    prefix?: string; // 图标的前缀 ==> 非必传（默认为"icon"）
    iconStyle?: { [key: string]: unknown }; // 图标的样式 ==> 非必传
}

export default function SvgIcon(props: SvgProps) {
    const { name, prefix = 'icon', color = 'currentColor', iconStyle = { width: '16px', height: '16px' }, className } = props;
    const symbolId = `#${prefix}-${name}`;
    return (
        <svg aria-hidden='true' className={className} style={iconStyle}>
            <use href={symbolId} fill={color} />
        </svg>
    );
}
