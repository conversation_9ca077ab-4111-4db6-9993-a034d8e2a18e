/**
 * @Owners xj
 * @Title 富文本编辑器
 */
import React, { createRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
type Props = ReactQuill['props'] & {
    // 图片上传是否使用oss转换 制图oss上传需要配置图片上传imageUploadFn回调函数
    imageUploadByOss?: boolean;
    // 是否支持多张上传
    isMultiple?: boolean;
    // 资源最大上传数量
    maxLength?: number;
    // 图片上传回调
    imageUploadFn?(arg0: FormData): Promise<string>;
};

const formats = [
    'header',
    'size',
    'color',
    'background',
    'bold',
    'italic',
    'underline',
    'strike',
    'blockquote',
    'list',
    'bullet',
    'indent',
    'align',
    'link',
    'image',
];

const defaultStyle = { height: '300px' };
class RichText extends React.Component<Props> {
    public constructor(props: Props) {
        super(props);
    }

    public ref = createRef<ReactQuill>();

    // 处理富文本上传图片 处理oss上传 默认上传返回的是base64 这里将返回成oss地址
    public imageHandler = () => {
        const { isMultiple = false, imageUploadFn, maxLength } = this.props;
        const quillEditor = this.ref.current?.getEditor();
        const input = document.createElement('input');
        input.setAttribute('type', 'file');
        if (isMultiple) {
            input.setAttribute('multiple', 'true');
        }
        input.setAttribute('accept', 'image/*');
        input.click();
        input.onchange = async () => {
            const fileLists = input.files as FileList;
            const len = maxLength && fileLists.length > maxLength ? maxLength : fileLists?.length;
            for (let i = 0; i < len; i++) {
                const file = fileLists[i];
                const formData = new FormData();
                formData.append('file', file);
                if (imageUploadFn) {
                    const url = await imageUploadFn(formData);
                    const cursorPosition = quillEditor?.getSelection()?.index || 0;
                    quillEditor?.insertEmbed(cursorPosition, 'image', url);
                }
            }
        };
    };

    public override render() {
        const {
            defaultValue,
            onChange,
            onBlur,
            value = '',
            readOnly = false,
            style,
            className,
            imageUploadByOss = false,
            imageUploadFn,
        } = this.props;

        const modules = {
            toolbar: {
                container: [
                    [
                        { header: [1, 2, 3, 4, 5, 6, false] },
                        { size: ['small', 'large', 'huge'] },
                        { color: ['blue', 'red', 'gray', 'black', 'green', 'orange', 'white'] },
                        { background: ['blue', 'red', 'gray', 'black', 'green', 'orange', 'white'] },
                    ],
                    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
                    [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
                    [{ align: ['justify', 'center', 'right'] }, 'link', 'image'],
                ],
                handlers:
                    imageUploadByOss && !!imageUploadFn
                        ? {
                              image: this.imageHandler,
                          }
                        : {},
            },
        };

        return (
            <ReactQuill
                className={`${readOnly ? className : ''}`}
                ref={this.ref}
                style={style ? style : defaultStyle}
                theme='snow'
                readOnly={readOnly}
                defaultValue={defaultValue}
                value={value}
                onChange={onChange}
                onBlur={onBlur}
                modules={modules}
                formats={formats}
            />
        );
    }
}
export default RichText;
