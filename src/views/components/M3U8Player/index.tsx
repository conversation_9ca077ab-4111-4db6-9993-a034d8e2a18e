/**
 * @Owners lzy
 * @Title M3U8P 播放器
 */

import { Button, Modal } from 'antd';
import Hls from 'hls.js';
import { useEffect, useRef, useState } from 'react';

export const handleDownload = ({ src }: { src: string }) => {
    const videoUrl = src; // 替换为你的视频链接
    const link = document.createElement('a');
    link.href = videoUrl;
    // link.download = 'video.mp4'; // 这里可以指定下载文件的名称
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

const M3U8Player: React.FC<{ src: string }> = ({ src }) => {
    const [visible, setVisible] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);

    const isM3u8 = src.endsWith('.m3u8');

    useEffect(() => {
        if (visible && isM3u8 && videoRef.current) {
            const video = videoRef.current;
            if (Hls.isSupported()) {
                const hls = new Hls();
                hls.loadSource(src); // 替换为你的 m3u8 URL
                hls.attachMedia(video);
                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    video.play();
                });
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = src;
                video.addEventListener('loadedmetadata', () => {
                    video.play();
                });
            }
        } else if (videoRef.current) {
            videoRef.current?.pause();
        }
    }, [visible]);

    return (
        <>
            <Button type='primary' onClick={() => setVisible(true)}>
                回放视频
            </Button>
            <Modal open={visible} footer={null} onCancel={() => setVisible(false)} width={800}>
                {isM3u8 ? (
                    <video ref={videoRef} controls style={{ height: '600px', width: '100%' }} />
                ) : (
                    <video ref={videoRef} src={src} controls style={{ height: '600px', width: '100%' }} />
                )}
            </Modal>
        </>
    );
};

export default M3U8Player;
