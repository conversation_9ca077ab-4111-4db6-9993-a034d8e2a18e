/**
 * @Owners blv
 * @Title 积分兑券 - 已选择优惠券组件
 */
import { Button, Popconfirm, Row, Table } from 'antd';
import { useEffect, useState } from 'react';

import { CouponStatus, CouponType, getCouponRuleText } from './SelectCoupon';
import { type CouponItem } from './index';

type Props = {
    // 已选的数据
    selectedCoupons: CouponItem[];
    // 数据变更回调
    onChange(data: CouponItem[]): void;
};

const SelectGoodsDetail = (props: Props) => {
    const { selectedCoupons } = props;
    const [data, setData] = useState<CouponItem[]>([]);

    const handleDelete = (id: number) => {
        const newData = [...data].filter(i => i.couponId !== id);
        setData(newData);
    };

    // 清空coupons
    const handleDeleteCoupons = () => {
        setData([]);
    };

    const columns = [
        { title: '优惠券名称', key: 'couponName', width: 150, dataIndex: 'couponName' },
        {
            title: '优惠券类型',
            key: 'discountType',
            width: 150,
            dataIndex: 'discountType',
            render: (value: number) => CouponType[value],
        },
        {
            title: '优惠券规则',
            key: 'discountType',
            width: 150,
            dataIndex: 'discountType',
            render: (value: number, record: CouponItem) =>
                getCouponRuleText(value, record.discountRuleModels, record.fullSubtractType),
        },
        { title: '优惠券状态', key: 'status', width: 150, dataIndex: 'status', render: (value: number) => CouponStatus[value] },
        {
            title: '操作',
            key: 'couponId',
            width: 100,
            dataIndex: 'couponId',
            render: (text: number) => (
                <Popconfirm
                    title='确认删除？'
                    onConfirm={() => {
                        handleDelete(text);
                    }}
                >
                    <Button type='link'>删除</Button>
                </Popconfirm>
            ),
        },
    ];

    // 初始化
    useEffect(() => {
        if (selectedCoupons.length) {
            setData(selectedCoupons);
        }
    }, []);

    // 选择改变
    useEffect(() => {
        props.onChange && props.onChange(data);
    }, [data]);

    return (
        <>
            {!!data.length && (
                <Row justify='end' className='mb10'>
                    <Popconfirm title='确认删除？' onConfirm={handleDeleteCoupons}>
                        <Button>清空</Button>
                    </Popconfirm>
                </Row>
            )}
            <Table key='id' rowKey='id' dataSource={data} columns={columns} />
        </>
    );
};

export default SelectGoodsDetail;
