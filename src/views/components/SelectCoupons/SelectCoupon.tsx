/**
 * @Owners blv
 * @Title 积分兑券 - 选择优惠券组件
 */
import { type Coupon } from '@/api/interface/coupon';
import { getCouponListByPage } from '@/api/modules/coupon';
import { uNumber } from '@/utils/uNumber';
import { Button, Input, Row, Select, Table, message } from 'antd';
import _ from 'lodash';
import { useEffect, useState } from 'react';

import { type CouponItem } from './index';

type Props = {
    /** 是否需要店长手机号 */
    isNeedShopPhone?: boolean;
    /** 店长手机号 */
    shopPhone?: string | undefined;
    // 已选的数据
    selectedCoupons: CouponItem[];
    //  值变更回调 如果是单选 则回传单条数据数组 如果是多选 则传递多条数据
    onChange(data: CouponItem[]): void;
};

export enum CouponType {
    '满减券' = 1,
    '折扣券' = 2,
    '无门槛' = 3,
}
export enum CouponStatus {
    '未生效' = 1,
    '已生效' = 2,
    '已过期' = 3,
    '已作废' = 4,
}

enum RangeType {
    '小程序商城券' = 1,
    '直播间营销券' = 2,
}

export function getCouponRuleText(
    discountType: number,
    discountRuleModels: CouponItem['discountRuleModels'],
    fullSubtractType: number
) {
    if (discountType === 1) {
        // 满减券
        const res = discountRuleModels.map(
            i =>
                `满${fullSubtractType === 2 ? `${i.to}件` : `${uNumber.centToYuan(i.to as number)}元`}减${uNumber.centToYuan(
                    i.sub as number
                )}`
        );
        const val = discountRuleModels[0];
        return `${res.join(',')}${!!val.maxSub ? `,最多减${uNumber.centToYuan(val.maxSub)}` : ''}`;
    } else if (discountType === 2) {
        const val = discountRuleModels[0];
        // 折扣券
        return `满${uNumber.centToYuan(val.to as number)}打${val.discount}折${
            !!val.maxSub ? `,最多减${uNumber.centToYuan(val.maxSub)}` : ''
        }`;
    } else if (discountType === 3) {
        // 无门槛
        return `无门槛减${uNumber.centToYuan(discountRuleModels[0].cash as number)}`;
    }
    return '-';
}

const SelectGoodsDetail = (props: Props) => {
    const { selectedCoupons, shopPhone, isNeedShopPhone, onChange } = props;
    const [loading, setLoading] = useState(true);
    const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
    const [pageNum, setPageNum] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState<number>(0);
    const [dataSource, setDataSource] = useState<Coupon.CouponListType[]>([]);
    const [name, setName] = useState<string>();
    const [id, setId] = useState<string>();
    const [status, setStatus] = useState<string>('2');
    const columns = [
        { title: '优惠券ID', key: 'id', width: 150, dataIndex: 'id' },
        { title: '优惠券名称', key: 'name', width: 150, dataIndex: 'name' },
        {
            title: '优惠券类型',
            key: 'discountType',
            width: 150,
            dataIndex: 'discountType',
            render: (value: number) => CouponType[value],
        },
        {
            title: '优惠券规则',
            key: 'discountType',
            width: 150,
            dataIndex: 'discountType',
            render: (value: number, record: CouponItem) =>
                getCouponRuleText(value, record.discountRuleModels, record.fullSubtractType),
        },
        {
            title: '领取范围',
            key: 'couponType',
            width: 150,
            dataIndex: 'couponType',
            render: (value: number) => RangeType[value],
        },
        { title: '优惠券状态', key: 'status', width: 150, dataIndex: 'status', render: (value: number) => CouponStatus[value] },
        {
            title: '优惠券有效期',
            key: 'timeModel',
            width: 150,
            dataIndex: 'timeModel',
            render: (_value: string, record: CouponItem) => {
                const { expiryStart, expiryEnd, sameDay, nextDay = '' } = record.timeModel || {};
                if (nextDay) return `领取次日起,${nextDay}天内可用`;
                if (sameDay) return `领取当日起,${sameDay}天内可用`;
                return `${expiryStart}~${expiryEnd}`;
            },
        },
    ];

    const getDataSource = async (page?: number) => {
        if (!shopPhone && isNeedShopPhone) {
            message.warning('请先填写主播信息');
            return;
        }
        setLoading(true);
        const params = {
            page: page || pageNum,
            rows: pageSize,
            shopPhone,
            couponType: 2,
            status,
            name,
            id,
        };
        const res = await getCouponListByPage(params);
        setLoading(false);
        setTotal(res?.data?.total || 0);
        setDataSource(res?.data?.dataList || []);
    };

    // 初始化
    useEffect(() => {
        setSelectedRowKeys(selectedCoupons.map(i => i.couponId));
    }, [selectedCoupons.length]);

    // 搜索条件改变重新搜索
    useEffect(() => {
        getDataSource();
    }, [pageNum, pageSize]);

    const handleSelectChange = (selectedKeys: React.Key[], _selectedRows: CouponItem[]) => {
        // const currentPageKeys = dataSource.map(i => i.id);
        // const currentPageSelectKeys = selectedRowKeys.filter(i => currentPageKeys.includes(i));
        // const isAdd = selectedRows.length > currentPageSelectKeys.length;
        // let newSelectedKeys = [...selectedRowKeys];
        // let newData = [...selectedCoupons];

        // if (isAdd) {
        //     const newAddData = dataSource.filter(i => !currentPageSelectKeys.includes(i.id) && selectedKeys.includes(i.id));
        //     newAddData.forEach(i => {
        //         newSelectedKeys.push(i.id);
        //         const baseData = {
        //             couponId: i.id,
        //             couponName: i.name,
        //             discountRuleModels: i.discountRuleModels,
        //             discountType: i.discountType,
        //             fullSubtractType: i.fullSubtractType,
        //             status: i.status,
        //             usableNum: i.usableNum,
        //             expiryEnd: i.expiryEnd,
        //             timeModel: i.timeModel,
        //             goodsScopeType: i.goodsScopeType,
        //         };
        //         newData.push(baseData);
        //     });
        // } else {
        //     const deleteKeys = _.xor(currentPageSelectKeys, selectedKeys);
        //     newSelectedKeys = newSelectedKeys.filter(i => !deleteKeys.includes(i));
        //     newData = newData.filter(i => !deleteKeys.includes(i.couponId));
        // }
        // setSelectedRowKeys(newSelectedKeys);
        // onChange && onChange(newData);

        // 以上是多选逻辑 二期改多选只需删除以下代码 释放以上代码就可以
        const newData: CouponItem[] = [];
        const newSelectedKeys: number[] = [];
        const newAddData = dataSource.filter(i => selectedKeys.includes(i.id));
        newAddData.forEach(i => {
            newSelectedKeys.push(i.id);
            const baseData = {
                couponId: i.id,
                couponName: i.name,
                discountRuleModels: i.discountRuleModels,
                discountType: i.discountType,
                fullSubtractType: i.fullSubtractType,
                status: i.status,
                usableNum: i.usableNum,
                expiryEnd: i.expiryEnd,
                timeModel: i.timeModel,
                goodsScopeType: i.goodsScopeType,
            };
            newData.push(baseData);
        });
        setSelectedRowKeys(newSelectedKeys);
        onChange && onChange(newData);
    };

    return (
        <>
            <Row align='middle'>
                优惠券名称：
                <Input
                    placeholder='请输入优惠券名称'
                    value={name}
                    onChange={e => setName((e as { target: { value: string } }).target.value)}
                    style={{ width: '150px', marginRight: '20px' }}
                    allowClear
                />
                优惠券ID：
                <Input
                    placeholder='请输入优惠券ID'
                    value={id}
                    onChange={e => setId((e as { target: { value: string } }).target.value)}
                    style={{ width: '150px', marginRight: '20px' }}
                    allowClear
                />
                优惠券状态：
                <Select value={status} placeholder='请选择' onChange={setStatus} style={{ width: '150px', marginRight: '20px' }}>
                    <Select.Option value='1'>未生效</Select.Option>
                    <Select.Option value='2'>生效中</Select.Option>
                </Select>
                <Button
                    type='primary'
                    onClick={() => {
                        getDataSource(1);
                        setPageNum(1);
                    }}
                >
                    搜索
                </Button>
            </Row>
            <Table
                loading={loading}
                key='couponId'
                rowKey='couponId'
                dataSource={dataSource as unknown as readonly CouponItem[]}
                columns={columns}
                scroll={{ x: 'max-content', y: 400 }}
                rowSelection={{
                    type: 'radio',
                    selectedRowKeys,
                    onChange: handleSelectChange,
                    // getCheckboxProps: (record: DataType) => ({
                    //     disabled: record.name === 'Disabled User', // Column configuration not to be checked
                    //     name: record.name,
                    //   }),
                }}
                pagination={{
                    total,
                    current: pageNum,
                    pageSize,
                    position: ['bottomRight'],
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: all => `共 ${all} 条记录`,
                    onChange: (num, size) => {
                        setPageNum(num);
                        setPageSize(size ? size : 10);
                    },
                }}
            />
        </>
    );
};

export default SelectGoodsDetail;
