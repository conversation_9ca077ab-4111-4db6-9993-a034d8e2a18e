/**
 * @Owners blv
 * @Title 积分兑券 - 选择优惠券弹窗Modal组件
 */
import { type Coupon } from '@/api/interface/coupon';
import { Modal, Tabs } from 'antd';
import { useEffect, useState } from 'react';

import SelectCoupon from './SelectCoupon';
import SelectedCoupon from './SelectedCoupon';

// 已选优惠券数据项
export type CouponItem = {
    // 券id
    couponId: number;
    // 券名称
    couponName: string;
    // 优惠券规则
    discountRuleModels: Coupon.CouponListType['discountRuleModels'];
    // 优惠券类型 1.满减券 2.折扣券 3.无门槛
    discountType: number;
    // 1满额件，2满件减
    fullSubtractType: number;
    // 优惠券状态 1.未生效 2.已生效 3.已过期 4.已作废
    status: number;
    // 券可用数量
    usableNum: number;
    // 券结束时间
    expiryEnd: string;
    // 券申请数量
    applyNum?: number;
    // 剩余数量
    activeNum?: number;
    // 兑换数量
    receiveNum?: number;
    // 兑券所需积分
    integral?: number;
    // 券总数
    total?: number;
    timeModel?: {
        // 有效结束时间
        expiryEnd: string;
        // 有效开始时间
        expiryStart: string;
        // 领取次天起几日内有效
        nextDay: number | undefined;
        // 领取当天起几日内有效
        sameDay: number | undefined;
    };
};
type Props = {
    /** 是否需要店长手机号 */
    isNeedShopPhone?: boolean;
    /** 店长手机号 */
    shopPhone?: string;
    // 已选的数据
    selectedCoupons: CouponItem[];
    // 是否过滤积分兑换
    isNeedExchangeFraction?: boolean;
    //  值变更回调 如果是单选 则回传单条数据数组 如果是多选 则传递多条数据
    onChange(data: CouponItem[]): void;
    // 关闭回调
    onClose(): void;
};

const SelectCouponModal = (props: Props) => {
    const { onChange, onClose, selectedCoupons, shopPhone, isNeedShopPhone = false } = props;
    const [type, seType] = useState('0');
    const [data, setData] = useState<CouponItem[]>([]);

    const handleOK = () => {
        onChange(data);
        onClose();
    };

    useEffect(() => {
        setData(selectedCoupons);
        seType('0');
    }, []);

    return (
        <Modal title='请选择优惠券' onOk={handleOK} onCancel={onClose} width={1200} open maskClosable={false}>
            <Tabs
                defaultActiveKey='0'
                activeKey={type}
                onChange={key => {
                    seType(key);
                }}
            >
                <Tabs.TabPane tab='优惠券信息' key='0'>
                    {}
                </Tabs.TabPane>
                <Tabs.TabPane tab={`已选：(${data.length || 0})件`} key='1'>
                    {}
                </Tabs.TabPane>
            </Tabs>
            {type === '0' ? (
                <SelectCoupon
                    isNeedShopPhone={isNeedShopPhone}
                    shopPhone={shopPhone}
                    selectedCoupons={data}
                    onChange={(d: CouponItem[]) => {
                        setData(d);
                    }}
                />
            ) : (
                <SelectedCoupon
                    selectedCoupons={data}
                    onChange={(d: CouponItem[]) => {
                        setData(d);
                    }}
                />
            )}
        </Modal>
    );
};

export default SelectCouponModal;
