/**
 * @Owners lvbenlong
 * @Title 商品选择组件
 */
import { type Goods } from '@/api/interface/goods';
import { getCategoryIds } from '@/api/modules/goods';
import { Button, Col, Input, Row, Select, Space } from 'antd';
// import { debounce } from 'lodash';
import { useEffect, useState } from 'react';

export type GoodsFilterParam = {
    spuCodeAndName?: string;
    status?: number;
    goodsType?: number;
    supplierId?: number;
    isShopExclusive?: number;
    rewardPrice?: number;
    rewardAmountMin?: number;
    rewardAmountMax?: number;
    oneFrontCategoryIdList?: number;
};

type Props = {
    goodsStatus: number[];
    rewardEnabled?: number;
    onSearch(param: GoodsFilterParam): void;
};
const goodsPrice = /^(([0-9]+)|([0-9]+\.[0-9]{0,2}))$/;
// type SupplierMap = Goods.Response.getSupplierByNameOrCode;

const GoodsFilter = (props: Props) => {
    const { goodsStatus, rewardEnabled, onSearch } = props;
    const [filter, setFilter] = useState<GoodsFilterParam>({
        // 默认展示正式库
        isShopExclusive: 0,
    });
    // const [supplierMap, setSupplierMap] = useState<SupplierMap>([]);
    const [bCategory, setBCategory] = useState<Goods.Response.getCategoryIdsRes[]>([]);

    useEffect(() => {
        handleGetCategoryIds();
    }, []);

    const handleSearch = () => {
        const newFilter = { ...filter };
        onSearch(newFilter);
    };

    const handleReset = () => {
        setFilter({});
        onSearch({});
    };

    const handleChangeFilter = (key: string, val: number | string) => {
        setFilter({
            ...filter,
            [key]: val,
        });
    };

    // const getSupplierList = async (codeOrName: string) => {
    //     if (!codeOrName) return;
    //     const res = await getSupplierByNameOrCode({ codeOrName });
    //     if (res.code === '0') {
    //         setSupplierMap(res.data as unknown as SupplierMap);
    //     }
    // };

    // 获取商品类目
    const handleGetCategoryIds = async () => {
        const rsp = await getCategoryIds({ type: 1 });
        // console.log(rsp);
        setBCategory(rsp?.data || []);
    };

    return (
        <>
            <Row className='mb-12'>
                <Col span={6}>
                    商品名称：
                    <Input
                        style={{ width: '200px' }}
                        value={filter?.spuCodeAndName}
                        placeholder='请输入商品名称'
                        onChange={e => handleChangeFilter('spuCodeAndName', e.target.value)}
                    />
                </Col>
                <Col span={6}>
                    商品类型：
                    <Select
                        style={{ width: '200px' }}
                        placeholder='请选择商品类型'
                        value={filter?.goodsType}
                        onChange={e => handleChangeFilter('goodsType', e)}
                    >
                        <Select.Option key={1} value={1}>
                            普通商品
                        </Select.Option>
                        <Select.Option key={2} value={2}>
                            跨境商品
                        </Select.Option>
                    </Select>
                </Col>
                <Col span={6}>
                    商品状态：
                    <Select
                        style={{ width: '200px' }}
                        placeholder='请选择商品状态'
                        value={filter?.status}
                        onChange={e => handleChangeFilter('status', e)}
                    >
                        {goodsStatus.includes(3) && (
                            <Select.Option key={3} value={3}>
                                待发布
                            </Select.Option>
                        )}
                        {goodsStatus.includes(4) && (
                            <Select.Option key={4} value={4}>
                                已发布
                            </Select.Option>
                        )}
                    </Select>
                </Col>
                {/* <Col span={6}>
                    供应商名称：
                    <Select
                        style={{ width: '200px' }}
                        filterOption={false}
                        showSearch
                        onSearch={debounce((name: string) => {
                            getSupplierList(name);
                        }, 500)}
                        placeholder='请选择'
                        value={filter?.supplierId}
                        onChange={e => handleChangeFilter('supplierId', e)}
                    >
                        {supplierMap.map(supplier => (
                            <Select.Option key={supplier.id} value={supplier.id || 0}>
                                {supplier.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Col> */}
            </Row>
            <Row className='mb-12'>
                <Col span={6}>
                    商品类目：
                    <Select
                        style={{ width: '200px' }}
                        placeholder='请选择商品类目'
                        showSearch
                        optionFilterProp='label'
                        value={filter?.oneFrontCategoryIdList}
                        onChange={e => handleChangeFilter('oneFrontCategoryIdList', e)}
                        getPopupContainer={triggerNode => (triggerNode as unknown as { parentNode: HTMLElement }).parentNode}
                    >
                        {bCategory.map(category => (
                            <Select.Option key={category.id} value={category.id} label={category.name}>
                                {category.name}
                            </Select.Option>
                        ))}
                    </Select>
                </Col>
                <Col span={6}>
                    会员专享商品：
                    <Select
                        style={{ width: '172px' }}
                        placeholder='请选择'
                        value={filter?.isShopExclusive}
                        allowClear
                        onChange={val => handleChangeFilter('isShopExclusive', val)}
                    >
                        <Select.Option key={1} value={1}>
                            是
                        </Select.Option>
                        <Select.Option key={0} value={0}>
                            否
                        </Select.Option>
                    </Select>
                </Col>
                {!!rewardEnabled && (
                    <Col span={6}>
                        直播奖励：
                        <Input
                            placeholder='请输入'
                            style={{ width: '30%' }}
                            value={filter.rewardAmountMin}
                            maxLength={20}
                            onChange={event => {
                                const value = event.currentTarget.value.trim();
                                // 允许空字符串
                                if (value === '' || !isNaN(+value)) {
                                    if (value === '' || goodsPrice.test(value)) {
                                        handleChangeFilter('rewardAmountMin', value);
                                    }
                                }
                            }}
                        />
                        <span> ~ </span>
                        <Input
                            placeholder='请输入'
                            style={{ width: '30%' }}
                            value={filter.rewardAmountMax}
                            maxLength={20}
                            onChange={event => {
                                console.log('event: ', event.currentTarget.value);
                                const value = event.currentTarget.value.trim();
                                // 允许空字符串
                                if (value === '' || !isNaN(+value)) {
                                    if (value === '' || goodsPrice.test(value)) {
                                        handleChangeFilter('rewardAmountMax', value);
                                    }
                                }
                            }}
                        />{' '}
                        元
                        {/* <Select
                            style={{ width: '172px' }}
                            placeholder='请选择'
                            value={filter?.rewardPrice}
                            allowClear
                            onChange={val => handleChangeFilter('rewardPrice', val)}
                        >
                            <Select.Option key={1} value={1}>
                                有
                            </Select.Option>
                            <Select.Option key={0} value={0}>
                                无
                            </Select.Option>
                        </Select> */}
                    </Col>
                )}
                <Col span={6}>
                    <Space>
                        <Button type='primary' onClick={handleSearch}>
                            搜索
                        </Button>
                        <Button onClick={handleReset}>重置</Button>
                    </Space>
                </Col>
            </Row>
        </>
    );
};

export default GoodsFilter;
