/**
 * @Owners lvbenlong
 * @Title 商品选择组件
 */
import { type Goods } from '@/api/interface/goods';
import { GoodStatusMap } from '@/consts/cGoods';
import { uDecimal } from '@/utils/uDecimal';
import { Button, Col, Popover, Row, Table, Tooltip } from 'antd';
import { type ColumnsType } from 'antd/lib/table';
import { useEffect, useMemo, useState } from 'react';

type GoodsItem = Goods.Response.getGoodsByPage['dataList'][number];
type Props = {
    // 数据加载中
    loading?: boolean;
    // 能否进行选择操作
    isRowSelection?: boolean;
    // 数据
    dataSource: GoodsItem[];
    // 数据总量 处理分页
    total?: number;
    // 选中的数据
    hasCheckedList?: GoodsItem[];
    // 已选中的商品 - 专题选择场景使用
    hasCheckedSpuIds?: number[];
    // 禁选的spu
    disabledSpuIds?: number[];
    // 是否单选
    isSingleSelect?: boolean;
    // 是否开启直播奖励
    rewardEnabled?: number;
    pagination: {
        current: number;
        pageSize: number;
    };
    onChange?(v: GoodsItem[]): void;
    onPageChange?(pageNumber: number, pageSize: number): void;
};

const GoodsList = (props: Props) => {
    const {
        dataSource,
        total,
        disabledSpuIds = [],
        hasCheckedSpuIds = [],
        isSingleSelect = false,
        isRowSelection = true,
        pagination,
        rewardEnabled = 0,
        onPageChange,
        onChange,
    } = props;
    const [checkedList, setCheckedList] = useState<GoodsItem[]>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

    useEffect(() => {
        setSelectedRowKeys(hasCheckedSpuIds);
    }, []);

    const columns = useMemo(
        () =>
            [
                {
                    title: '商品信息',
                    key: 'sku',
                    width: 300,
                    align: 'center',
                    dataIndex: 'sku',
                    render: (_text: string, record: GoodsItem) => (
                        <Row>
                            <div className='goods-info' style={{ display: 'flex', flexDirection: 'row' }}>
                                <img
                                    src={(record.pics && record.pics[0]?.picUrl) || ''}
                                    style={{ width: '65px', height: '65px', marginRight: '10px' }}
                                />
                                <span
                                    style={{
                                        textAlign: 'left',
                                    }}
                                >
                                    {record.spuName}
                                </span>
                            </div>
                        </Row>
                    ),
                },
                {
                    title: '基础信息',
                    key: 'goodsType',
                    width: 150,
                    align: 'center',
                    dataIndex: 'goodsType',
                    render: (_text: string, record: GoodsItem) => (
                        <>
                            <div>商品编码：{record.spuCode || '-'}</div>&nbsp;&nbsp;
                            <div>商品类型：{record.goodsType === 1 ? '普通商品' : '跨境商品'}</div>
                        </>
                    ),
                },
                {
                    title: '状态',
                    key: 'status',
                    width: 100,
                    align: 'center',
                    dataIndex: 'status',
                    render: (text: string) => GoodStatusMap[text as '1'].text,
                },
                {
                    title: '会员专享商品',
                    width: 120,
                    align: 'center',
                    dataIndex: 'isShopExclusive',
                    render: (text: number) => (Boolean(text) ? '是' : '否'),
                },
                {
                    title: '属性',
                    key: 'propertyValuesStr',
                    dataIndex: 'propertyValuesStr',
                    align: 'center',
                    width: 500,
                    render: (_text: string, record: GoodsItem) => (
                        <>
                            {!!record.skus &&
                                !!record.skus.length &&
                                record.skus.slice(0, 2).map(i => (
                                    <Row key={i.skuCode} justify={'start'}>
                                        <Col span={10}>
                                            <Tooltip title={i.propertyValuesStr}>
                                                属性：<span>{i.propertyValuesStr ? i.propertyValuesStr.slice(0, 10) : '--'}</span>
                                            </Tooltip>
                                        </Col>
                                        <Col span={4}>价格：{uDecimal.FenToAmount(i.retailPrice)}</Col>
                                        <Col span={4}>库存：{i.num}</Col>
                                        {rewardEnabled && <Col span={5}>直播奖励：{uDecimal.FenToAmount(i.rewardPrice)}</Col>}
                                    </Row>
                                ))}
                            {!!record.skus && record.skus.length > 2 && (
                                <Popover
                                    content={
                                        <Table
                                            rowKey={'skuId'}
                                            pagination={false}
                                            dataSource={record.skus}
                                            style={{ width: '700px' }}
                                            columns={[
                                                { title: '属性', dataIndex: 'propertyValuesStr', key: 'propertyValuesStr' },
                                                {
                                                    title: '价格',
                                                    dataIndex: 'retailPrice',
                                                    key: 'retailPrice',
                                                    render: (value: number) => uDecimal.FenToAmount(value),
                                                },
                                                { title: '库存', dataIndex: 'num', key: 'num' },
                                                {
                                                    title: '直播奖励',
                                                    dataIndex: 'rewardPrice',
                                                    key: 'rewardPrice',
                                                    render: (value: number) => uDecimal.FenToAmount(value),
                                                    hidden: rewardEnabled ? false : true,
                                                },
                                            ]}
                                        />
                                    }
                                >
                                    <Button type='link'>查看所有规格</Button>
                                </Popover>
                            )}
                        </>
                    ),
                },
            ] as unknown as ColumnsType<GoodsItem>,
        [dataSource]
    );

    const handleSelectChange = (_selectedRowKeys: React.Key[], selectedRows: GoodsItem[]) => {
        if (isSingleSelect) {
            onChange && onChange([selectedRows[0]]);
            setSelectedRowKeys([_selectedRowKeys[0] as number]);
        } else {
            const currentPageKeys = dataSource.map(i => i.spuId);
            const currentPageSelectKeys = selectedRowKeys.filter(i => currentPageKeys.includes(i));
            const isAdd = selectedRows.length > currentPageSelectKeys.length;
            let newCheckedList = [];
            if (isAdd) {
                // 处理新增
                const newAddData = dataSource.filter(
                    i => !currentPageSelectKeys.includes(i.spuId) && _selectedRowKeys.includes(i.spuId)
                );
                newCheckedList = [...checkedList, ...newAddData];
                setCheckedList(newCheckedList);
                setSelectedRowKeys(newCheckedList.map(i => i.spuId));
            } else {
                // 处理删除
                const deleteKeys = currentPageSelectKeys.filter(i => !_selectedRowKeys.includes(i));
                newCheckedList = checkedList.filter(i => !deleteKeys.includes(i.spuId));
                setCheckedList(newCheckedList);
                setSelectedRowKeys(newCheckedList.map(i => i.spuId));
            }
            onChange && onChange(newCheckedList);
        }
    };

    return (
        <Table
            key='spuId'
            rowKey='spuId'
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: true, y: 500 }}
            rowSelection={
                isRowSelection
                    ? {
                          type: isSingleSelect ? 'radio' : 'checkbox',
                          selectedRowKeys,
                          onChange: handleSelectChange,
                          getCheckboxProps: record => ({
                              disabled: disabledSpuIds.includes(record.spuId),
                          }),
                      }
                    : undefined
            }
            pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total,
                position: ['bottomRight'],
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: all => `共 ${all} 条记录`,
                onChange: (pageNumber, pageSize) => {
                    onPageChange && onPageChange(pageNumber, pageSize || 0);
                },
            }}
        />
    );
};

export default GoodsList;
