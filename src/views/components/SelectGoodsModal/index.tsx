/**
 * @Owners lvbenlong
 * @Title 商品选择组件
 */
import { type Goods } from '@/api/interface/goods';
import { getGoodsByPage } from '@/api/modules/goods';
import { Button, Flex, Modal, message } from 'antd';
import { useEffect, useState } from 'react';

import GoodsFilter, { type GoodsFilterParam } from './GoodsFilter';
import GoodsList from './GoodsList';

/**
 * 直播商品上架状态
 */
export enum LiveGoodsUpStatus {
    /** 立即上架 */
    IMMEDIATELY = 1,
    /** 加入待上架 */
    WAIT = 0,
}

type GoodsItem = Goods.Response.getGoodsByPage['dataList'][number];
type Props = {
    liveId: number;
    // 弹框名称
    title?: string;
    // 是否显示过滤框
    showFilterTools?: boolean;
    // 是否单选 默认false 多选
    isSingleSelect?: boolean;
    // 能否进行选择操作
    isRowSelection?: boolean;
    // 仅显示已选择的商品
    // onlyShowCheckedGoods?: boolean;
    //  商品状态选择过滤 默认为[4]，仅查询已发布商品
    goodsStatus?: number[];
    // 已选的商品spuIds
    hasCheckedSpuIds?: number[];
    // 禁选的商品spuIds
    disabledSpuIds?: number[];
    // 所属商品库，0 正式推广库，1 店长专享库，-1 查全部的库（正式推广库、店长专享库、团批商品库）
    isShopExclusive?: number;
    // 是否是上架标识 0 否 1 是
    goodsUpFlag?: number;
    // 是否直播场景
    isLive?: boolean;
    // 奖励是否开启
    rewardEnabled?: number;
    // 选择成功确认回调
    onOk(spus: GoodsItem[]): void;
    // 选择取消回调
    onCancel(): void;
    // 添加直播商品
    onAddLiveGoods?(spus: GoodsItem[], type: number): void;
};

export const SelectGoodsModal = (props: Props) => {
    const {
        liveId,
        title = '选择商品',
        isSingleSelect = false,
        showFilterTools = true,
        isRowSelection = true,
        goodsStatus = [4],
        // hasCheckedSpuIds = [],
        // disabledSpuIds = [],
        goodsUpFlag = undefined,
        // isShopExclusive = 0,
        isLive = false,
        rewardEnabled,
        onOk,
        onCancel,
        onAddLiveGoods,
    } = props;
    const [loading, setLoading] = useState<boolean>(true);
    const [total, setTotal] = useState<number>(0);
    const [dataSource, setDataSource] = useState<GoodsItem[]>([]);
    const [hasCheckedList] = useState<GoodsItem[]>([]);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 20 });
    const [filter, setFilter] = useState<GoodsFilterParam>({
        // 默认展示正式库
        isShopExclusive: 0,
    });
    const [selectGoods, setSelectGoods] = useState<GoodsItem[]>([]);
    const [isSelectedSpuIds, setIsSelectedSpuIds] = useState<number[]>([]);

    useEffect(() => {
        // handleInitSelectedGoods();
    }, []);

    useEffect(() => {
        handleGetData();
    }, [pagination.current, pagination.pageSize, JSON.stringify(filter)]);

    const handleGetData = async () => {
        setLoading(true);
        const res = await getGoodsByPage({
            condition: {
                ...filter,
                liveId,
                status: filter.status ? [filter.status] : goodsStatus,
                oneFrontCategoryIdList: filter.oneFrontCategoryIdList ? [filter.oneFrontCategoryIdList] : undefined,
                goodsUpFlag,
                scene: 6,
            },
            page: pagination.current || 1,
            rows: pagination.pageSize || 20,
        });

        setLoading(false);

        if (res.data) {
            const dataList = res.data.dataList || [];
            const _isSelectedSpuIds = dataList?.filter(v => v.isSelected === 1)?.map(v => v.spuId);
            setIsSelectedSpuIds(_isSelectedSpuIds);
            setDataSource(dataList);
            setTotal(res.data.total ?? 0);
        }
    };

    /* const handleInitSelectedGoods = async () => {
        if (hasCheckedSpuIds.length) {
            // 选择选中的 spuIds 进行默认填充
            // 目前没有这个业务需求
            await getGoodsByPage({
                condition: {
                    liveId,
                    spuIds: hasCheckedSpuIds,
                },
                page: 1,
                rows: 100,
            });
        }
    }; */

    const handleSetSelectGoods = () => {
        if (!selectGoods.length) {
            message.warning('请选择商品');
            return;
        }
        console.log('selectGoods', selectGoods);
        onOk(selectGoods);
    };

    const handleChangePageSize = (pm: number, ps: number) => {
        setPagination({
            current: pm,
            pageSize: ps,
        });
    };
    const handleChange = (v: GoodsItem[]) => {
        console.log('handleChange-checked-spuList', v);
        setSelectGoods(v);
    };

    const handleSearch = (filterParam: GoodsFilterParam) => {
        setPagination({
            pageSize: 20,
            current: 1,
        });
        setFilter(filterParam);
    };

    const handleAddLiveGoods = async (type: number) => {
        if (!selectGoods.length) {
            message.warning('请选择商品');
            return;
        }
        console.log('selectGoods', selectGoods);
        onAddLiveGoods?.(selectGoods, type);
    };

    return (
        <Modal
            title={title}
            width={1500}
            onOk={handleSetSelectGoods}
            onCancel={onCancel}
            maskClosable={false}
            keyboard={false}
            open
            centered
            footer={
                isLive ? (
                    <Flex wrap gap='small' justify='end' className='site-button-ghost-wrapper'>
                        <Button onClick={onCancel}>取消</Button>
                        <Button type='primary' ghost onClick={() => handleAddLiveGoods(LiveGoodsUpStatus.WAIT)}>
                            添加到待上架
                        </Button>
                        <Button type='primary' onClick={() => handleAddLiveGoods(LiveGoodsUpStatus.IMMEDIATELY)}>
                            上架到直播间
                        </Button>
                    </Flex>
                ) : undefined
            }
        >
            {showFilterTools && <GoodsFilter onSearch={handleSearch} rewardEnabled={rewardEnabled} goodsStatus={goodsStatus} />}
            <GoodsList
                pagination={pagination}
                isSingleSelect={isSingleSelect}
                disabledSpuIds={isSelectedSpuIds}
                hasCheckedSpuIds={isSelectedSpuIds}
                isRowSelection={isRowSelection}
                loading={loading || false}
                dataSource={dataSource}
                total={total}
                rewardEnabled={rewardEnabled}
                onPageChange={handleChangePageSize}
                hasCheckedList={hasCheckedList}
                onChange={(spuList: GoodsItem[]) => {
                    handleChange(spuList);
                }}
            />
        </Modal>
    );
};
