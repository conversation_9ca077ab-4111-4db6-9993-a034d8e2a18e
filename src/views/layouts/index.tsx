/**
 * @Owners init
 * @Title layout-组件
 */
// import { getAuthorButtons } from '@/api/modules/login';
import { type RootState, useDispatch, useSelector } from '@/redux';
// import { setAuthButtons } from '@/redux/modules/auth';
import { updateCollapse } from '@/redux/modules/menu';
import KeepAlive from '@/views/components/common/KeepAlive';
import { Layout } from 'antd';
import { useEffect, useMemo } from 'react';

import LayoutContainer from './Style';
// import LayoutFooter from './components/Footer';
import LayoutHeader from './components/Header';
import LayoutMenu from './components/Menu';
// import LayoutTabs from './components/Tabs';

const _screenWidth = 1200;

type IProps = {
    remoteHasUpdate: boolean;
};

const LayoutIndex = (props: IProps) => {
    const dispatch = useDispatch();
    const { isCollapse } = useSelector((state: RootState) => state.menu);
    const { authRouter } = useSelector((state: RootState) => state.auth);

    const { remoteHasUpdate } = props;
    const { Sider, Content } = Layout;

    // 获取按钮权限列表
    // const getAuthButtonsList = async () => {
    //     const { data } = await getAuthorButtons();
    //     data && dispatch(setAuthButtons(data));
    // };

    // 监听窗口大小变化
    const listeningWindow = () => {
        window.onresize = () =>
            (() => {
                const screenWidth = document.body.clientWidth;
                if (!isCollapse && screenWidth < _screenWidth) dispatch(updateCollapse(true));
                if (!isCollapse && screenWidth > _screenWidth) dispatch(updateCollapse(false));
            })();
    };

    useEffect(() => {
        listeningWindow();
        // getAuthButtonsList();
    }, []);

    const renderContent = useMemo(
        () => <Content>{remoteHasUpdate ? null : <KeepAlive includes={authRouter} excludes={['']} />}</Content>,
        [remoteHasUpdate, authRouter]
    );

    return (
        // 这里不用 Layout 组件原因是切换页面时样式会先错乱然后在正常显示，造成页面闪屏效果
        <LayoutContainer className='container'>
            <Sider trigger={null} collapsed={isCollapse} width={180}>
                <LayoutMenu />
            </Sider>
            <Layout>
                <LayoutHeader />
                {/* <LayoutTabs /> */}
                {renderContent}
                {/* <LayoutFooter /> */}
            </Layout>
        </LayoutContainer>
    );
};

export default LayoutIndex;
