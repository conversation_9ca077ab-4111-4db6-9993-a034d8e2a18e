/**
 * @Owners zp
 * @Title layout-样式
 */
import { Layout } from 'antd';
import styled from 'styled-components';

const LayoutContainer = styled(Layout)`
    display: flex;
    min-width: 950px;
    height: 100%;
    .ant-layout-sider-children {
        height: 100%;
        .ant-spin-text {
            text-shadow: none !important;
        }
    }
    .ant-layout-sider {
        background-color: transparent;
        .logo-box {
            background-color: transparent;
        }
    }
    .ant-layout {
        /* 防止 tabs 超出不收缩 */
        overflow-x: hidden;
        .ant-layout-content {
            box-sizing: border-box;
            flex: 1;
            padding: 12px 12px;
            overflow-x: hidden;
            margin-right: 3px;
        }
    }
`;
export default LayoutContainer;
