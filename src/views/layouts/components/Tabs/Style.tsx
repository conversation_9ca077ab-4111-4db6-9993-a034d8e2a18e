/**
 * @Owners zp
 * @Title tabs-样式
 */

import styled from 'styled-components';

const TabsContainer = styled.div`
    position: relative;
    margin-top: 12px;

    .ant-tabs {
        padding: 0 90px 0 13px;
        .ant-tabs-nav {
            margin: 0;
            &::before {
                border: none;
            }
            .ant-tabs-ink-bar {
                visibility: visible;
            }
            .ant-tabs-tab-with-remove.ant-tabs-tab-active {
                .ant-tabs-tab-remove {
                    top: 1px;
                    margin: 4px;
                    color: ${props => props.theme.colors.primaryColor}!important;
                    opacity: 1 !important;
                }
                .ant-tabs-tab-btn {
                    transform: translateX(-9px);
                }
            }
            .ant-tabs-nav-list {
                .ant-tabs-tab {
                    padding: 8px 22px;
                    color: #86909c;
                    background: ${props => props.theme.colors.tabBgColor};
                    border: none;
                    transition: none;
                    margin: 0 4px !important;
                    border-bottom: none !important;
                    &:first-child {
                        margin-left: 0 !important;
                    }
                    .anticon-home {
                        margin-right: 7px;
                    }
                    &:hover {
                        color: ${props => props.theme.colors.primaryColor};
                        .ant-tabs-tab-remove {
                            color: ${props => props.theme.colors.primaryColor};
                        }
                    }
                    .ant-tabs-tab-remove {
                        position: absolute;
                        right: 0;
                        opacity: 0;
                        transition: 0.1s ease-in-out;
                        &:hover {
                            color: inherit;
                        }
                    }
                }
            }

            .ant-tabs-tab.ant-tabs-tab-with-remove {
                &:hover {
                    .ant-tabs-tab-remove {
                        top: 1px;
                        margin: 4px;
                        opacity: 1;
                        transition: 0.1s ease-in-out;
                    }
                    .ant-tabs-tab-btn {
                        transform: translateX(-9px);
                    }
                }
            }
        }
    }
    .more-button {
        position: absolute;
        top: 8px;
        right: 13px;
        padding-left: 10px;
        font-size: 12px;
    }

    /* tabs 超出显示的样式 */
    .ant-tabs-dropdown {
        .ant-tabs-dropdown-menu-item {
            .anticon-home {
                margin-right: 7px;
            }
        }
    }

    /* tabs 不受全局组件大小影响 */
    .ant-tabs-small > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-large > .ant-tabs-nav .ant-tabs-tab {
        padding: 8px 22px !important;
        font-size: 14px !important;
    }
`;

export default TabsContainer;
