/**
 * @Owners zp
 * @Title tabs-组件
 */
import { HOME_URL } from '@/config/config';
import { type RootState, useDispatch, useSelector } from '@/redux';
import { type TabListItem } from '@/redux/interface';
import { setTabsList } from '@/redux/modules/tabs';
import pagesRouter from '@/routers/pagesRouter';
import { HomeFilled } from '@ant-design/icons';
import { searchRoute } from '@utils';
import { Tabs } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import TabsContainer from './Style';
import MoreButton from './components/MoreButton';

const LayoutTabs = () => {
    const dispatch = useDispatch();
    const { tabsList } = useSelector((state: RootState) => state.tabs);
    const { pathname } = useLocation();
    const navigate = useNavigate();
    const [activeValue, setActiveValue] = useState<string>(pathname);

    const curPathname = useMemo(() => pathname, [pathname]);

    useEffect(() => {
        addTabs();
    }, [pathname]);

    // click tabs
    const clickTabs = (path: string) => {
        navigate(path);
    };

    // add tabs
    const addTabs = () => {
        let routeInfo: { name?: string; path?: string } = {};
        const route = searchRoute(curPathname, pagesRouter);
        routeInfo = { name: route.meta?.title, path: route.path };
        const newTabsList = JSON.parse(JSON.stringify(tabsList));
        if (tabsList.every(item => item.path !== routeInfo.path)) {
            newTabsList.push(routeInfo);
        }
        dispatch(setTabsList(newTabsList));
        setActiveValue(curPathname);
    };

    // delete tabs
    const delTabs = (tabPath: string) => {
        if (tabPath === HOME_URL) return;
        if (curPathname === tabPath) {
            tabsList.forEach((item: TabListItem, index: number) => {
                if (item.path !== curPathname) return;
                const nextTab = tabsList[index + 1] || tabsList[index - 1];
                if (!nextTab) return;
                navigate(nextTab.path);
            });
        }
        dispatch(setTabsList(tabsList.filter((item: TabListItem) => item.path !== tabPath)));
    };

    return (
        <TabsContainer>
            <Tabs
                animated
                activeKey={activeValue}
                onChange={clickTabs}
                hideAdd
                type='editable-card'
                onEdit={path => {
                    delTabs(path as string);
                }}
                items={tabsList.map(item => ({
                    label: (
                        <span>
                            {item.path === HOME_URL ? <HomeFilled /> : ''}
                            {item.name}
                        </span>
                    ),
                    key: item.path,
                    closable: item.path !== HOME_URL,
                }))}
            />
            <MoreButton delTabs={delTabs} />
        </TabsContainer>
    );
};

export default LayoutTabs;
