/**
 * @Owners init
 * @Title 更多按钮-组件
 */

import { HOME_URL } from '@/config/config';
import { type RootState, useDispatch, useSelector } from '@/redux';
import { setTabsList } from '@/redux/modules/tabs';
import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown, type MenuProps } from 'antd';
import { type FC } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

interface IProps {
    delTabs(pathname: string): void;
}

const MoreButton: FC<IProps> = props => {
    const { delTabs } = props;
    const dispatch = useDispatch();
    const { tabsList } = useSelector((state: RootState) => state.tabs);

    const { pathname } = useLocation();
    const navigate = useNavigate();

    // close multipleTab
    const closeMultipleTab = (tabPath?: string) => {
        const newTabsList = tabsList.filter(item => item.path === tabPath || item.path === HOME_URL);
        dispatch(setTabsList(newTabsList));
        if (!tabPath) navigate(HOME_URL);
    };

    const menuProps: MenuProps = {
        items: [
            {
                key: '1',
                label: <span>关闭当前</span>,
                onClick: () => delTabs(pathname),
            },
            {
                key: '2',
                label: <span>关闭其他</span>,
                onClick: () => closeMultipleTab(pathname),
            },
            {
                key: '3',
                label: <span>关闭所有</span>,
                onClick: () => closeMultipleTab(''),
            },
        ],
    };

    return (
        <Dropdown menu={menuProps} placement='bottom' arrow={{ pointAtCenter: true }} trigger={['click']}>
            <Button className='more-button' type='primary' size='small'>
                更多 <DownOutlined />
            </Button>
        </Dropdown>
    );
};
export default MoreButton;
