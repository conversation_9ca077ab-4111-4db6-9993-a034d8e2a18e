/**
 * @Owners init
 * @Title 全局组件大小切换
 */
import { type SysMenu } from '@/api/interface/system';
import { type RootState, useDispatch, useSelector } from '@/redux';
import { setAssemblySize } from '@/redux/modules/global';
import { Dropdown } from 'antd';
import type { SizeType } from 'antd/lib/config-provider/SizeContext';

const AssemblySize = () => {
    const dispatch = useDispatch();
    const { assemblySize } = useSelector((state: RootState) => state.global);

    // 切换组件大小
    const onClick = (e: SysMenu.MenuInfo) => {
        dispatch(setAssemblySize(e.key as SizeType));
    };

    const menu = [
        {
            key: 'middle',
            disabled: assemblySize === 'middle',
            label: <span>默认</span>,
            onClick,
        },
        {
            disabled: assemblySize === 'large',
            key: 'large',
            label: <span>大型</span>,
            onClick,
        },
        {
            disabled: assemblySize === 'small',
            key: 'small',
            label: <span>小型</span>,
            onClick,
        },
    ];

    return (
        <Dropdown menu={{ items: menu }} placement='bottom' trigger={['click']} arrow>
            <i className='icon-style iconfont icon-contentright' />
        </Dropdown>
    );
};

export default AssemblySize;
