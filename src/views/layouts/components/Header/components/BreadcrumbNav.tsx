/**
 * @Owners init
 * @Title 面包屑
 */
import { HOME_URL } from '@/config/config';
import { type RootState, useSelector } from '@/redux';
import { Breadcrumb } from 'antd';
import { useLocation } from 'react-router-dom';

const BreadcrumbNav = () => {
    const { pathname } = useLocation();
    const breadcrumbState = useSelector((state: RootState) => state.breadcrumb);
    const breadcrumbList = breadcrumbState.breadcrumbList[pathname] || [];

    return (
        <Breadcrumb>
            <Breadcrumb.Item href={HOME_URL}>首页</Breadcrumb.Item>
            {breadcrumbList.map((item: string) => (
                <Breadcrumb.Item key={item}>{item !== '首页' ? item : null}</Breadcrumb.Item>
            ))}
        </Breadcrumb>
    );
};

export default BreadcrumbNav;
