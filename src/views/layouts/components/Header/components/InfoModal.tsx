/**
 * @Owners init
 * @Title 修改个人信息
 */
import { Modal, message } from 'antd';
import { type Ref, useImperativeHandle, useState } from 'react';

interface Props {
    innerRef: Ref<{ showModal(params: unknown): void } | undefined>;
}

const InfoModal = (props: Props) => {
    const [modalVisible, setModalVisible] = useState(false);

    useImperativeHandle(props.innerRef, () => ({
        showModal,
    }));

    const showModal = (params: { name: number }) => {
        console.log(params);
        setModalVisible(true);
    };

    const handleOk = () => {
        setModalVisible(false);
        message.success('修改用户信息成功 🎉🎉🎉');
    };

    const handleCancel = () => {
        setModalVisible(false);
    };
    return (
        <Modal title='个人信息' open={modalVisible} onOk={handleOk} onCancel={handleCancel} destroyOnClose>
            <p>User Info...</p>
            <p>User Info...</p>
            <p>User Info...</p>
        </Modal>
    );
};
export default InfoModal;
