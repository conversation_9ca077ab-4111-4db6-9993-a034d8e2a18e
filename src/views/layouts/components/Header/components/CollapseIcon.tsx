/**
 * @Owners init
 * @Title 侧边栏收缩
 */
import { type RootState, useDispatch, useSelector } from '@/redux';
import { updateCollapse } from '@/redux/modules/menu';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';

const CollapseIcon = () => {
    const dispatch = useDispatch();
    const { isCollapse } = useSelector((state: RootState) => state.menu);

    return (
        <div
            className='collapsed'
            onClick={() => {
                dispatch(updateCollapse(!isCollapse));
            }}
        >
            {isCollapse ? <MenuUnfoldOutlined id='isCollapse' /> : <MenuFoldOutlined id='isCollapse' />}
        </div>
    );
};

export default CollapseIcon;
