/**
 * @Owners haoyang
 * @Title 用户信息-样式
 */

import styled from 'styled-components';

interface IProps {
    colorHue: number;
    size: number;
}

const Container = styled.div<IProps>`
    display: flex;
    align-items: center;
    overflow: hidden;
    border-radius: 468px;

    .username {
        display: flex;
        align-items: center;
        height: 24px;
        .text {
            margin: 0 4px;
            font-size: 14px;
        }
        .icon {
            font-size: 12px;
        }
    }
`;

export default Container;
