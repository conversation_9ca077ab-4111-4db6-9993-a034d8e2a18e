/**
 * @Owners haoyang
 * @Title 用户信息-组件
 */
import { logout } from '@/api/modules/system/user';
import logoCircle from '@/assets/images/logo-circle.png';
import { store, useDispatch, useSelector } from '@/redux';
import { setAvatarTheme as setAvatarTheme, setToken, setUserInfo } from '@/redux/modules/global';
import { modal } from '@/views/components/UseAppPrompt';
import { DownOutlined, ExclamationCircleOutlined, LogoutOutlined } from '@ant-design/icons';
import { uCookies } from '@utils';
import { Avatar, Dropdown, type MenuProps } from 'antd';
import { memo, type FC, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import Container from './style';

/** 头像尺寸 */
const size = 32;
/** 头像默认色相 */
const defaultHue = 210;

/**
 * 用户信息-组件
 */
const UserInfo: FC = memo(() => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { userInfo, avatarTheme } = useSelector(state => state.global);
    const { nickname, avatar } = userInfo;

    // 退出登录
    const handleLogout = () => {
        modal.confirm({
            icon: <ExclamationCircleOutlined />,
            content: '您确定要退出登录?',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
                const res = await logout({ loginId: userInfo.loginId || 0 });
                if (res.code === '0' && res.data) {
                    // 清除 token和用户信息
                    store.dispatch(setToken(''));
                    store.dispatch(setUserInfo({}));
                    uCookies.remove('token');
                    navigate('/login');
                }
            },
        });
    };

    /** 颜色色相 */
    const colorHue = avatarTheme?.colorHue ?? defaultHue;

    // 初始化颜色
    useEffect(() => {
        if (avatarTheme?.colorHue === undefined) {
            dispatch(
                setAvatarTheme({
                    colorHue: defaultHue,
                })
            );
        }
    }, []);

    /** 下拉菜单 */
    const menu: MenuProps['items'] = [
        {
            key: '0',
            label: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <img style={{ height: '36px', width: '36px', marginRight: '8px', borderRadius: '50%' }} src={logoCircle} />
                    <div>
                        <div style={{ minWidth: '120px' }}>{userInfo?.nickname}</div>
                        <div style={{ minWidth: '120px' }}>{userInfo?.phone}</div>
                    </div>
                </div>
            ),
        },
        {
            key: '4',
            label: (
                <div style={{ color: '#FF4D4F' }}>
                    <LogoutOutlined />
                    <span style={{ marginLeft: '8px' }}>退出登录</span>
                </div>
            ),
            onClick: handleLogout,
        },
    ];

    return (
        <Container colorHue={colorHue} size={size}>
            {/** 用户名 */}
            {/** 下拉菜单 */}
            <Dropdown menu={{ items: menu }} placement='bottom' arrow>
                {/* 头像 */}
                <span className='username'>
                    <Avatar className='user-avatar' size={24} src={avatar} />
                    <span className='text'>{nickname}</span>
                    <DownOutlined className='icon' />
                </span>
            </Dropdown>
        </Container>
    );
});

export default UserInfo;
