/**
 * @Owners haoyang
 * @Title 头部-组件
 */

import HeaderContainer from './Style';
// import AssemblySize from './components/AssemblySize';
// import Fullscreen from './components/Fullscreen';
import BreadcrumbNav from './components/BreadcrumbNav';
import CollapseIcon from './components/CollapseIcon';
import UserInfo from './components/UserInfo';

const LayoutHeader = () => (
    <HeaderContainer>
        <div className='header-lf'>
            <CollapseIcon />
            <BreadcrumbNav />
        </div>
        <div className='header-ri'>
            {/* <AssemblySize /> */}
            {/* <Fullscreen /> */}
            <UserInfo />
        </div>
    </HeaderContainer>
);

export default LayoutHeader;
