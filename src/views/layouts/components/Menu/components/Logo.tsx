/**
 * @Owners zp
 * @Title logo-组件
 */
import home_logo from '@/assets/images/home_logo.png';
import logo from '@/assets/images/logo.png';
import { HOME_URL } from '@/config/config';
import { useNavigate } from 'react-router-dom';

const Logo = ({ isCollapse }: { isCollapse: boolean }) => {
    const navigate = useNavigate();
    const goHome = () => navigate(HOME_URL);
    return (
        <div className='logo-box' onClick={goHome}>
            <img src={logo} alt='logo' className='logo-img' style={{ width: !isCollapse ? '0' : '30px' }} />
            <img src={home_logo} alt='logo' className='logo-img' style={{ width: isCollapse ? '0' : '180px' }} />
        </div>
    );
};

export default Logo;
