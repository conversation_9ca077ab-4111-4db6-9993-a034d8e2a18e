/**
 * @Owners zp
 * @Title 菜单-样式
 */

import styled from 'styled-components';

const MenuContainer = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    .logo-box {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 55px;
        .logo-img {
            transition: all 0.3s;
            cursor: pointer;
            margin: 0;
        }
        .logo-text {
            font-size: 24px;
            color: #dadada;
            white-space: nowrap;
        }
    }
    .ant-menu-root {
        flex: 1;
        overflow-x: hidden;
        overflow-y: auto;
        background-color: transparent;
        padding-left: 6px;

        .ant-menu-item,
        .ant-menu-submenu,
        .ant-menu-submenu-title {
            border-radius: 6px;
            margin-left: 0 !important;
            width: 100%;
            color: #4e5969;
        }
        .ant-menu-sub.ant-menu-inline {
            background-color: inherit;
        }

        /* 目录 */
        .ant-menu-submenu-title {
            color: #4e5969;
        }
        .ant-menu-submenu-title:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected) {
            background-color: rgba(${props => props.theme.colors.primaryColorRgba}, 0.5) !important;
            color: inherit !important;
        }
        .ant-menu-submenu-selected > .ant-menu-submenu-title {
            color: ${props => props.theme.colors.primaryColor} !important;
        }
        /* 子菜单 */
        .ant-menu-item:not(.ant-menu-item-selected):hover {
            background-color: rgba(${props => props.theme.colors.primaryColorRgba}, 0.5) !important;
            color: #4e5969 !important;
        }
        .ant-menu-item-selected {
            background-color: white; //${props => props.theme.colors.primaryColor};
            color: #00b68f;
            border: 1px solid #00b68f;
        }
    }

    /* 去除菜单 Loading 遮罩层 */
    .ant-spin-nested-loading,
    .ant-spin-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        .ant-spin {
            max-height: 100% !important;
        }
        .ant-spin-container::after {
            background: transparent !important;
        }
        .ant-spin-blur {
            overflow: auto !important;
            clear: none !important;
            opacity: 1 !important;
        }
    }
`;

export default MenuContainer;
