/**
 * @Owners init
 * @Title 菜单-组件
 */
import type { SysMenu } from '@/api/interface/system';
import menuJson from '@/assets/json/menu.json';
import { type RootState, useDispatch, useSelector } from '@/redux';
import { setAuthRouter } from '@/redux/modules/auth';
import { setBreadcrumbList } from '@/redux/modules/breadcrumb';
import { setMenuList as reduxSetMenuList } from '@/redux/modules/menu';
import { createMenuNormalData, findAllBreadcrumb, getIconByName, getOpenKeys, handleRouter, searchRoute } from '@utils';
import { Menu, Spin, type MenuProps } from 'antd';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import MenuContainer from './Style';
import Logo from './components/Logo';

// 定义 menu 类型
type MenuComItem = Required<MenuProps>['items'][number];
// 后端返回菜单类型
type UserInfoMenu = SysMenu.MenuItem;

const LayoutMenu = () => {
    const dispatch = useDispatch();
    const { isCollapse, menuList: reduxMenuList } = useSelector((state: RootState) => state.menu);

    const { pathname } = useLocation();
    const [selectedKeys, setSelectedKeys] = useState<string[]>([pathname]);
    const [openKeys, setOpenKeys] = useState<string[]>([]);
    // 获取菜单列表并处理成 antd menu 需要的格式
    const [menuList, setMenuList] = useState<MenuComItem[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        getMenuData();
    }, []);

    // 刷新页面菜单保持高亮
    useEffect(() => {
        const path = pathname;
        let key = [];
        key = getOpenKeys(path);
        !isCollapse && setOpenKeys(key);
        setSelectedKeys([path]);
    }, [pathname, isCollapse]);

    // 设置当前展开的 subMenu
    const onOpenChange = (_openKeys: string[]) => {
        if (_openKeys.length === 0 || _openKeys.length === 1) return setOpenKeys(_openKeys);
        const latestOpenKey = _openKeys[_openKeys.length - 1];
        if (latestOpenKey.includes(_openKeys[0])) return setOpenKeys(_openKeys);
        setOpenKeys([latestOpenKey]);
    };

    const getItem = (item: {
        label: React.ReactNode;
        type?: 'group';
        key?: React.Key;
        icon?: React.ReactNode;
        children?: MenuComItem[];
    }) =>
        ({
            ...item,
            children: item.children || undefined,
        } as MenuComItem);

    // 处理后台返回菜单 key 值为 antd 菜单需要的 key 值
    const deepLoopFloat = (_menuList: UserInfoMenu[], newArr: MenuComItem[] = []) => {
        _menuList
            .filter(i => !i.hidden)
            .forEach((item: UserInfoMenu) => {
                // 下面判断代码解释 *** !item?.children?.length   ==>   (!item.children || item.children.length === 0)
                if (!item?.children?.length) {
                    return newArr.push(getItem({ label: item.name, key: item.path, icon: getIconByName(item.icon) }));
                }
                newArr.push(
                    getItem({
                        label: item.name,
                        key: item.path,
                        icon: getIconByName(item.icon),
                        children: deepLoopFloat(item?.children),
                    })
                );
            });
        return newArr;
    };

    const getMenuData = async () => {
        setLoading(true);
        try {
            if (!menuJson) return;
            const newData = createMenuNormalData(menuJson as UserInfoMenu[]);
            setMenuList(deepLoopFloat(newData));
            // 存储处理过后的所有面包屑导航栏到 redux 中
            dispatch(setBreadcrumbList(findAllBreadcrumb(newData)));
            // 把路由菜单处理成一维数组，存储到 redux 中，做菜单权限判断
            const dynamicRouter = handleRouter(newData);
            dispatch(setAuthRouter(dynamicRouter));
            dispatch(reduxSetMenuList(newData));
        } finally {
            setLoading(false);
        }
    };

    // 点击当前菜单跳转页面
    const navigate = useNavigate();
    const clickMenu: MenuProps['onClick'] = ({ key }: { key: string }) => {
        if (pathname === key) return;
        const route = searchRoute(key, reduxMenuList);
        if (route.isLink) window.open(route.isLink, '_blank');
        navigate(key);
    };

    return (
        <MenuContainer>
            <Spin spinning={loading} tip='Loading...'>
                <Logo isCollapse={isCollapse} />
                <Menu
                    mode='inline'
                    triggerSubMenuAction='click'
                    openKeys={openKeys}
                    selectedKeys={selectedKeys}
                    items={menuList}
                    onClick={clickMenu}
                    onOpenChange={onOpenChange}
                />
            </Spin>
        </MenuContainer>
    );
};

export default LayoutMenu;
