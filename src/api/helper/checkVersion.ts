/**
 * @Owners zp
 * @Title 检查是否同一版本
 */
import { isDev } from '@/utils/uEnv';
import { modal } from '@/views/components/UseAppPrompt';
import { type AxiosResponseHeaders } from 'axios';
import React from 'react';
interface AllVersion {
    serverAppVersion: string | null;
    clientAppVersion?: string;
}

/**
 * 获取新老两个版本
 * @param headers header数据
 * @returns AllVersion
 */
const getVersion = async (headers?: AxiosResponseHeaders): Promise<AllVersion> => {
    try {
        let serverAppVersion;
        if (headers) {
            // 从接口获取
            serverAppVersion = headers['client-version']?.trim();
        } else {
            // 从静态资源获取
            // const res = await fetch(`${window.location.protocol}//${window.location.host}`, {
            //     method: 'HEAD',
            //     cache: 'no-cache',
            // });
            // serverAppVersion = res?.headers?.get('Client-Version');
            serverAppVersion = '';
        }
        const meta = (document.getElementsByTagName('meta') as { version?: { content?: string } })?.version;
        const clientAppVersion = meta?.content?.replace('/', '')?.trim();

        return { serverAppVersion, clientAppVersion };
    } catch (error) {
        console.error(`getVersion: ${error}`);
        return { serverAppVersion: '', clientAppVersion: '' };
    }
};

/**
 * 比较两个版本是否不同
 * @param versions AllVersion
 * @returns boolean
 */
const compareVersion = (versions: AllVersion) => {
    const { serverAppVersion, clientAppVersion } = versions;
    return serverAppVersion && clientAppVersion && serverAppVersion !== '*' && serverAppVersion !== clientAppVersion;
};

/**
 * 版本已更新
 * @returns boolean
 */
const versionHasUpdate = async (headers?: AxiosResponseHeaders) => !isDev() && compareVersion(await getVersion(headers));

/**
 * 提示刷新的弹窗
 */
const reloadModal = () => {
    if (document.querySelector('#reload-modal')) return;
    modal.warning({
        title: '温馨提示 🧡',
        content: React.createElement('span', { id: 'reload-modal' }, '当前程序已更新，请点击更新获取最新版本'),
        okText: '更新',
        maskClosable: false,
        keyboard: false,
        onOk: () => {
            location.reload();
        },
    });
};

/**
 * 当前页面是否已缓存
 * @pathname 当前页面路由
 * @returns boolean
 */
const currentPageHasCache = (pathname: string): boolean => {
    try {
        // 检查节点是否已缓存
        const nodeCache = document.querySelector('.ant-layout-content')?.children.item(0)?.children;
        // eslint-disable-next-line @typescript-eslint/tslint/config, @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return Array.from(nodeCache || []).some(item => item?.attributes['data-page-path']?.value?.includes(pathname));
    } catch (error) {
        console.error(`currentPageHasCache:${error}`);
        return false;
    }
};

export { versionHasUpdate, reloadModal, currentPageHasCache };
