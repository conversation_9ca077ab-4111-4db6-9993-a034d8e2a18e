/**
 * @Owners haoyang
 * @Title 校验网络请求状态码
 */

import { message } from 'antd';

enum Status {
    Bad_Request = 400,
    Unauthorized = 401,
    Forbidden = 403,
    Not_Found = 404,
    Method_Not_Allowed = 405,
    Request_Timeout = 408,
    Internal_Server_Error = 500,
    Bad_Gateway = 502,
    Service_Unavailable = 503,
    Gateway_Timeout = 504,
}

/**
 * @description: 校验网络请求状态码
 * @param status 状态码
 * @return void
 */
export const checkStatus = (status: number): void => {
    switch (status) {
        case Status.Bad_Request:
            message.error('请求失败！请您稍后重试');
            break;
        case Status.Unauthorized:
            message.error('登录失效！请您重新登录');
            break;
        case Status.Forbidden:
            message.error('当前账号无权限访问！');
            break;
        case Status.Not_Found:
            message.error('你所访问的资源不存在！');
            break;
        case Status.Method_Not_Allowed:
            message.error('请求方式错误！请您稍后重试');
            break;
        case Status.Request_Timeout:
            message.error('请求超时！请您稍后重试');
            break;
        case Status.Internal_Server_Error:
            message.error('服务异常！');
            break;
        case Status.Bad_Gateway:
            message.error('网关错误！');
            break;
        case Status.Service_Unavailable:
            message.error('服务不可用！');
            break;
        case Status.Gateway_Timeout:
            message.error('网关超时！');
            break;
        default:
            message.error('请求失败！');
    }
};
