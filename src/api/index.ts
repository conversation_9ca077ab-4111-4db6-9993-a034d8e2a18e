/**
 * @Owners haoyang
 * @Title 请求管理器
 */
import { tryHideFullScreenLoading } from '@/config/serviceLoading';
import { store } from '@/redux';
import { setToken, setUserInfo } from '@/redux/modules/global';
import { cConfig } from '@consts';
import { hProgress } from '@helpers';
import { message } from 'antd';
import axios, { type AxiosError, type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios';

import { AxiosCanceler } from './helper/axiosCancel';
import { checkStatus } from './helper/checkStatus';
import { reloadModal, versionHasUpdate } from './helper/checkVersion';
import { CodeEnum, type ResultData } from './interface/request';

const axiosCanceler = new AxiosCanceler();
const baseConfig = {
    // 默认地址请求地址，可在 .env 开头文件中修改
    baseURL: import.meta.env.VITE_API_HOST,
    // 设置超时时间（10s）
    timeout: 10000,
    // 跨域时候允许携带凭证
    withCredentials: true,
};

/**
 * 请求管理器
 */
class RequestHttp {
    public constructor(_config: AxiosRequestConfig) {
        // 实例化axios
        this.service = axios.create(_config);
        /**
         * @description 请求拦截器
         * 客户端发送请求 -> [请求拦截器] -> 服务器
         * token校验(JWT) : 接受服务器返回的token,存储到redux/本地储存当中
         */
        this.service.interceptors.request.use(
            config => {
                // 不需要显示 loading时 也不显示进度条
                config.headers?.noLoading || hProgress.start();
                //  将当前请求添加到 pending 中
                axiosCanceler.addPending(config);
                // 如果当前请求不需要显示 loading,在api服务中通过指定的第三个参数: { headers: { noLoading: true } }来控制不显示loading，参见loginApi
                // config.headers?.noLoading || showFullScreenLoading();
                const { userInfo, token } = store.getState().global;
                const isGray = window.location.host.includes(cConfig.LOCAL_GRAY_KEY);
                const obj = {
                    token,
                    liveUserId: userInfo.liveUserId,
                    loginId: userInfo.loginId,
                    grayReleaseTag: isGray ? 'gray' : '',
                };
                const filteredObj = Object.fromEntries(
                    Object.entries(obj).filter(
                        ([_key, value]) => value !== null && value !== undefined && String(value).length !== 0
                    )
                );

                return {
                    ...config,
                    headers: {
                        ...config.headers,
                        ...filteredObj,
                    },
                };
            },
            (error: AxiosError) => Promise.reject(error)
        );

        /**
         * @description 响应拦截器
         * 服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
         */
        this.service.interceptors.response.use(
            async (response: AxiosResponse<{ code: CodeEnum; msg: string }, unknown>) => {
                const { data, config, headers = {} } = response;
                // 进度条关闭
                hProgress.done();
                // 在请求结束后，移除本次请求(关闭loading)
                axiosCanceler.removePending(config);
                tryHideFullScreenLoading();

                // 登录状态失效
                if (data.code === CodeEnum.LOGIN_EXPIRED) {
                    message.error(data.msg);
                    // 清除登录态，触发守卫重定向登录页
                    store.dispatch(setToken(''));
                    store.dispatch(setUserInfo({}));
                    return Promise.reject(data);
                }

                // 全局错误信息拦截（防止下载文件得时候返回数据流，没有code，直接报错）
                if (data.code && data.code !== CodeEnum.SUCCESS) {
                    if (config?.headers?.noErrorTips !== true) {
                        message.error(data.msg);
                    }
                    return Promise.reject(data);
                }

                // 检查版本是否相同
                if (await versionHasUpdate(headers)) {
                    reloadModal();
                }

                // 成功请求（在页面上除非特殊情况，否则不用处理失败逻辑）
                return data;
            },
            async (error: AxiosError) => {
                const { response } = error;
                // 进度条关闭
                hProgress.done();
                tryHideFullScreenLoading();
                // 请求超时单独判断，请求超时没有 response
                if (error.message.indexOf('timeout') !== -1) message.error('请求超时，请稍后再试');
                // 根据响应的错误状态码，做不同的处理
                if (response) checkStatus(response.status);
                // 服务器结果都没有返回(可能服务器错误可能客户端断网) 断网处理:可以跳转到断网页面
                if (!window.navigator.onLine) window.location.hash = '/500';
                return Promise.reject(error);
            }
        );
    }

    public service: AxiosInstance;

    // 常用请求方法封装
    public get<T>(url: string, params?: {}, _object = {}): Promise<ResultData<T>> {
        return this.service.get(url, { params, ..._object });
    }

    public post<T>(url: string, params?: {}, _object = {}): Promise<ResultData<T>> {
        return this.service.post(url, params, _object);
    }

    public put<T>(url: string, params?: {}, _object = {}): Promise<ResultData<T>> {
        return this.service.put(url, params, _object);
    }

    public delete<T>(url: string, params?: unknown, _object = {}): Promise<ResultData<T>> {
        return this.service.delete(url, { params, ..._object });
    }
}

interface SSEOptions {
    url: string;
    method?: 'GET' | 'POST';
    data?: unknown;
    onMessage(data: unknown): void;
    onError?(error: unknown): void;
    onCancel?(): void;
}

export function requestSSE({ url, method = 'POST', data, onMessage, onError, onCancel }: SSEOptions) {
    const xhr = new XMLHttpRequest();
    const fullUrl = `${import.meta.env.VITE_API_HOST}${url}`;

    xhr.open(method, fullUrl, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    const isGray = window.location.host.includes(cConfig.LOCAL_GRAY_KEY);
    xhr.setRequestHeader('grayReleaseTag', isGray ? 'gray' : '');

    let lastLength = 0;
    let buffer = ''; // 用于存储未完整的 JSON 数据

    xhr.onprogress = () => {
        const newData = xhr.responseText.substring(lastLength); // 只取新增部分
        lastLength = xhr.responseText.length; // 更新已读取数据的长度
        buffer += newData.trim(); // 累加新数据

        // 正确拆分数据流（如果有多个 JSON 片段）
        let match;
        const regex = /data:\s*({.*?})/g; // 提取 JSON 格式数据
        // eslint-disable-next-line @typescript-eslint/tslint/config, no-cond-assign
        while ((match = regex.exec(buffer)) !== null) {
            try {
                const parsedData = JSON.parse(match[1]); // 解析 JSON
                onMessage(parsedData); // 传递给回调
            } catch (e) {
                console.error('解析SSE数据失败:', e);
            }
        }

        // 清理已经处理过的数据，避免 buffer 无限增长
        buffer = buffer.slice(buffer.lastIndexOf('}') + 1);
    };

    xhr.onerror = error => {
        onError?.(error);
    };

    xhr.send(data ? JSON.stringify(data) : null);

    return () => {
        xhr.abort();
        onCancel?.();
    };
}

export default new RequestHttp(baseConfig);
