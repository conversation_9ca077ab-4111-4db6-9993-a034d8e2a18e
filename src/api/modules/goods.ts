/**
 * @Owners blv
 * @Title 商品
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type Goods } from '@/api/interface/goods';

export const getGoodsByPage = async (params: Goods.Params.getGoodsByPage) =>
    http.post<Goods.Response.getGoodsByPage>(TEST + '/goodsMgr/getGoodsByPage', params);

export const getSupplierByNameOrCode = async (params: { codeOrName: string }) =>
    http.get<Goods.Response.getSupplierByNameOrCode>(TEST + '/platform/supplierMgr/getSupplierByNameOrCode', params);

export const getCategoryIds = async (params: Goods.Response.getCategoryIdsReq) =>
    http.post<Goods.Response.getCategoryIdsRes[]>(TEST + '/goodsMgr/getCategoryTreeByType', params);

export const getSupplierGoodsPage = async (params: Goods.Params.getSupplierGoodsPage) =>
    http.post<Goods.Response.getSupplierGoodsPage>(TEST + '/goodsMgr/supplierGoodsPage', params);
