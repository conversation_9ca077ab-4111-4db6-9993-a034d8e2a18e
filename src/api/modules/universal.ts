/**
 * @Owners xj
 * @Title 系统通用接口
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type Universal } from '@/api/interface/universal';

/** oss上传图片 */
export const uploadFileToOss = (params: FormData) => http.post<Universal.FilesUploadType>('/platform/file/upload', params);

/** 获取省市区联动 */
export const getAreaInfo = () => http.post<Universal.AreaInfoType>(`${TEST}/app/area/getAreaInfo`);
