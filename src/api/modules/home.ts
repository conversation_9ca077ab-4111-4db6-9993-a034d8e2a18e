/**
 * @Owners xj
 * @Title 首页
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type Home } from '@/api/interface/home';

// 用户近期直播
export const userLivingInfo = async () => http.post<Home.Response.UserLivingInfo>(TEST + '/liveQuery/userLivingInfo');

// 近期直播预告
export const preLiveList = async () => http.post<Home.Response.PreLiveList>(TEST + '/liveQuery/preLiveList');

// 往期直播场次
export const pastLivePage = async (params: { page: number; rows: number }) =>
    http.post<Home.Response.PastLivePage>(TEST + '/liveQuery/pastLivePage', params);

export const getLiveOverview = async () => http.get<Home.Response.LiveOverview>(TEST + '/liveQuery/recentLivingInfo');
