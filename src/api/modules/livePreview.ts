/**
 * @Owners mzh
 * @Title 直播预告
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type LivePreview } from '@/api/interface/livePreview';

// 近期直播预告
export const getPreLiveList = async () => http.post<LivePreview.Response.LiveItem[]>(TEST + '/liveQuery/preLiveList');

// 直播预告里的往期直播场次
export const getPastPreLivePage = async (params: LivePreview.Params.getPastPreLivePage) =>
    http.post<LivePreview.Response.getLiveList>(TEST + '/liveQuery/pastPreLivePage', params);

// 往期直播统计
export const getPastLiveStatistics = async () =>
    http.post<LivePreview.Response.getPastLiveStatistics>(TEST + '/liveQuery/pastLiveStatistics');

// 往期直播场次
export const getPastLivePage = async (params: LivePreview.Params.getPastLivePage) =>
    http.post<LivePreview.Response.getLiveList>(TEST + '/liveQuery/pastLivePage', params);
