/**
 * @Owners zp
 * @Title 系统设置 - 角色
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import type { Roles } from '@/api/interface/system';

// 获取角色列表
export const getRolesList = <T>(params?: { pageSize?: number; current?: number; keyword?: string }) =>
    http.get<T>(`${TEST}/role/list`, params);

// 新建角色
export const addRoles = (params: Roles.IAddRoles) => http.post<Roles.IAddRoles>(`${TEST}/role/add`, params);

// 更新角色
export const updateRoles = (params: Roles.IAddRoles) => http.post<Roles.IAddRoles>(`${TEST}/role/update`, params);
