/**
 * @Owners xj
 * @Title 登录
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import type { User } from '@/api/interface/system';

/** 登录 */
export const login = (params: User.LoginReq) => http.post<User.LoginRes>(`${TEST}/LoginMgr/login`, params);

/** 登出 */
export const logout = (params: { loginId: number }) => http.get(`${TEST}/LoginMgr/logout`, params);

// 获取用户信息
export const getUserInfo = () => http.post<User.UserInfo>(`${TEST}/liveQuery/findUserInfo`, { headers: { noLoading: true } });
