/**
 * @Owners xj
 * @Title 菜单
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import type { SysMenu } from '@/api/interface/system';

type MenuItem = SysMenu.MenuItem;
// 获取菜单列表
export const getMenuList = <T>(params?: { pageSize?: number; current?: number; keyword?: string }) =>
    http.get<T>(`${TEST}/menu/list`, params);

// 新建菜单
export const addMenu = (params: MenuItem) => http.post<MenuItem>(`${TEST}/menu/add`, params);

// 更新菜单
export const updateMenu = (params: MenuItem) => http.post<MenuItem>(`${TEST}/menu/update`, params);
