/**
 * @Owners blv
 * @Title 直播中控台
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type Goods } from '@/api/interface/goods';
import { type CreateLive } from '@/api/interface/live/create';

export const findUserLivePermissions = async () =>
    http.post<CreateLive.Response.UserPermission>(TEST + '/liveMgr/findUserLivePermissions');

/**
 *
 * @returns 获取主播最近一场已结束直播的有效商品
 */
export const getAllLastLiveGoods = async (param: {
    /**
     * 主播id
     */
    anchorUserId: number;
}) => http.post<{ dataList: Goods.Response.getGoodsByPage['dataList'] }>(TEST + '/live/goods/getAllLastLiveGoods', param);

export const addLive = async (param: CreateLive.Params.AddLive) => http.post<number>(TEST + '/liveMgr/addLive', param);
