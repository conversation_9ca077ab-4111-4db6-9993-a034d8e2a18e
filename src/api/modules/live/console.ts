/**
 * @Owners xj
 * @Title 直播中控台
 */
import http, { requestSSE } from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type Console } from '@/api/interface/live/console';
import { type RedPacket } from '@/api/interface/live/redpacket';
import { type IPageResult } from '@/api/interface/request';

// 直播详情
export const liveDetailInfo = async (params: Console.Params.LiveDetailInfo) =>
    http.post<Console.Response.LiveDetailInfo>(TEST + '/liveQuery/liveDetailInfo', params);

// 修改直播
export const updateLive = async (params: Console.Params.UpdateLive) => http.post(TEST + '/liveMgr/updateLive', params);

// 直播推流
export const pushLiveOpenNotice = async (params: Console.Params.PushLiveOpenNotice) =>
    http.post(TEST + '/liveMgr/pushLiveOpenNotice', params);

// 操作直播间
export const operateLive = async (params: Console.Params.OperateLive) => http.post(TEST + '/liveMgr/operateLive', params);

// 生成流地址
export const getStreamUrl = async (params: Console.Params.GetStreamUrl) =>
    http.post<Console.Response.LiveDetailInfo>(TEST + '/liveMgr/getStreamUrl', params);

/** 历史直播记录 */
export const getLiveHistoryRecordPage = async (params: Console.Params.GetLiveHistoryRecordPage) =>
    http.post<Console.Response.GetLiveHistoryRecordPage>(TEST + '/liveQuery/getLiveHistoryRecordPage', params);

// 操作直播间
export const findLiveGoodsByPage = async (params: Console.Params.FindLiveGoodsByPage) =>
    http.post<Console.Response.FindLiveGoodsByPage>(TEST + '/live/goods/findLiveGoodsByPage', params, {
        headers: { noLoading: true },
    });

export const batchImportLiveGoods = async (params: Console.Params.BatchImportLiveGoods) =>
    http.post(TEST + '/live/goods/batchImportLiveGoods', params);

/**
 * 上下架专享价
 */
export const goodsSkuPriceMgrOnAndOff = async (params: Console.Params.LiveExclusivePriceOnOffReqDTO) =>
    http.post(TEST + '/exclusive/price/onAndOff', params, {
        headers: { noLoading: true },
    });

// 直播商品添加
export const addLiveGoods = async (params: Console.Params.AddLiveGoods) =>
    http.post(TEST + '/live/goods/addLiveGoods', params, {
        headers: { noLoading: true },
    });

// 直播商品删除
export const deleteLiveGoods = async (params: Console.Params.DeleteLiveGoods) =>
    http.post(TEST + '/live/goods/deleteLiveGoods', params);

// 直播商品讲解
export const saySortLiveGoods = async (params: Console.Params.SaySortLiveGoods) =>
    http.post(TEST + '/live/goods/saySortLiveGoods', params);

// 直播商品置顶
export const setLiveGoodsTop = async (params: Console.Params.SetLiveGoodsTopReqDTO) =>
    http.post(TEST + '/live/goods/setLiveGoodsTop', params);

// 直播商品取消讲解
export const cancelSayLiveGoods = async (params: Console.Params.SaySortLiveGoods) =>
    http.post(TEST + '/live/goods/cancelSayLiveGoods', params);

// 直播商品-设置直播商品序号
export const setLiveGoodsSort = async (params: Console.Params.SetLiveGoodsSort) =>
    http.post(TEST + '/live/goods/setLiveGoodsSort', params);

// 直播商品-重置直播链接序号
export const resetLiveGoodsSerialNo = async (params: Console.Params.ResetLiveGoodsSerialNo) =>
    http.post(TEST + '/live/goods/resetLiveGoodsSerialNo', params);

// 获取数据大屏链接
export const createLiveDataQueryToken = async (params: { liveId: number }) =>
    http.post(TEST + '/liveMgr/createLiveDataQueryToken', params);

// 获取用户列表与禁言用户列表
export const liveGroupUsers = async (params: Console.Params.PageReqDTOLiveGroupOnlineUsersReqVO) =>
    http.post<Console.Response.PageResDTOLiveGroupOnlineUsersRespVO>(TEST + '/liveUser/liveGroupUsers', params);
// 获取直播评论
// export const getLiveComment = async (params: Console.Params.GetLiveComment) =>
//     http.post(TEST + '/liveMgr/getLiveComment', params);

/** 设置用户禁言 */
export const setUserMuted = async (params: Console.Params.SetUserMuted) => http.post(TEST + '/liveUser/makeMuted', params);

/** 获取用户禁言列表 */
export const getUserMutedList = async (params: Console.Params.GetUserMutedList) =>
    http.post<Console.Response.GetUserMutedList>(TEST + '/liveUser/getAllMuteUserIds', params);

/** 获取拉黑用户列表 */
export const getBlockUserList = async (params: Console.Params.GetBlockUserList) =>
    http.post<Console.Response.GetBlockUserList>(TEST + '/liveUser/liveBlockUsers', params);

/** 获取直播评论 */
export const getCommentList = async (params: Console.Params.GetCommentList) =>
    http.get<Console.Response.GetCommentList>(TEST + '/comment/im/history-msg', params);

/** 设置评论状态 */
export const setCommentStatus = async (params: Console.Params.SetCommentStatus) => http.post(TEST + '/comment/control', params);

/** 设置全局禁言 */
export const setGlobalMute = async (params: Console.Params.SetGlobalMute) => http.post(TEST + '/liveMgr/globalMute', params);

/** 发送评论 */
export const sendComment = async (params: Console.Params.SendComment) => http.post(TEST + '/comment/im/send', params);

/** 发送评论并上墙 */
export const sendCommentAndUpperWall = async (params: Console.Params.SendComment) =>
    http.post(TEST + '/comment/im/sendAndUpperWall', params);

// 获取指定直播间概览数据
export const overviewData = async (params: { liveId: number }) =>
    http.get<Console.Response.LiveRuntimeBigStatisticsDTO>(TEST + '/largeScreen/bigData/statistics/overview/data', params);
// 获取指定直播间趋势数据
export const trendData = async (params: { liveId: number }) =>
    http.get<Console.Response.LiveTrendsDataDTO[]>(TEST + '/largeScreen/bigData/statistics/trend/data', params);
// 获取直播在线用户/ 禁言用户数量
export const getLiveImUserNum = async (params: { groupId: number; liveId: number }) =>
    http.post<Console.Response.LiveImUserNumRespDTO>(TEST + '/liveUser/getLiveImUserNum', params);

// 邀请榜
export const getInvite = async (params: Console.Params.TopRequest) =>
    http.get<Console.Response.LiveUserInviteTopDataDTO[]>(TEST + '/largeScreen/bigData/statistics/invite/data', params);
// 互动榜
export const getInteraction = async (params: Console.Params.TopRequest) =>
    http.get<Console.Response.LiveUserCommentTopDataDTO[]>(TEST + '/largeScreen/bigData/statistics/interaction/data', params);
// 在线榜
export const getTime = async (params: Console.Params.TopRequest) =>
    http.get<Console.Response.LiveUserTimeDataDTO[]>(TEST + '/largeScreen/bigData/statistics/time/data', params);
// 购买榜
export const getBuy = async (params: Console.Params.TopRequest) =>
    http.get<Console.Response.LiveUserBuyTopDataDTO[]>(TEST + '/largeScreen/bigData/statistics/buy/data', params);
// 用户画像/城市
export const userPortraitsData = async (params: Console.Params.UserPortraitsDataRequest) =>
    http.get<Console.Response.LiveUserPortraitsRespVO>(TEST + '/largeScreen/bigData/statistics/userPortraits/data', params);

/** 屏蔽关键词 */
export const getLiveBlockKeywords = async (params: Console.Params.OnlyLiveId) =>
    http.post<Console.Response.GetLiveBlockKeywords>(TEST + '/liveMgr/liveSensitiveKeyword/selectByLiveId', params);

/** 更新关键词 */
export const updateLiveBlockKeywords = async (params: Console.Params.UpdateLiveBlockKeywords) =>
    http.post(TEST + '/liveMgr/liveSensitiveKeyword/update', params);

// 查询商品话术
export const liveEpigraphTextQuery = async (params: Console.Params.liveEpigraphTextRequest) =>
    http.post<Console.Response.LiveEpigraphTextRespDTO>(TEST + '/liveMgr/liveEpigraphText/query', params);
// 更新直播间商品话术
export const liveEpigraphTextUpdate = async (params: Console.Params.LiveEpigraphTextReqDTO) =>
    http.post(TEST + '/liveMgr/liveEpigraphText/update', params);
// AI生成话术
export function getLiveGoodsPromotionWords(
    data: Console.Params.liveEpigraphTextRequest,
    onMessage: (data: Console.Response.LiveGoodsPromotionWordsAiStreamLiveEpigraphTextRespDTO) => void,
    onError?: (error: unknown) => void,
    onCancel?: () => void
) {
    return requestSSE({
        url: TEST + '/liveMgr/liveGoodsPromotionWordsAi/stream',
        data,
        onMessage,
        onError,
        onCancel,
    });
}
/** 开启提词 */
export const openNoticeFromApi = async (params: { liveId: number; spuId: number; type: number }) =>
    http.post<Console.Response.Response>(TEST + '/liveMgr/liveEpigraphText/open', params, {
        headers: {
            noErrorTips: true,
        },
    });
/** 关闭提词 */
export const closeNoticeFromApi = async (params: { liveId: number }) =>
    http.post<Console.Response.Response>(TEST + '/liveMgr/liveEpigraphText/close', params);
/** 商品分析 */
export const getGoodsAnalysisData = async (params: { liveId: number; page: number; rows: number }) =>
    http.get<Console.Response.GoodsAnalysisRes>(TEST + '/largeScreen/bigData/statistics/goods/data/analytics', params);
// 查询直播间AI诊断
export const liveAnalysisQuery = async (params: Console.Params.LiveAnalysisReqDTO) =>
    http.post<Console.Response.LiveAnalysisRespDTO>(TEST + '/liveMgr/liveAnalysis/query', params);
/** 更新公告 */
export const updateNotice = async (params: Console.Params.UpdateNotice) =>
    http.post(TEST + '/liveMgr/updateAnnouncement', params);

/** 更新广播 */
export const updateBroadcast = async (params: Console.Params.UpdateBroadcast) =>
    http.post(TEST + '/liveMgr/updateBroadcast', params);

/** 更新下一场直播预约 pushSwitch:是否推送预约 0.关闭 1.开启 */
export const pushLiveReservation = async (params: { liveId: number; pushSwitch: number }) =>
    http.post(TEST + '/liveMgr/pushLiveReservation', params);

/** 获取直播间内推送直播预告信息 */
export const getLiveReservationPushMsg = async (params: { liveId: number }) =>
    http.post<Console.Response.getLiveReservationPushMsgRespDTO>(TEST + '/liveMgr/getLiveReservationPushMsg', params);

export const optLiveGoods = async (params: {
    /**
     * 直播间id
     */
    liveId: number;
    /**
     * 商品列表
     */
    spuIds: number[];
    /**
     * 1上架 0下架
     */
    type: number;
}) => http.post<Console.Response.LiveImUserNumRespDTO>(TEST + '/live/goods/optLiveGoods', params);

// 获取直播间商品数量
export const getLiveGoodsCount = async (params: Console.Params.GetLiveGoodsCount) =>
    http.post<Console.Response.GetLiveGoodsCount>(TEST + '/live/goods/getLiveGoodsCount', params, {
        headers: { noLoading: true },
    });

/** 更新连麦状态 */
export const updateEnableMic = async (params: Console.Params.UpdateEnableMic) =>
    http.post(TEST + '/mic/config/audience-link/open-or-over', params);

/** 获取直播间连麦配置 */
export const getInteractiveConfig = async (params: Console.Params.GetInteractiveConfig) =>
    http.get<Console.Response.GetInteractiveConfig>(TEST + '/mic/config/info', params);

/** 保存连麦配置 */
export const saveInteractiveConfig = async (params: Console.Params.SaveInteractiveConfig) =>
    http.post(TEST + '/mic/config/update', params);

/** 获取观众列表 */
export const getAudienceList = async (params: Console.Params.GetAudienceList) =>
    http.post<Console.Response.GetAudienceList>(TEST + '/mic/invite/invites-list', params);

/** 邀请连麦 */
export const inviteCoStream = async (params: Console.Params.InviteCoStream) =>
    http.post<Console.Response.GetAudienceList>(TEST + '/mic/invite/send', params);

/** 获取连麦申请列表 */
export const getApplyCoList = async (params: Console.Params.GetApplyCoList) =>
    http.post<Console.Response.GetApplyCoList>(TEST + '/mic/apply/getApplyingListByAnchor', params);

/** 接受连麦 */
export const acceptApplyCo = async (params: Console.Params.AcceptApplyCo) => http.post(TEST + '/mic/apply/acceptApply', params);

/** 更新用户麦克风状态 */
export const updateMicStatusOfUser = async (params: Console.Params.UpdateMicStatusOfUser) =>
    http.post(TEST + '/mic/seat/mute-user', params);

/** 更新用户摄像头状态 */
export const updateCameraStatusOfUser = async (params: Console.Params.UpdateCameraStatusOfUser) =>
    http.post(TEST + '/mic/seat/camera-user', params);

/** 断开用户连麦 */
export const diconnectStreamOfUser = async (params: Console.Params.DiconnectStreamOfUser) =>
    http.post(TEST + '/mic/seat/disconnect', params);

/** 获取连麦列表 */
export const getCoStreamList = async (params: Console.Params.GetCoStreamList) =>
    http.get<Console.Response.GetCoStreamList>(TEST + '/mic/seat/list', params);
// 红包雨新增活动
export const addActiveRedPacketRain = async (params: RedPacket.BaseActiveRedPacketRain) =>
    http.post(TEST + '/active/activeRedPacketRainMgr/add', params);

// 红包雨删除活动
export const deleteActiveRedPacketRain = async (params: Partial<{ id: number }>) =>
    http.post(TEST + '/active/activeRedPacketRainMgr/delete', params);

// 红包雨活动详情
export const getActiveRedPacketRainDetail = async (params: Partial<{ id: number }>) =>
    http.post<RedPacket.Response.GetActiveRedPacketRainDetail>(TEST + '/active/activeRedPacketRainMgr/detail', params);

// 红包雨活动数量
export const getActiveRedPacketRainCount = async (params: RedPacket.Params.GetActiveRedPacketRainCount) =>
    http.post<RedPacket.Response.GetActiveRedPacketRainCount>(TEST + '/active/activeRedPacketRainMgr/count', params);

// 红包雨活动列表
export const getActiveRedPacketRainList = async (params: RedPacket.Params.GetActiveRedPacketRainList) =>
    http.post<RedPacket.Response.GetActiveRedPacketRainList>(TEST + '/active/activeRedPacketRainMgr/list', params);

// 活动数据统计
export const getActiveRedPacketRainStat = async (params: Partial<{ id: number }>) =>
    http.post<RedPacket.Response.GetActiveRedPacketRainStat>('/proxy/active/activeRedPacketRain/stat', params);

// 编辑任务
export const updateActiveRedPacketRain = async (params: RedPacket.BaseActiveRedPacketRain) =>
    http.post(TEST + '/active/activeRedPacketRainMgr/update', params);

// 设置开启/关闭状态
export const updateActiveRedPacketRainOpenStatus = async (params: RedPacket.Params.UpdateActiveRedPacketRainOpenStatus) =>
    http.post(TEST + '/active/activeRedPacketRainMgr/updateOpenStatus', params);

// 红包雨活动-中奖信息列表导出
export const exportActiveRedPacketRainUseLotteryPrize = async (
    params: RedPacket.Params.ExportActiveRedPacketRainUseLotteryPrize
) => http.post(TEST + '/active/activeRedPacketRain/useLotteryPrize/export', params);

// 红包雨活动-中奖信息列表
export const getActiveRedPacketRainUseLotteryPrizeList = async (
    params: RedPacket.Params.GetActiveRedPacketRainUseLotteryPrizeList
) =>
    http.post<RedPacket.Response.GetActiveRedPacketRainUseLotteryPrizeList>(
        TEST + '/active/activeRedPacketRainMgr/useLotteryPrize/list',
        params
    );

// 直播间福袋领取详情
export const getLuckBagReceiveDetail = async (params: RedPacket.Params.GetActiveLuckBagReceiveDetail) =>
    http.post<RedPacket.Response.GetActiveLuckBagReceiveDetail>(TEST + '/liveLuckyBagMgr/getReceiveDetail', params);
// 红包雨活动-奖品核销
export const receiveActiveRedPacketRainUseLotteryPrize = async (
    params: RedPacket.Params.ReceiveActiveRedPacketRainUseLotteryPrize
) => http.post(TEST + '/active/activeRedPacketRain/useLotteryPrize/receive', params);

// 已授权的快递公司枚举
export const getCourierCompanyList = async () =>
    http.get<Console.Response.GetCourierCompanyList>(TEST + '/expressWaybill/auth/courierCompanyList');

// 红包雨活动-实物奖品列表
export const getActiveRedPacketRainRealPrizeList = async (params: RedPacket.Params.GetActiveRedPacketRainRealPrizeList) =>
    http.post<RedPacket.Response.GetActiveRedPacketRainRealPrizeList>(
        TEST + '/active/activeRedPacketRainMgr/realPrize/list',
        params
    );

// 根据spuId获取规格列表接口
export const getSkuDescBySpuId = async (params: RedPacket.Params.GetSkuDescBySpuId) =>
    http.post<RedPacket.Response.GetSkuDescBySpuId>(TEST + '/active/activeRedPacketRainMgr/getSkuDescBySpuId', params);

// 判断是否存在活动
export const hasLiveActive = async (params: { liveId: number }) =>
    http.post<RedPacket.Response.HasLiveActive>(TEST + '/liveQuery/hasLiveActive', params);

// 获取是否供应商
export const getAnchorIsSupplier = async () =>
    http.post<RedPacket.Response.GetAnchorIsSupplier>(TEST + '/userMgr/getAnchorIsSupplier');

// 直播间商品设置短标题
export const setLiveGoodsCustomSpuName = async (params: RedPacket.Params.LiveGoodsCustomSpuNameReq) =>
    http.post(TEST + '/live/goods/customSpuName', params);

/** 更新快速评论预设 */
export const updateLiveManualComment = async (params: Console.Params.UpdateLiveManualComment) =>
    http.post(TEST + '/liveMgr/updateLiveManualComment', params);

/** 获取快速评论预设 */
export const getLiveManualComment = async (params: { liveId: number }) =>
    http.post<Console.Response.GetLiveManualComment>(TEST + '/liveMgr/queryManualComment', params);

/** 获取全部直播商品类别 */
export const getAllGoodsCategory = async (params: Console.Params.GetLiveGoodsCategoryReqVO) =>
    http.post<Console.Response.LiveGoodsCategoryVO[]>(TEST + '/live/goods/getAllGoodsCategory', params);
/** 新增直播商品类别 */
export const addGoodsCategory = async (params: Console.Params.AddLiveGoodsCategoryReqVO) =>
    http.post<{}>(TEST + '/live/goods/addGoodsCategory', params);
/** 删除直播商品类别 */
export const delGoodsCategory = async (params: Console.Params.OptLiveGoodsCategoryReqVO) =>
    http.post<{}>(TEST + '/live/goods/delGoodsCategory', params);
/** 修改直播商品类别 */
export const editGoodsCategory = async (params: Console.Params.OpdateLiveGoodsCategoryReqVO) =>
    http.post<{}>(TEST + '/live/goods/editGoodsCategory', params);
/** 获取快速评论预设 */
export const sortGoodsCategory = async (params: Console.Params.OptSoftLiveGoodsCategoryReqVO) =>
    http.post<{}>(TEST + '/live/goods/sortGoodsCategory', params);
/** 全量更新直播商品类别 */
export const updateAllGoodsCategory = async (params: Console.Params.OptSoftLiveGoodsCategoryReqVO) =>
    http.post<{}>(TEST + '/live/goods/updateAllGoodsCategory', params);

/** 批量编辑直播商品 */
export const batchEditLiveGoods = async (params: Console.Params.EditLiveGoodsReqVO) =>
    http.post<{}>(TEST + '/live/goods/batchEditLiveGoods', params);

/** 导入直播间商品 */
export const importLiveGoods = async (params: Console.Params.LiveGoodsImportReqVO) =>
    http.post<{}>(TEST + '/live/goods/importLiveGoods', params);

/** 一键开售直播间商品 */
export const publishLiveGoods = async (params: Console.Params.PublishLiveGoodsReqVO) =>
    http.post<{}>(TEST + '/live/goods/publishLiveGoods', params);

/** 获取最近一场预推流直播预告 */
export const getRecentlyPrePushPreLive = async () =>
    http.post<Console.Response.GetRecentlyPrePushPreLive>(TEST + '/liveMgr/getRecentlyPrePushLive', {});

/** 获取直播间配置 */
export const getLiveConfig = async (params: { liveId: number }) =>
    http.post<Console.Response.GetLiveConfig>(TEST + '/config/get', params);

/** 更新直播间配置 */
export const updateLiveConfig = async (params: Console.Params.UpdateLiveConfig) => http.post(TEST + '/config/update', params);

/** 获取直播拉流地址 */
export const getPlayUrl = async (params: { liveId: number }) =>
    http.post<Console.Response.GetPlayUrl>(TEST + '/liveMgr/getPlayUrl', params);
/** 根据热销数据排序直播商品 */
export const sortLiveGoods = async (params: Console.Params.SortLiveGoodsByHotSalesReqVO) =>
    http.post<{}>(TEST + '/live/goods/sortLiveGoods', params);

/** 评论上墙 */
export const setUpperWall = async (params: Console.Params.SetUpperWall) =>
    http.post<{}>(TEST + '/comment/im/messageUpperWall', params);

/** 拉黑/解禁用户 */
export const setUserBlock = async (params: Console.Params.SetUserBlock) => http.post<{}>(TEST + '/liveUser/makeBlock', params);

export function getOrderList(data: Console.Params.OrderListReqVO) {
    return http.post<IPageResult<Console.Response.LiveOrderVO>>(TEST + '/largeScreen/bigData/statistics/orders/data', data);
}
