/**
 * @Owners fleix.cai
 * @Title 优惠券
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type Coupon } from '@/api/interface/coupon';

export const getCouponListByPage = async (params: Coupon.Params.getCouponListByPage) =>
    http.post<Coupon.Response.getCouponListByPage>(TEST + '/couponMgr/couponListByPage', params);

export const getCouponDetailById = async (params: Coupon.Params.getCouponDetailById) =>
    http.post<Coupon.Response.getCouponDetailById>(TEST + '/couponMgr/getCouponDetailById', params);

export const supplierLiveCouponPage = async (params: Coupon.Params.supplierLiveCouponPage) =>
    http.post<Coupon.Response.supplierLiveCouponPage>(TEST + '/coupon/supplierLiveCouponPage', params);
