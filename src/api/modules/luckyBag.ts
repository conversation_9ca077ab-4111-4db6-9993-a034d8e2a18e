/**
 * @Owners wyy
 * @Title 福袋
 */
import http from '@/api';
import { TEST } from '@/api/config/servicePort';
import { type LuckyBag } from '@/api/interface/luckyBag';

// 直播间下面的福袋列表
export const getLiveLuckyBags = async (params: LuckyBag.Params.getLiveLuckyBags) =>
    http.post<LuckyBag.Response.getLiveLuckyBags>(TEST + '/liveLuckyBagMgr/getLiveLuckyBags', params);

// 直播间下面的福袋列表数量
export const getLiveLuckyBagsCount = async (params: LuckyBag.Params.getLiveLuckyBagsCount) =>
    http.post<LuckyBag.Response.getLiveLuckyBagsCount>(TEST + '/liveLuckyBagMgr/getLiveLuckyBagsCount', params);

// 直播间添加福袋
export const addLuckyBag = async (params: LuckyBag.Params.addLuckyBag) =>
    http.post<LuckyBag.Response.addLuckyBag>(TEST + '/liveLuckyBagMgr/addLuckyBag', params);

// 直播间更新福袋
export const updateLuckyBag = async (params: LuckyBag.Params.updateLuckyBag) =>
    http.post<LuckyBag.Response.updateLuckyBag>(TEST + '/liveLuckyBagMgr/updateLuckyBag', params);

// 直播间编辑福袋
export const editLuckyBag = async (params: LuckyBag.Params.editLuckyBag) =>
    http.post<LuckyBag.Response.editLuckyBag>(TEST + '/liveLuckyBagMgr/editLuckyBag', params);

// 直播间福袋详情
export const getLiveLuckyBagDetail = async (params: LuckyBag.Params.getLiveLuckyBagDetail) =>
    http.post<LuckyBag.Response.getLiveLuckyBagDetail>(TEST + '/liveLuckyBagMgr/getLiveLuckyBagDetail', params);
