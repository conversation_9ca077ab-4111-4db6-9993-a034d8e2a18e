/**
 * @Owners xj
 * @Title 登录
 */

export namespace User {
    export type LoginReq = {
        /** 用户名 */
        username: string;
        /** 密码 */
        password: string;
    };

    export type LoginRes = {
        /** token */
        token: string;
        id: number;
        userId: number;
        nickname: string;
        username: string;
    };

    export interface UserInfo {
        id: number;
        nickname: string;
        avatar: string;
        phone: string;
        shopInfo: {
            id: number;
            dealerId: number;
            brief: string;
            shopIcon: string;
            shopName: string;
        };
    }
}
