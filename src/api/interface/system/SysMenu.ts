/**
 * @Owners zp
 * @Title 菜单
 */
import type * as Icons from '@ant-design/icons';

export namespace SysMenu {
    export interface MenuItem {
        path: string;
        name: string;
        sort: number;
        perssion?: string;
        id?: string;
        isDelete?: number;
        // 0关闭，1启用
        status?: number;
        parentId?: string;
        icon?: keyof typeof Icons;
        type?: string;
        chidrenMenuList?: MenuItem[];
        children?: MenuItem[];
        hidden?: boolean;
    }

    // Dropdown MenuInfo
    export interface MenuInfo {
        key: string;
        keyPath: string[];
        /** @deprecated This will not support in future. You should avoid to use this */
        item: React.ReactInstance;
        domEvent: React.KeyboardEvent<HTMLElement> | React.MouseEvent<HTMLElement>;
    }
}
