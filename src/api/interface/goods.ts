/**
 * @Owners blv
 * @Title 商品
 */

export namespace Goods {
    export namespace Params {
        export type getGoodsByPage = {
            condition: {
                status?: number[];
                goodsType?: number;
                spuCodeAndName?: string;
                spuIds?: number[];
                supplierId?: number;
                goodsUpFlag?: number;
                oneFrontCategoryIdList?: number[];
                liveId?: number;
                scene?: number;
            };
            page: number;
            rows: number;
        };
        export type getSupplierGoodsPage = {
            /**
             * 页码, 默认1
             */
            page?: number;
            /**
             * 条数, 默认10
             */
            rows?: number;
            /**
             * 商品搜索关键字
             */
            searchText?: string;
        };
    }
    export namespace Response {
        export type getGoodsByPage = {
            dataList: {
                sort: number;
                serialNo: number;
                activityShowStyle: number;
                activityShowText: string;
                baseSales: number;
                buyerShowLabelList: string[];
                carouselPic: string;
                categoryName: string;
                columnGoodsId: number;
                columnSort: number;
                cpsCategoryId: string;
                cpsCategoryName: string;
                deliveryType: number;
                freAskedQuestions: string;
                freightTemplateId: number;
                gmtCreate: string;
                gmtModified: string;
                goodVersion: number;
                goodsCardPic: string;
                goodsDeliverTime: number;
                goodsType: number;
                goodsUpStatus: number;
                goodsVideo: string;
                insurance: number;
                integralDeductRate: number;
                integralReturnRate: number;
                isCompensation: number;
                isIntegralSubsidy: number;
                isParcel: number;
                isPublish: number;
                isPurchaseLimits: number;
                isSevenDayReturn: number;
                isShopExclusive: number;
                isShowSupSuggest: number;
                isTestGoods: number;
                labelIds: string;
                level: string;
                limitCycle: number;
                limitNum: number;
                newShopSort: number;
                oneCategory: number;
                oneCategoryName: string;
                picSourceList: {
                    id: number;
                    spuId: number;
                    type: number;
                    sort: number;
                    refObj: string;
                    gmtCreate: string;
                    gmtModified: string;
                }[];
                publishDown: string;
                publishTime: string;
                publishUp: string;
                realSales: number;
                remark: string;
                searchWordsLabelList: string[];
                shopExclusiveRule: number;
                sourceMaterialStatus: number;
                spuSort: number;
                status: number;
                supervisorId: number;
                supervisorName: string;
                supplierIntroduce: string;
                supplierLevel: number;
                supplierName: string;
                supplierReturnAddressId: number;
                textSourceList: {
                    id: number;
                    spuId: number;
                    type: number;
                    sort: number;
                    refObj: string;
                    gmtCreate: string;
                    gmtModified: string;
                }[];
                totalSales: number;
                twoCategory: number;
                twoCategoryName: string;
                videoSourceList: {
                    id: number;
                    spuId: number;
                    type: number;
                    sort: number;
                    refObj: string;
                    gmtCreate: string;
                    gmtModified: string;
                }[];
                spuId: number;
                spuName: string;
                spuCode: string;
                originAddress: string;
                manyProperty: boolean;
                sellingPoint: string;
                detail: string;
                backgroundCategoryId: number;
                backgroundCategoryName: string;
                frontDeskCategoryId: number;
                frontDeskCategoryName: string;
                brandId: number;
                brandName: string;
                pics: {
                    spuId: number;
                    picId: number;
                    picUrl: string;
                    picSort: number;
                }[];
                skus: {
                    endDeliveryTime: string;
                    goodsDeliveryAgeing: number;
                    isDelete: number;
                    isHide: number;
                    jdPrice: number;
                    jdPriceRate: number;
                    jdPriceSub: number;
                    jdPriceUpdate: string;
                    jdUrl: string;
                    newShopNum: number;
                    newShopPrice: number;
                    newUserPrice: number;
                    otherPrices: [
                        {
                            otherPlatform: string;
                            otherPrice: number;
                        }
                    ];
                    propPicUrl: string;
                    salesStatus: number;
                    skuApplyCount: number;
                    skuPrice: number;
                    skuSort: number;
                    startDeliveryTime: string;
                    supplierId: number;
                    taxation: number;
                    tmPrice: number;
                    tmPriceRate: number;
                    tmPriceSub: number;
                    tmPriceUpdate: string;
                    tmUrl: string;
                    spuId: number;
                    skuId: number;
                    skuCode: number;
                    num: number;
                    rewardPrice: number;
                    unitPrice: number;
                    retailPrice: number;
                    grossWeight: number;
                    netWeight: number;
                    grossWeightUnit: string;
                    netWeightUnit: string;
                    gmtModified: string;
                    supplierCode: string;
                    supplierSkuCode: string;
                    thirdPartySkuCode: string;
                    commissionRate: number;
                    primaryDistributionPercentage: 0.5;
                    settlementPrice: number;
                    commissionMoney: number;
                    commissionType: number;
                    propertyValuesStr: string;
                    properties: {
                        sort: number;
                        propertyId: number;
                        propertyName: string;
                        propertyValueId: number;
                        propertyValue: string;
                    }[];
                }[];
                goodsTestingImgs: {
                    picId: number;
                    picSort: number;
                    picUrl: string;
                    spuId: number;
                }[];
                isSelected: number;
            }[];
            total: number;
        };
        export type getSupplierByNameOrCode = {
            code?: string;
            id: number;
            name?: string;
            supplierIntroduce?: string;
            supplierType?: number[];
        }[];
        /**
         * 分类类型
         *
         * 查询类目（根据分类类型获取分类树）请求实体
         */
        export type getCategoryIdsReq = {
            /**
             * 类目类型 1.前台 2.后台 3.小b
             */
            type: number;
            [property: string]: unknown;
        };
        export type getSupplierGoodsPage = {
            dataList: {
                /**
                 * 商品头图
                 */
                headPic?: string;
                /**
                 * 最小价格sku规格
                 */
                minSkuDesc?: string;
                /**
                 * 短标题
                 */
                shortTitle?: string;
                /**
                 * spuId
                 */
                spuId?: number;
                /**
                 * 商品名称
                 */
                spuName?: string;
                /**
                 * 商品主图
                 */
                url?: string;
            }[];
            total: number;
        };
        /**
         * 查询类目（根据类目类型获取类目树）结果实体
         */
        export type getCategoryIdsRes = {
            /**
             * 子类目
             */
            // children: 查询类目根据类目类型获取类目树结果实体[];
            /**
             * 发货时效
             */
            goodsDeliveryAgeing?: number;
            /**
             * 类目图标
             */
            icon: string;
            /**
             * 类目id
             */
            id: number;
            /**
             * 类目名字
             */
            name: string;
            /**
             * 类目的父级id
             */
            parentId: number;
            /**
             * 类目父级名字
             */
            parentName: string;
            /**
             * 排序
             */
            sort: number;
            /**
             * 启用状态 true=启动 false=禁用
             */
            status: boolean;
            /**
             * 类目类型 1.前台 2.后台 3.小b
             */
            type: number;
            // [property: string]: unknown;
        };
    }
}
