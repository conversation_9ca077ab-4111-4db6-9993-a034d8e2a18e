/**
 * @Owners blv
 * @Title 商品
 */

export namespace Goods {
    export namespace Params {
        export type getGoodsByPage = {
            condition: {
                status?: number[];
                goodsType?: number;
                spuCodeAndName?: string;
                spuIds?: number[];
                supplierId?: number;
                goodsUpFlag?: number;
                oneFrontCategoryIdList?: number[];
                liveId?: number;
                scene?: number;
                rewardAmountMin?: number;
                rewardAmountMax?: number;
            };
            page: number;
            rows: number;
        };
        export type getSupplierGoodsPage = {
            /**
             * 页码, 默认1
             */
            page?: number;
            /**
             * 条数, 默认10
             */
            rows?: number;
            /**
             * 商品搜索关键字
             */
            searchText?: string;
        };
    }
    export namespace Response {
        export type getGoodsByPage = {
            dataList: {
                sort: number;
                serialNo: number;
                activityShowStyle: number;
                activityShowText: string;
                baseSales: number;
                buyerShowLabelList: string[];
                carouselPic: string;
                categoryName: string;
                columnGoodsId: number;
                columnSort: number;
                cpsCategoryId: string;
                cpsCategoryName: string;
                deliveryType: number;
                freAskedQuestions: string;
                freightTemplateId: number;
                gmtCreate: string;
                gmtModified: string;
                goodVersion: number;
                goodsCardPic: string;
                goodsDeliverTime: number;
                goodsType: number;
                goodsUpStatus: number;
                goodsVideo: string;
                insurance: number;
                integralDeductRate: number;
                integralReturnRate: number;
                isCompensation: number;
                isIntegralSubsidy: number;
                isParcel: number;
                isPublish: number;
                isPurchaseLimits: number;
                isSevenDayReturn: number;
                isShopExclusive: number;
                isShowSupSuggest: number;
                isTestGoods: number;
                labelIds: string;
                level: string;
                limitCycle: number;
                limitNum: number;
                newShopSort: number;
                oneCategory: number;
                oneCategoryName: string;
                picSourceList: {
                    id: number;
                    spuId: number;
                    type: number;
                    sort: number;
                    refObj: string;
                    gmtCreate: string;
                    gmtModified: string;
                }[];
                publishDown: string;
                publishTime: string;
                publishUp: string;
                realSales: number;
                remark: string;
                searchWordsLabelList: string[];
                shopExclusiveRule: number;
                sourceMaterialStatus: number;
                spuSort: number;
                status: number;
                supervisorId: number;
                supervisorName: string;
                supplierIntroduce: string;
                supplierLevel: number;
                supplierName: string;
                supplierReturnAddressId: number;
                textSourceList: {
                    id: number;
                    spuId: number;
                    type: number;
                    sort: number;
                    refObj: string;
                    gmtCreate: string;
                    gmtModified: string;
                }[];
                totalSales: number;
                twoCategory: number;
                twoCategoryName: string;
                videoSourceList: {
                    id: number;
                    spuId: number;
                    type: number;
                    sort: number;
                    refObj: string;
                    gmtCreate: string;
                    gmtModified: string;
                }[];
                spuId: number;
                spuName: string;
                spuExclusivePrice: number;
                /**
                 * 券信息
                 */
                activeCouponList?: GetActiveCouponResDTO[];
                activeData: ActiveData;
                spuCode: string;
                originAddress: string;
                manyProperty: boolean;
                sellingPoint: string;
                detail: string;
                backgroundCategoryId: number;
                backgroundCategoryName: string;
                frontDeskCategoryId: number;
                frontDeskCategoryName: string;
                brandId: number;
                brandName: string;
                pics: {
                    spuId: number;
                    picId: number;
                    picUrl: string;
                    picSort: number;
                }[];
                skus: {
                    endDeliveryTime: string;
                    goodsDeliveryAgeing: number;
                    isDelete: number;
                    isHide: number;
                    jdPrice: number;
                    jdPriceRate: number;
                    jdPriceSub: number;
                    jdPriceUpdate: string;
                    jdUrl: string;
                    newShopNum: number;
                    newShopPrice: number;
                    newUserPrice: number;
                    otherPrices: [
                        {
                            otherPlatform: string;
                            otherPrice: number;
                        }
                    ];
                    propPicUrl: string;
                    salesStatus: number;
                    skuApplyCount: number;
                    skuPrice: number;
                    skuSort: number;
                    startDeliveryTime: string;
                    supplierId: number;
                    taxation: number;
                    tmPrice: number;
                    tmPriceRate: number;
                    tmPriceSub: number;
                    tmPriceUpdate: string;
                    tmUrl: string;
                    spuId: number;
                    skuId: number;
                    skuCode: number;
                    num: number;
                    rewardPrice: number;
                    unitPrice: number;
                    retailPrice: number;
                    grossWeight: number;
                    netWeight: number;
                    grossWeightUnit: string;
                    netWeightUnit: string;
                    gmtModified: string;
                    supplierCode: string;
                    supplierSkuCode: string;
                    thirdPartySkuCode: string;
                    commissionRate: number;
                    primaryDistributionPercentage: 0.5;
                    settlementPrice: number;
                    commissionMoney: number;
                    commissionType: number;
                    propertyValuesStr: string;
                    properties: {
                        sort: number;
                        propertyId: number;
                        propertyName: string;
                        propertyValueId: number;
                        propertyValue: string;
                    }[];
                }[];
                goodsTestingImgs: {
                    picId: number;
                    picSort: number;
                    picUrl: string;
                    spuId: number;
                }[];
                isSelected: number;
            }[];
            total: number;
        };

        /**
         * LadderGroupBuyingMinPriceRuleVO
         */
        export interface LadderGroupBuyingMinPriceRuleVO {
            /**
             * 最低活动价
             */
            activePrice?: number;
            boolMaxDiffPrice?: boolean;
            /**
             * 已优惠价格
             */
            diffPrice?: number;
            /**
             * 最大优惠价格
             */
            maxDiffPrice?: number;
            /**
             * 最低价的 skuId
             */
            skuId?: number;
            [property: string]: unknown;
        }
        /**
         * FullDecrementRuleModelVO_1
         */
        export interface FullDecrementRuleModelVO1 {
            /**
             * 优惠阶梯
             */
            conditionList?: FullDecrementConditionModelVO1[];
            /**
             * 类型(1:满额减 2:满件减)
             */
            fullSubtractType?: number;
            /**
             * 优惠类型(1:阶梯优惠 2:循环优惠)
             */
            preferenceType?: number;
            [property: string]: unknown;
        }
        /**
         * FullDecrementConditionModelVO_1
         */
        export interface FullDecrementConditionModelVO1 {
            /**
             * 分摊类型(1:按金额 2:按比例)
             */
            apportionmentType?: number;
            /**
             * 优惠金额
             */
            discount?: number;
            /**
             * 最多优惠金额
             */
            maxDiscount?: number;
            /**
             * 平台分摊金额
             */
            platformCost?: number;
            /**
             * 平台分摊比例
             */
            platformRatio?: number;
            /**
             * 供应商分摊金额
             */
            supplierCost?: number;
            /**
             * 供应商分摊比例
             */
            supplierRatio?: number;
            /**
             * 门槛金额
             */
            threshold?: number;
            [property: string]: unknown;
        }
        /**
         * ActiveData
         */
        export interface ActiveData {
            /**
             * 活动id
             */
            activeId?: number;
            /**
             * 活动名称
             */
            activeName?: string;
            /**
             * 活动库存
             */
            activeNum?: number;
            /**
             * 最小活动价
             */
            activePrice?: number;
            /**
             * 活动说明
             */
            activeRemark?: string;
            /**
             * 状态 1.待提交 2.待发布 3.未开始 4.进行中 5.已结束 6.已取消
             */
            activeStatus?: number;
            /**
             * 活动类型 10.群友购 11.秒杀 13.限量预售 14限量秒杀
             */
            activeType: number;
            boolMaxDiffPrice?: boolean;
            /**
             * 活动佣金
             */
            commission?: number;
            /**
             * 营销文案
             */
            copyWriting?: string;
            /**
             * 活动已优惠价格
             */
            diffPrice?: number;
            /**
             * 结束剩余时间
             */
            endRemainingTime?: number;
            /**
             * 结束时间
             */
            endTime: string;
            fullDecrementConditionModelVOS?: FullDecrementRuleModelVO1;
            /**
             * 满减活动规则
             */
            fullDecrementDetails: string[];
            ladderGroupBuyingMinPriceRuleVO?: LadderGroupBuyingMinPriceRuleVO;
            /**
             * 最大活动价
             */
            maxActivePrice?: number;
            /**
             * 活动最大优惠价格
             */
            maxDiffPrice?: number;
            /**
             * 剩余数量
             */
            residueNum?: number;
            /**
             * 已抢数量
             */
            seckillBuyNum?: number;
            /**
             * 服务器时间
             */
            serverTime: string;
            /**
             * 开始剩余时间
             */
            startRemainingTime?: number;
            /**
             * 开始时间
             */
            startTime: string;
            /**
             * 活动省
             */
            subPrice?: number;
            /**
             * 订阅人数
             */
            subscribeNum?: number;
            /**
             * 活动的时间段id
             */
            subTimeId?: number;
            /**
             * 活动限量总库存数
             */
            totalStock?: number;
            [property: string]: unknown;
        }
        /**
         * GetActiveCouponResDTO
         */
        export interface GetActiveCouponResDTO {
            /**
             * 店长专属券活动id
             */
            activeId?: number;
            /**
             * 券
             */
            coupons?: CouponModel1[];
            /**
             * 活动有效结束时间
             */
            expiryEnd: string;
            /**
             * 活动有效开始时间
             */
            expiryStart: string;
            /**
             * 是否删除 0.正常 1.删除
             */
            isDelete?: number;
            /**
             * 0是未领取完1是领取完
             */
            isReceived?: number;
            /**
             * 是否限领0代表不限制
             */
            limitNum?: number;
            /**
             * 1.不限 2.新用户 3.商品首单券--
             */
            personProperty?: number;
            /**
             * 1.不限  2.店长 3.C端用户
             */
            personScope?: number;
            /**
             * 活动预热时间
             */
            preTimeStart?: Date;
            /**
             * 服务器时间
             */
            serverTime: string;
            /**
             * 活动状态 状态 1.待提交 2.待发布 3.未开始 4.进行中 5.已结束 6.已取消
             */
            status?: number;
            [property: string]: unknown;
        }
        /**
         * CouponModel_1
         */
        export interface CouponModel1 {
            activeNum?: number;
            count?: number;
            couponTab?: string;
            couponType: number;
            discountRule?: string;
            /**
             * 优惠规则 满100-20,满100打2折,最多少20,30元无门槛券
             */
            discountRuleModels: DiscountRuleModel5[];
            activeTime: {
                expiryStart: string;
                expiryEnd: string;
                serverTime: string;
            };
            discountType?: number;
            exchangeNum?: number;
            expiryEnd: string;
            expiryObject?: string;
            expiryStart: string;
            explain?: string;
            fullSubtractType?: number;
            goodsScope?: string;
            goodsScopeModel?: GoodsScopeModel2;
            /**
             * 1.全部 2.指定商品 3.前台类目 4.品牌 5.专题
             */
            goodsScopeType?: number;
            id?: number;
            /**
             * 是否叠加使用：0否，1是
             */
            isAdd?: number;
            isFilterSpu?: number;
            isLimitNewcomer?: number;
            isReceive?: number;
            isSupportShare?: number;
            /**
             * 限制范围名称：只有一个类目和一个品牌的时候才有该值
             */
            limitName?: string;
            /**
             * 是否限领0代表不限制
             */
            limitNum?: number;
            /**
             * 优惠券领取的位置
             */
            listPosition?: number[];
            name: string;
            sort?: number;
            status?: number;
            timeModel: {
                /**
                 * 有效结束时间
                 */
                expiryEnd?: string;
                /**
                 * 有效开始时间
                 */
                expiryStart?: string;
                /**
                 * 领取次天起几日内有效
                 */
                nextDay?: number;
                /**
                 * 领取当天起几日内有效
                 */
                sameDay?: number;
                [property: string]: unknown;
            };
            transferNum?: number;
            usableNum?: number;
            visibility?: number;
            serverTime: string;
            [property: string]: unknown;
        }
        /**
         * DiscountRuleModel_5
         */
        export interface DiscountRuleModel5 {
            /**
             * 无门槛
             */
            cash?: number;
            /**
             * 折扣
             */
            discount?: number;
            /**
             * 最多减
             */
            maxSub?: number;
            /**
             * 减
             */
            sub?: number;
            /**
             * 满
             */
            to?: number;
            [property: string]: unknown;
        }

        /**
         * GoodsScopeModel_2
         */
        export interface GoodsScopeModel2 {
            /**
             * 品牌集合
             */
            brands?: number[];
            /**
             * 类目集合
             */
            categories?: number[];
            /**
             * 是否赔付券 0 否 1 是
             */
            isCompensation?: number;
            isShopExclusive?: number;
            /**
             * 商品id集合
             */
            spuIds?: number[];
            /**
             * 商品列表
             */
            spus?: GoodsScopeSpuModel2[];
            /**
             * 1.全部 2.指定商品 3.前台类目 4.品牌 5.专题
             */
            type?: number;
            [property: string]: unknown;
        }

        /**
         * GoodsScopeSpuModel_2
         */
        export interface GoodsScopeSpuModel2 {
            /**
             * sku列表
             */
            skus?: GoodsScopeSkuModel2[];
            /**
             * 商品spuId
             */
            spuId?: number;
            [property: string]: unknown;
        }

        /**
         * GoodsScopeSkuModel_2
         */
        export interface GoodsScopeSkuModel2 {
            /**
             * sku规格名称
             */
            skuDesc?: string;
            /**
             * skuId
             */
            skuId?: number;
            [property: string]: unknown;
        }
        export type getSupplierByNameOrCode = {
            code?: string;
            id: number;
            name?: string;
            supplierIntroduce?: string;
            supplierType?: number[];
        }[];
        /**
         * 分类类型
         *
         * 查询类目（根据分类类型获取分类树）请求实体
         */
        export type getCategoryIdsReq = {
            /**
             * 类目类型 1.前台 2.后台 3.小b
             */
            type: number;
            [property: string]: unknown;
        };
        export type getSupplierGoodsPage = {
            dataList: {
                /**
                 * 商品头图
                 */
                headPic?: string;
                /**
                 * 最小价格sku规格
                 */
                minSkuDesc?: string;
                /**
                 * 短标题
                 */
                shortTitle?: string;
                /**
                 * spuId
                 */
                spuId?: number;
                /**
                 * 商品名称
                 */
                spuName?: string;
                /**
                 * 商品主图
                 */
                url?: string;
            }[];
            total: number;
        };
        /**
         * 查询类目（根据类目类型获取类目树）结果实体
         */
        export type getCategoryIdsRes = {
            /**
             * 子类目
             */
            // children: 查询类目根据类目类型获取类目树结果实体[];
            /**
             * 发货时效
             */
            goodsDeliveryAgeing?: number;
            /**
             * 类目图标
             */
            icon: string;
            /**
             * 类目id
             */
            id: number;
            /**
             * 类目名字
             */
            name: string;
            /**
             * 类目的父级id
             */
            parentId: number;
            /**
             * 类目父级名字
             */
            parentName: string;
            /**
             * 排序
             */
            sort: number;
            /**
             * 启用状态 true=启动 false=禁用
             */
            status: boolean;
            /**
             * 类目类型 1.前台 2.后台 3.小b
             */
            type: number;
            // [property: string]: unknown;
        };
    }
}
