/**
 * @Owners haoyang
 * @Title 基础-类型
 */

/** 状态枚举 */
export enum Status {
    /** 不启用 */
    INVALID = 0,
    /** 启用 */
    VALID = 1,
}

/** 基本类型 */
export interface Base {
    /** id */
    id: number;
    /** 状态 */
    status: Status;
    /** 创建者 */
    createBy?: string;
    /** 修改者 */
    updateBy: string;
    /** 创建时间 */
    gmtCreate?: Date;
    /** 修改时间 */
    gmtModified: Date;
}

/** 分页请求 */
export interface Page<T = {}> {
    /** 条件 */
    condition?: T;
    /** 页数 */
    page: number;
    /** 每页条数 */
    rows: number;
    /** 顺序 */
    order?: string;
    /** 排序方式 */
    sort?: string;
}
