/**
 * @Owners felix.cai
 * @Title 红包雨
 */
import { type Coupon } from '../coupon';
export namespace RedPacket {
    export type ActiveLotteryCouponBaseResVO = {
        /** 优惠券ID */
        couponBaseId: number;
        /** 优惠券名称 */
        name: string;
        /** 优惠券类型 */
        discountType: number;
        /** 满减类型 */
        fullSubtractType: number;
        /** 优惠券规则 */
        discountRuleModels: {
            to: number;
            sub: number;
            discount: number;
            maxSub: number;
            cash: number;
        }[];
        /** 商品范围类型 */
        goodsScopeType: number;
        /** 解释 */
        explain: string;
        /** 商品列表 */
        spuInfoList: {
            /** 商品ID */
            spuId: number;
            /** 商品名称 */
            spuName: string;
            /** 商品图片 */
            spuPic: string;
            categoryName: string;
        }[];
    };
    export type ActiveRedPacketRainRuleReqVO = {
        /** 中奖用户类型（0-不限制 1-指定用户） */
        userType?: number;
        /** 预估人数 */
        allEstimateNum?: number;
        /**
         * 规则配置列表
         */
        prizeConfigList: PrizeConfig[];
    };
    export type PrizeConfig = {
        /** 奖品ID */
        prizeId?: number;
        /** 奖品Key */
        prizeKey?: string;
        /** 奖品类型 */
        prizeType?: number;
        /** 奖品来源 */
        prizeSource?: number;
        /** 奖品名称 */
        prizeName?: string;
        /** 奖品规格 */
        prizeSpec?: string;
        /** 奖品图片 */
        prizeImage?: string;
        /** 概率 */
        probability?: number;
        /** 可用数量 */
        usableNum?: number;
        /** 总数 */
        allNum?: number;
        /** 奖品数量 */
        prizeNum?: number;
        /** 已使用数量 */
        usedNum?: number;
        /** 未使用数量 */
        notUsedNum?: number;
        /** 已领取数量 */
        receivedPrizeNum?: number;
        /** 待领取数量 */
        beReceivedPrizeNum?: number;
        /** 已过期数量 */
        expiredPrizeNum?: number;
        /**
         * 优惠券信息
         */
        couponBaseRes?: Coupon.Response.supplierLiveCouponPage['dataList'][number];
    };

    export type BaseActiveRedPacketRain = {
        id?: number;
        /**
         * 规则配置信息
         */
        activeRedPacketRainRule: ActiveRedPacketRainRuleReqVO;
        /**
         * 活动名称
         */
        activityName: string;
        /**
         * 活动门槛（0 - 不限 1 - 已关注主播用户可领取 2 - 仅分享可领取）
         */
        activityThreshold: number;
        /**
         * 红包雨元素集合
         */
        elementImages?: string[];
        /**
         * 结束时间
         */
        endTime?: string;
        /**
         * 红包雨浮层样式图片
         */
        floatingLayerImage: string;
        /**
         * 红包雨浮层样式（1 - 标准样式 2 - 喜临门 3 - 福满多 4 - 自定义）
         */
        floatingLayerType: number;
        /**
         * 直播间ID
         */
        foreignId: number;
        /**
         * 红包雨领取弹窗图片
         */
        popImage?: string;
        /**
         * 开始时间
         */
        startTime: string;
    };

    export namespace Params {
        export type LiveGoodsCustomSpuNameReq = {
            /**
             * 自定义商品spu名称
             */
            customSpuName?: string;
            /**
             * 直播间id
             */
            liveId: number;
            /**
             * 商品ID
             */
            spuId: number;
        };

        export type GetActiveRedPacketRainCount = {
            /**
             * 活动状态 1-待开始，2-进行中，3-已结束
             */
            activityStatus?: number;
            /**
             * 直播间ID
             */
            foreignId: number;
        };
        export type GetActiveRedPacketRainList = {
            condition?: {
                /**
                 * 活动状态 1-待开始，2-进行中，3-已结束
                 */
                activityStatus?: number;
                /**
                 * 关联外键ID,如直播间，则传直播间ID
                 */
                foreignId: number;
            };
            order?: string;
            page?: number;
            rows?: number;
            sort?: string;
        };

        export type UpdateActiveRedPacketRainOpenStatus = Partial<{
            /**
             * 活动ID
             */
            id: number;
            /**
             * 开启/关闭状态（0：关闭 1：开启）
             */
            openStatus: number;
        }>;

        export type ExportActiveRedPacketRainUseLotteryPrize = Partial<{
            /**
             * 用户昵称
             */
            userNickname?: string;
            /**
             * 领取状态（1-待发放，2-待领取, 3-已领取 4-已过期）
             */
            distributionStatus?: number;
            /**
             * 中奖券码
             */
            foreignId?: string;
            /**
             * 活动ID
             */
            lotteryId: number;
            /**
             * 奖品名称
             */
            prizeName?: string;
            /**
             * 奖品类型（1-虚拟奖品，2-优惠券）
             */
            prizeType?: number;
            /**
             * 获奖人
             */
            prizeUser?: string;
            /**
             * 获奖账号（获奖人手机号）
             */
            userPhone?: string;
            /**
             * 核销状态（0-未核销 1-已核销）
             */
            verifyStatus?: number;
        }>;

        export type GetActiveLuckBagReceiveDetail = {
            /**
             * 福袋id
             */
            bagId?: number;
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 页码, 默认1
             */
            page?: number;
            /**
             * 领取用户信息
             */
            receiverUserMsg?: string;
            /**
             * 条数, 默认10
             */
            rows?: number;
            /**
             * 查询类型 1:全部 2:已使用 3:未使用
             */
            type?: number;
        };

        export type GetActiveRedPacketRainUseLotteryPrizeList = {
            condition?: {
                /**
                 * 搜索关键词（用户昵称/手机号）
                 */
                keyword?: string;
                /**
                 * 活动ID
                 */
                lotteryId: number;
                /**
                 * 奖品ID
                 */
                prizeId: number;
                /**
                 * 奖品类型（1-虚拟奖品，2-优惠券 99-谢谢惠顾）
                 */
                prizeType: number;
                /**
                 * 查询列表类型 1-中奖用户 2-已使用 3-未使用 4-已领取 5-待领取 6-已过期
                 */
                selectType: number;
            };
            order?: string;
            page?: number;
            rows?: number;
            sort?: string;
        };

        export type ReceiveActiveRedPacketRainUseLotteryPrize = Partial<{
            /**
             * 物流公司编码
             */
            courierCompanyCode: string;
            /**
             * 物流公司名称
             */
            courierCompanyName?: string;
            /**
             * 奖品ID
             */
            id: number;
            /**
             * 物流单号
             */
            logisticsCode: string;
        }>;

        export type GetSkuDescBySpuId = {
            spuId: number;
        };

        export type GetActiveRedPacketRainRealPrizeList = Partial<{
            condition?: {
                /**
                 * 奖品名称
                 */
                prizeName?: string;
                /**
                 * 奖品id
                 */
                realPrizeId?: string;
                /** 奖品状态：0-已下架；1-上架中 */
                status?: number;
            };
            order?: string;
            page?: number;
            rows?: number;
            sort?: string;
        }>;
    }
    export namespace Response {
        export type ActiveRedPacketRainRuleRespVO = {
            /**
             * 预估人数
             */
            allEstimateNum: number;
            /**
             * 指定用户类型集合 SHOP-会员 SHOP_NEW-会员新用户 SHOP_OLD-会员旧用户 NOT_SHOP-非会员 NOT_SHOP_NEW-非会员新用户
             * NOT_SHOP_OLD-非会员旧用户
             */
            appointUserTypeList?: string[];
            /**
             * 非会员预估人数
             */
            notShopEstimateNum: number;
            /**
             * 新非会员预估人数
             */
            notShopNewEstimateNum: number;
            /**
             * 旧非会员预估人数
             */
            notShopOldEstimateNum: number;
            /**
             * 规则配置列表
             */
            prizeConfigList?: PrizeConfig[];
            /**
             * 会员预估人数
             */
            shopEstimateNum: number;
            /**
             * 新会员预估人数
             */
            shopNewEstimateNum: number;
            /**
             * 旧会员预估人数
             */
            shopOldEstimateNum: number;
            /**
             * 中奖用户类型（0-不限制 1-指定用户）
             */
            userType?: number;
        };

        export type GetActiveRedPacketRainDetail = {
            /** 活动ID */
            id?: number;
            /** 直播间ID */
            foreignId?: number;
            /** 主播用户ID */
            foreignUserId?: number;
            /** 外键类型（1 - 直播间ID） */
            foreignType?: number;
            /** 活动开启状态 0-关闭 1-开启 */
            openStatus?: number;
            /** 活动状态 1-待开始，2-进行中，3-已结束 */
            activityStatus?: number;
            /** 活动类型（1 - 红包雨） */
            activityType?: number;
            /** 活动名称 */
            activityName?: string;
            /** 活动门槛 */
            activityThreshold?: number;
            /** 奖励类型 */
            rewardType?: number;
            /** 活动开始时间 */
            startTime?: string;
            /** 活动结束时间 */
            endTime?: string;
            /** 领取方式 1-活动开始后自动弹出 2-活动开始后用户手动领取 */
            receiveType?: number;
            /** 红包雨样式配置 */
            floatingLayerType?: number;
            /** 红包雨样式配置 */
            floatingLayerImage?: string;
            /** 红包雨样式配置 */
            popImage?: string;
            /** 红包雨样式配置 */
            elementImages?: string[];
            /** 倒计时时间 */
            countdownTime?: number;
            /** 领取时限 */
            receiveTime?: number;
            /** 发货时间 */
            deliverDay?: number;
            /** 创建时间 */
            gmtCreate?: string;
            /** 修改时间 */
            gmtModified?: string;
            /** 活动规则 */
            activeRedPacketRainRule?: ActiveRedPacketRainRuleReqVO;
        };

        export type ActiveLotteryAnchorPrizeRespDTO = {
            /** 奖品ID */
            prizeId?: number;
            /** 奖品类型 */
            prizeType?: number;
            couponBaseResDTO?: Coupon.Response.supplierLiveCouponPage['dataList'][number];
            /** 奖品信息 */
            prizeInfoDTO?: {
                /** 奖品名称 */
                prizeName?: string;
                /** 奖品规格 */
                prizeSpec?: string;
                /** 奖品图片 */
                prizeImage?: string;
            };
        };

        export type GetActiveRedPacketRainStat = {
            /**
             * 活动参与人数
             */
            partNum?: number;
            /**
             * 活动中奖人数
             */
            prizeNum?: number;
        };

        export type ActiveRedPacketRainPageRespVO = {
            /** 活动ID */
            id?: number;
            /** 直播间ID */
            foreignId?: number;
            /** 主播用户ID */
            foreignUserId?: number;
            /** 外键类型（1 - 直播间ID） */
            foreignType?: number;
            /** 活动开启状态 0-关闭 1-开启 */
            openStatus?: number;
            /** 活动状态 1-待开始，2-进行中，3-已结束 */
            activityStatus?: number;
            /** 活动类型（1 - 红包雨） */
            activityType?: number;
            /** 活动名称 */
            activityName?: string;
            /** 活动门槛（0 - 不限 1 - 已关注主播用户可领取 2 - 仅分享可领取） */
            activityThreshold?: number;
            /** 开始时间 */
            startTime?: string;
            /** 结束时间 */
            endTime?: string;
            /** 领取方式（1 - 用户点击领取 2 - 用户自动触发） */
            receiveType?: number;
            /** 活动描述 */
            activityDesc?: string;
            /** 服务器时间 */
            serverTime?: string;
            /** 活动参与人数 */
            partNum?: number;
            /** 活动中奖人数 */
            prizeNum?: number;
            /** 创建来源 1-主播端/中控台 2-中台 */
            source?: number;
            /** 活动中奖信息列表 */
            activeLotteryPrizeList?: ActiveLotteryAnchorPrizeRespDTO[];
        };

        export type GetActiveRedPacketRainCount = {
            /**
             * 全部数量
             */
            count?: number;
            /**
             * 已结束数量
             */
            endedCount?: number;
            /**
             * 进行中数量
             */
            inProgressCount?: number;
            /**
             * 待开始数量
             */
            toBeginCount?: number;
        };

        export type GetActiveRedPacketRainList = {
            /**
             * 集合
             */
            dataList: ActiveRedPacketRainPageRespVO[];
            /**
             * 总条数
             */
            total: number;
        };

        export type ActiveUserLotteryPrizeAddressVO = {
            /**
             * 详细地址
             */
            address?: string;
            /**
             * 市
             */
            city?: string;
            /**
             * 市编码
             */
            cityCode?: string;
            /**
             * 区
             */
            district?: string;
            /**
             * 区编码
             */
            districtCode?: string;
            /**
             * 省
             */
            province?: string;
            /**
             * 省编码
             */
            provinceCode?: string;
            /**
             * 收件人姓名
             */
            receiverName?: string;
            /**
             * 收件人手机号码
             */
            receiverPhone?: string;
            /**
             * 用户收货地址id
             */
            userAddressId?: number;
        };

        export type ActiveUserLotteryPrizeRespVO = {
            /**
             * 奖品ID
             */
            prizeId?: number;
            /**
             * 奖品类型（1-虚拟奖品，2-优惠券 99-谢谢惠顾）
             */
            prizeType?: number;
            /**
             * 查询列表类型 1-中奖用户 2-已使用 3-未使用 4-已领取 5-待领取 6-已过期
             */
            selectType?: number;
            /**
             * 用户头像url
             */
            userAvatar?: string;
            /**
             * 用户ID
             */
            userId?: number;
            /**
             * 用户昵称
             */
            userNickname?: string;
            /**
             * 用户手机号
             */
            userPhone?: string;
            /** 状态 2-已使用 3-未使用 4-已领取 5-待领取 6-已过期 */
            status?: number;
        };

        export type LuckyBagInfoReceiverRespVO = {
            /**
             * 领取人头像
             */
            avatar?: string;
            /**
             * 领取人昵称
             */
            nickName?: string;
            /**
             * 未使用数量
             */
            notUsedNum?: number;
            /**
             * 领券人手机
             */
            phone?: string;
            /**
             * 已使用数量
             */
            usedNum?: number;
        };

        export type GetActiveLuckBagReceiveDetail = {
            /**
             * 集合
             */
            dataList: LuckyBagInfoReceiverRespVO[];
            /**
             * 总条数
             */
            total: number;
        };

        export type GetActiveRedPacketRainUseLotteryPrizeList = {
            /**
             * 集合
             */
            dataList: ActiveUserLotteryPrizeRespVO[];
            /**
             * 总条数
             */
            total: number;
        };

        export type ActiveRedPacketRealPrizeListRespVO = {
            /**
             * 库存数
             */
            num?: number;
            /**
             * 奖品id
             */
            realPrizeId?: number;
            /**
             * 奖品图片
             */
            prizeImage?: string;
            /**
             * 奖品名称（如果是优惠券，则用优惠券名称）
             */
            prizeName: string;
            /**
             * 奖品规格信息
             */
            prizeSpec?: string;
            /** 奖品状态：0-已下架；1-上架中 */
            status?: number;
        };

        export type GetSkuDescBySpuId = {
            data: {
                /**
                 * 商品规格
                 */
                skuDesc?: string;
                /**
                 * 商品skuId
                 */
                skuId?: number;
                /**
                 * 商品spuId
                 */
                spuId?: number;
            }[];
        };

        export type GetAnchorIsSupplier = {
            /**
             * 是否供应商 0-否 1-是
             */
            isSupplier?: number;
            /**
             * 用户ID
             */
            userId?: number;
        };

        export type HasLiveActive = {
            /**
             * 是否有红包雨活动 1是 0否
             */
            hasLottery?: number;
            /**
             * 是否有幸运袋活动 1是 0否
             */
            hasLuckyBag?: number;
            /**
             * 直播间id
             */
            liveId?: number;
        };

        export type GetActiveRedPacketRainRealPrizeList = {
            /**
             * 集合
             */
            dataList: ActiveRedPacketRealPrizeListRespVO[];
            /**
             * 总条数
             */
            total: number;
        };
    }
}
