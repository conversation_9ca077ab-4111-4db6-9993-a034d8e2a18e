/**
 * @Owners xj
 * @Title 直播中控台
 */
import { type IMMessageStatus } from '@/consts/cLive';
import { type Message } from '@tencentcloud/chat';

export namespace Console {
    export namespace Params {
        export type OnlyLiveId = {
            liveId?: number;
        };

        export type LiveDetailInfo = {
            liveId?: number;
        };

        export type UpdateLive = {
            id?: number;
            pushType?: number;
            coverUrl?: string;
            liveCoverUrl?: string;
            liveTheme?: string;
            preStartTime?: string;
            liveName?: string;
        };

        export type PushLiveOpenNotice = {
            id?: number;
        };

        export type OperateLive = {
            id?: number;
            type?: number;
        };

        export type GetStreamUrl = {
            id?: number;
        };

        export type FindLiveGoodsByPage = {
            condition: {
                liveId: number;
                type: number;
                customerCategoryId?: number;
            };
            page: number;
            rows: number;
        };

        /**
         * LiveExclusivePriceOnOffReqDTO
         */
        export type LiveExclusivePriceOnOffReqDTO = {
            /**
             * 直播间房间ID
             */
            liveId?: number;
            /**
             * 状态 110-待生效 200-已生效
             */
            priceStatus?: number;
            /**
             * 商品sku id
             */
            skuId?: number;
            // [property: string]: unknown,
        };

        export type AddLiveGoods = {
            liveId: number;
            spus?: {
                serialNo?: number;
                sort?: number;
                spuId?: number;
            }[];
            unupSpus?: {
                serialNo?: number;
                sort?: number;
                spuId?: number;
            }[];
        };
        export type DeleteLiveGoods = {
            liveId: number;
            spuId?: number;
            spuIds?: number[];
            optType: number;
        };
        export type SaySortLiveGoods = {
            liveId: number;
            spuId: number;
        };
        /**
         * SetLiveGoodsTopReqDTO
         */
        export type SetLiveGoodsTopReqDTO = {
            liveId: number;
            spuId?: number;
            spuIds?: number[];
            top: number;
            // [property: string]: unknown;
        };
        export type SetLiveGoodsSort = {
            liveId: number;
            sort: number;
            spuId: number;
            optType: number;
        };

        export type ResetLiveGoodsSerialNo = {
            liveId: number;
        };
        /**
         * PageReqDTOLiveGroupOnlineUsersReqVO
         */
        export type PageReqDTOLiveGroupOnlineUsersReqVO = {
            condition?: LiveGroupOnlineUsersReqVO;
            order?: string;
            page?: number;
            rows?: number;
            sort?: string;
            // [property: string]: unknown;
        };

        /**
         * LiveGroupOnlineUsersReqVO
         */
        export type LiveGroupOnlineUsersReqVO = {
            /**
             * 群组id
             */
            groupId?: number;
            /**
             * 直播间ID
             */
            liveId?: number;
            /**
             * 昵称
             */
            nickname?: string;
            /**
             * 查询类型 1:在线 2:禁言
             */
            queryType?: number;
            [property: string]: unknown;
        };

        /**
         * LiveGroupBlockUsersReqVO
         */
        export type LiveGroupBlockUsersReqVO = {
            /**
             * 直播间ID
             */
            liveId?: number;
            /**
             * 昵称
             */
            nickname?: string;
            /**
             * 查询类型 1:当前直播间拉黑用户 2:永久拉黑用户
             */
            queryType?: number;
            [property: string]: unknown;
        };

        export type SetUserMuted = {
            /** 用户ID */
            userId?: number;
            /** 群组id */
            groupId?: number;
            /** 是否禁言 0否 1是 */
            muteStatus?: number;
            /** 直播间ID */
            liveId?: number;
            nickName?: string;
        };

        export type GetUserMutedList = {
            /** 群组id */
            groupId?: number;
        };

        /**
         * 获取拉黑用户列表
         */
        export type GetBlockUserList = {
            page?: number;
            rows?: number;
            condition?: LiveGroupOnlineUsersReqVO;
        };

        export type SetGlobalMute = {
            liveId?: number;
            /** 是否禁言 0否 1是 */
            muteStatus?: number;
        };

        export type GetCommentList = {
            anchorImMsgId?: string;
            groupId?: number;
            size?: number;
        };

        export type SetCommentStatus = {
            /** 腾讯云IM消息id */
            imMsgId?: string;
            /** IM群组ID */
            groupId?: number;
            /** 操作类型 1.评论隐藏/取消隐藏 */
            controlType?: number;
            /** 控制操作 0.关闭 1.打开 */
            controlOperate?: number;
        };

        export type SendComment = {
            /**
             * 群id
             */
            groupId?: number;
            /**
             * 等级
             */
            level?: number;
            /**
             * 等级图标
             */
            levelIcon?: string;
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 消息内容
             */
            msgContentText?: string;
            /**
             * 角色 1-普通用户 2-会员
             */
            role?: number;
            /**
             * 发送者id
             */
            senderId?: number;
            /**
             * 发送者昵称
             */
            senderNickname?: string;
            /**
             * 用户列表
             */
            atUserList?: AtUser[];
        };

        export type PrizeConfig = {
            /**
             * 奖品数（不限制）
             */
            allNum: number;
            /**
             * 奖品图片
             */
            prizeImage: string;
            /**
             * 奖品Key（如果是优惠券则存券模版ID，如果是实物奖品就用实物奖品库ID,如果是商城商品，就取商品spuId）
             */
            prizeKey: string;
            /**
             * 奖品名称
             */
            prizeName: string;
            /**
             * 实物奖品来源 1-实物奖品库 2-平台商品
             */
            prizeSource?: number;
            /**
             * 奖品规格
             */
            prizeSpec: string;
            /**
             * 奖品类型（1-虚拟奖品，2-优惠券 99-谢谢惠顾）
             */
            prizeType: number;
            /**
             * 通用中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            probability?: number;
        };

        export type PrizeConfig1 = {
            /**
             * 奖品数（不限制）
             */
            allNum?: number;
            /**
             * 奖品数（非会员新用户）
             */
            notShopNewNum?: number;
            /**
             * 非会员新用户中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            notShopNewProbability?: number;
            /**
             * 奖品数（非会员）
             */
            notShopNum?: number;
            /**
             * 奖品数（非会员老用户）
             */
            notShopOldNum?: number;
            /**
             * 非会员老用户中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            notShopOldProbability?: number;
            /**
             * 非会员中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            notShopProbability?: number;
            /**
             * 奖品Key（如果是优惠券则存券模版ID，如果是实物奖品就用实物奖品库ID）
             */
            prizeKey?: string;
            /**
             * 奖品名称
             */
            prizeName?: string;
            /**
             * 奖品类型（1-虚拟奖品，2-优惠券 99-谢谢惠顾）
             */
            prizeType: number;
            /**
             * 通用中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            probability?: number;
            /**
             * 奖品数（新会员）
             */
            shopNewNum?: number;
            /**
             * 会员新用户中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            shopNewProbability?: number;
            /**
             * 奖品数（会员）
             */
            shopNum?: number;
            /**
             * 奖品数（会员老用户）
             */
            shopOldNum?: number;
            /**
             * 会员老用户中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            shopOldProbability?: number;
            /**
             * 会员中奖概率,整数，如：70代表70%，99.99代表99.99%
             */
            shopProbability?: number;
        };
        export type TopRequest = {
            liveId: number;
            topN: number;
        };

        export type UserPortraitsDataRequest = {
            liveId: number;
            /**
             * tab类型：order:成交用户 view:看播用户
             */
            tabType: 'order' | 'view';
            // [property: string]: any;
        };

        export type UpdateLiveBlockKeywords = {
            liveId: number;
            keywords?: string[];
        };

        /**
         * LiveEpigraphTextReqDTO
         */
        export type LiveEpigraphTextReqDTO = {
            /**
             * 文本
             */
            epigraphText?: string;
            /**
             * 直播间ID
             */
            liveId?: number;
            /**
             * 商品ID
             */
            spuId?: number;
            /**
             * 1=商品话术，2=案例分析
             */
            type: number;
            // [property: string]: unknown;
        };

        export type liveEpigraphTextRequest = {
            /**
             * 直播间ID
             */
            liveId?: number;
            /**
             * 商品ID
             */
            spuId?: number;
            /**
             * 1=商品话术，2=案例分析
             */
            type: number;
            // [property: string]: any;
        };

        /**
         * LiveAnalysisReqDTO
         */
        export type LiveAnalysisReqDTO = {
            /**
             * 直播间id
             */
            liveId?: number;
            // [property: string]: unknown;
        };
        export type UpdateNotice = {
            liveId: number;
            announcement?: string;
        };

        export type UpdateBroadcast = {
            liveId: number;
            broadcastContent?: string;
        };

        export type GetLiveGoodsCount = {
            liveId: number;
            type: number;
        };

        export type UpdateEnableMic = {
            liveId?: number;
            /** 状态 0 关闭 1 开启 */
            status?: number;
        };

        export type GetAudienceList = {
            page?: number;
            rows?: number;
            order?: string;
            sort?: string;
            condition?: {
                liveId?: number;
                nickname?: string;
                ifRefresh?: number;
            };
        };

        export type InviteCoStream = {
            liveId?: number;
            /** 被邀请用户ID */
            targetUsers?: number;
        };

        export type GetInteractiveConfig = {
            liveId?: number;
        };

        export type SaveInteractiveConfig = {
            /**
             * 是否允许开启摄像头（0: 不允许, 1: 允许）
             */
            allowCamera?: number;
            /**
             * 用户连麦是否需要申请（0: 无需申请, 1: 需要申请）
             */
            auditStrategy?: number;
            /**
             * 加入时是否自动静音（0: 非静音, 1: 静音）
             */
            autoMute?: number;
            /**
             * 默认连麦类型（1: 音频, 2: 视频）
             */
            defaultMedia?: number;
            /**
             * 直播间连麦总开关（0: 关闭, 1: 开启）
             */
            enableMic?: number;
            /**
             * 直播间ID
             */
            liveId?: number;
        };

        export type GetApplyCoList = {
            liveId?: number;
            userId?: number;
            nickname?: string;
            anchorApplyId?: number;
            size?: number;
            // page?: number;
            // rows?: number;
            // order?: string;
            // sort?: string;
            // condition?: {
            //     liveId?: number;
            //     userName?: string;
            // };
        };

        export type AcceptApplyCo = {
            id?: number;
            liveId?: number;
            userId?: number;
            nickname?: string;
        };

        export type UpdateMicStatusOfUser = {
            /** 直播间ID */
            liveId?: number;
            /** 用户ID */
            userId?: number;
            /** 是否静音 0 非静音 1 静音 */
            status?: number;
        };

        export type UpdateCameraStatusOfUser = {
            /** 直播间ID */
            liveId?: number;
            /** 用户ID */
            userId?: number;
            /** 是否静音 0 关闭 1 打开 */
            status?: number;
        };

        export type DiconnectStreamOfUser = {
            /** 直播间ID */
            liveId?: number;
            /** 用户ID */
            userId?: number;
        };

        export type GetCoStreamList = {
            liveId?: number;
        };

        export type UpdateLiveManualComment = {
            liveId?: number;
            preCommentItems?: Response.LiveManualCommentRecord[];
        };
        /**
         * GetLiveGoodsCategoryReqVO
         */
        export type GetLiveGoodsCategoryReqVO = {
            /**
             * 直播间id
             */
            liveId?: number;
            // [property: string]: unknown;
        };
        /**
         * AddLiveGoodsCategoryReqVO
         */
        export type AddLiveGoodsCategoryReqVO = {
            /**
             * 类别名称
             */
            categoryName?: string;
            /**
             * 直播间id
             */
            liveId?: number;
            [property: string]: unknown;
        };
        /**
         * OptLiveGoodsCategoryReqVO
         */
        export type OptLiveGoodsCategoryReqVO = {
            /**
             * 类别名称-修改名称时必传
             */
            categoryName?: string;
            /**
             * id
             */
            id?: number;
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 序号-排序时必传
             */
            sort?: number;
            [property: string]: unknown;
        };
        /**
         * OptLiveGoodsCategoryReqVO
         */
        export type OpdateLiveGoodsCategoryReqVO = {
            /**
             * 类别名称-修改名称时必传
             */
            categoryName?: string;
            /**
             * id
             */
            id?: number;
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 序号-排序时必传
             */
            sort?: number;
            [property: string]: unknown;
        };

        /**
         * OptLiveGoodsCategoryReqVO
         */
        export type OptSoftLiveGoodsCategoryReqVO = {
            /**
             * 类别名称-修改名称时必传
             */
            categoryName?: string;
            /**
             * id
             */
            id?: number;
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 序号-排序时必传
             */
            sort?: number;
            [property: string]: unknown;
        };
        /**
         * UpdateAllLiveGoodsCategoryReqVO
         */
        export type UpdateAllLiveGoodsCategoryReqVO = {
            /**
             * 直播间类别
             */
            liveGoodsCategoryList: Response.LiveGoodsCategoryVO[];
            /**
             * 直播间id
             */
            liveId: number;
            // [property: string]: unknown;
        };

        /**
         * EditLiveGoodsReqVO
         */
        export type EditLiveGoodsReqVO = {
            liveGoods?: LiveGoodsInfo[];
            /**
             * type=1,单个修改，type=2批量修改
             */
            type: number;
            // [property: string]: unknown;
        };

        /**
         * LiveGoods
         */
        export type LiveGoodsInfo = {
            /**
             * 自定义商品分类
             */
            customerCategoryId?: number;
            /**
             * 自定义商品spu名称
             */
            customSpuName?: string;
            /**
             * 直播间id
             */
            liveId: number;
            /**
             * 商品卖点
             */
            sellingPoint?: string;
            /**
             * spuId
             */
            spuId?: number;
            // [property: string]: unknown;
        };

        /**
         * LiveGoodsImportReqVO
         */
        export type LiveGoodsImportReqVO = {
            /**
             * 导入的直播id
             */
            importLiveId?: number;
            /**
             * 直播id
             */
            liveId: number;
            // [property: string]: unknown;
        };

        /**
         * PublishLiveGoodsReqVO
         */
        export type PublishLiveGoodsReqVO = {
            /**
             * 直播id
             */
            liveId: number;
            /**
             * spuId
             */
            spuId: number;
            // [property: string]: unknown;
        };

        /**
         * UpdateLiveConfig
         */
        export type UpdateLiveConfig = {
            /**
             * 是否推流预览 1-是 0-否
             */
            isPrePush?: number;
            /**
             * 直播间id
             */
            liveId: number;
            /**
             * 直播预推流方式 1-手动推流 2-自动推流
             */
            prePushType?: number | null;
        };

        /**
         * 根据热销数据排序直播商品
         */
        export type SortLiveGoodsByHotSalesReqVO = {
            /**
             * 直播id
             */
            liveId: number;
            /**
             * 商品类型 0 上架中商品 1 待上架商品
             */
            goodsType: 0 | 1;
            /**
             * 排序方式 0 降序 1 升序 默认降序
             */
            sortType: number;
        };

        /**
         * 评论上墙
         */
        export type SetUpperWall = {
            liveId: number;
            message: string;
            nickName: string;
        };

        /**
         * 拉黑/解禁用户
         */
        export type SetUserBlock = {
            liveId: number;
            userId: number;
            /**
             * 操作类型 0:当前直播间拉黑 1:解除当前直播间拉黑 2:永久拉黑 3:解除永久拉黑
             */
            operateType: 0 | 1 | 2 | 3;
        };
        export interface LiveOrderReqVO {
            /**
             * 直播间ID
             */
            liveId: number;
            /**
             * 订单编号或用户昵称
             */
            orderCodeOrReceiverNick?: string;
            /**
             * 订单状态
             */
            orderStatus?: number;

            orderCode?: string;

            goodsName?: string;
        }
        export interface OrderListReqVO {
            /**
             * 查询条件
             */
            condition?: LiveOrderReqVO;
            /**
             * 排序 asc/desc
             */
            order?: string;
            /**
             * 页码，默认1
             */
            page?: number;
            /**
             * 条数，默认10
             */
            rows?: number;
            /**
             * 按字段排序
             */
            sort?: string;
        }
    }
    export namespace Response {
        /** 商品分析 */
        export type GoodsAnalysisRes = {
            /**
             * 集合
             */
            dataList: {
                /** 前端排名字段 */
                sortIndex: number;
                /**
                 * 全单访问商品详情页人数
                 */
                fullAccessUsers?: number;
                /**
                 * 全单加购人数
                 */
                fullAddcarUsers?: number;
                /**
                 * 全单点击数
                 */
                fullClickUsers?: number;
                /**
                 * 全单首单用户数
                 */
                fullFirstOrderUsers?: number;
                /**
                 * 全单成交件数
                 */
                fullOrderSkuNumbers?: number;
                /**
                 * 全单累积金额
                 */
                fullOrderTotalGmv?: number;
                /**
                 * 全单成交人数
                 */
                fullOrderUsers?: number;
                /**
                 * 全单复购用户数
                 */
                fullRepayOrderUsers?: number;
                /**
                 * 全单浏览人数
                 */
                fullViewUsers?: number;
                /**
                 * 全单点击数
                 */
                goodsAnalysisList?: {
                    /**
                     * 评论集合
                     */
                    commentList?: string[];
                    /**
                     * 内容
                     */
                    content?: string;
                    /**
                     * 等级 0=无，1=低，2=中，3=高
                     */
                    level: number;
                    /**
                     * 标题
                     */
                    title?: string;
                }[];
                /**
                 * 跨境商品 2 跨境 1 普通
                 */
                goodsType?: number;
                liveId?: number;
                /**
                 * 直播间类型 公开直播= 1 ，私密直播 = 2
                 */
                liveType?: number;
                /**
                 * 库存数
                 */
                numberOfInventories?: number;
                /**
                 * 价格
                 */
                price?: number;
                /**
                 * 纯单访问商品详情页人数
                 */
                pureAccessUsers?: number;
                /**
                 * 纯单加购人数
                 */
                pureAddcarUsers?: number;
                /**
                 * 纯单点击数
                 */
                pureClickUsers?: number;
                /**
                 * 纯单首单用户数
                 */
                pureFirstOrderUsers?: number;
                /**
                 * 纯单成交件数
                 */
                pureOrderSkuNumbers?: number;
                /**
                 * 纯单累积金额
                 */
                pureOrderTotalGmv?: number;
                /**
                 * 纯单成交人数
                 */
                pureOrderUsers?: number;
                /**
                 * 纯单复购用户数
                 */
                pureRepayOrderUsers?: number;
                /**
                 * 纯单浏览人数
                 */
                pureViewUsers?: number;
                /**
                 * 链接序号
                 */
                serialNo?: number;
                /**
                 * 商品ID
                 */
                spuId?: number;
                /**
                 * 是否讲解 0 不讲解 1 讲解中
                 */
                spuIsExplain?: number;
                /**
                 * 商品名称
                 */
                spuName?: string;
                /**
                 * 商品图片
                 */
                spuPic?: string;

                /**
                 * 展示直播奖励标识 1:展示 0:不展示
                 */
                showReward: number;

                /**
                 * 直播奖励
                 */
                pureRewardAmt: number;
            }[];
            /*
             * 总条数
             */
            total: number;
        };
        export type LiveDetailInfo = {
            actualEndTime: string; // 实际开播时间
            actualStartTime: string; // 实际开播时间
            announcement: string; // 公告
            broadcastContent: string; // 广播
            id: number;
            /** 是否开启连麦 0.否 1.是 */
            enableMic: number;
            coverUrl: string; // 直播间分享封面
            liveCoverUrl: string; // 直播间封面
            liveName: string; // 直播间名称
            liveTheme: string; // 直播主题
            roomCode: number; // 房间号
            pushStatus: number; // 推流状态
            isRecord: number; // 是否开启录播 0.否 1.是
            isMute: number; // 是否禁言 0否 1是
            imGroupId: number; // 直播间群组ID
            imUserId: number; // 直播间用户ID
            liveCategory: number; // 直播间类型 1.即时开播 2.直播预告
            liveMode: number; // 直播间方式 1.横屏直播 2.竖屏直播
            liveType: number; // 直播间类别 1.公开直播 2.私密直播
            pushType: number; // 推流类型 1.外设推流 2.手机推流
            preStartTime: string; // 直播时间
            liveSummary: {
                fullTotalGmv: number; // 成交金额
                orderUsers: number; // 下单用户数
                uvValue: number; // UV 价值
                fullSkuNumbers: number; // 销售件数
                totalOnlineUsers: number; // 在线人数
                watchTotalCount: number; // 观看人次
                orderRate: number; // 下单转化
                liveFirstOrderUsers: number; // 首单用户数
                avgOnlineTime: number; // 平均在线时长
                avgOnlineTimeStr: string;
                increFans: number; // 新增关注
                maxOnlineCount: number; // 最高在线
                shareUsers: number; // 分享人数
                shareCount: number; // 分享次数
                liveStartTime: string; // 直播间开播时间
                totalLiveTimeStr: string; // 直播时长
                reserveUsers: number; // 预约人数
                updateTime: string;
            };
            playUrl: {
                flv: string; // FLV地址
                hls: string; // HLS地址
                rtmp: string; // RTMP地址
                webRtc: string; // WebRTC地址
            };
            pushUrl: {
                obsDomain: string; // OBS服务器
                obsPushCode: string; // OBS推流码
                rtmp: string; // RTMP地址
                webRtc: string; // WebRTC地址
            };
            userInfo: {
                level: number;
                levelIcon: string;
                role: number;
                avatar: string;
            };
            /** 是否支持一键开售 0否 1是 */
            isSupportPublishGoods: number;
        };

        type LiveFullDecrementConditionModel = {
            /**
             * 分摊类型(1:按金额 2:按比例)
             */
            apportionmentType?: number;
            /**
             * 优惠金额
             */
            discount?: number;
            /**
             * 服务公司分摊金额
             */
            marketingCost?: number;
            /**
             * 服务公司分摊比例
             */
            marketingRatio?: number;
            /**
             * 最多优惠金额
             */
            maxDiscount?: number;
            /**
             * 平台分摊金额
             */
            platformCost?: number;
            /**
             * 平台分摊比例
             */
            platformRatio?: number;
            /**
             * 店铺分摊金额
             */
            shopCost?: number;
            /**
             * 店铺分摊比例
             */
            shopRatio?: number;
            /**
             * 供应商分摊金额
             */
            supplierCost?: number;
            /**
             * 供应商分摊比例
             */
            supplierRatio?: number;
            /**
             * 门槛金额
             */
            threshold?: number;
        };

        type LiveFullDecrementRuleModel = {
            /**
             * 优惠阶梯
             */
            conditionList?: LiveFullDecrementConditionModel[];
            /**
             * 类型(1:满额减 2:满件减)
             */
            fullSubtractType?: number;
            /**
             * 优惠类型(1:阶梯优惠 2:循环优惠)
             */
            preferenceType?: number;
        };

        type LiveExclusiveActiveBySpuDTO = {
            ext?: LiveFullDecrementRuleModel;
            /**
             * 活动id
             */
            id?: number;
            /**
             * 商品id
             */
            spuId?: number;
            /**
             * spu限购数量 0-不限购
             */
            spuLimitCount?: number;
            /**
             * 活动类型
             */
            type?: number;
        };

        export type FindLiveGoodsByPage = {
            dataList: {
                activeInfos?: LiveExclusiveActiveBySpuDTO[];
                /**
                 * 券信息
                 */
                activeCouponList?: GetActiveCouponResDTO[];
                activeData: ActiveData;
                brandName: string;
                currentSales: number;
                spuExclusivePrice: number;
                goodsSkuList: {
                    retailPrice: number;
                    skuCode: string;
                    skuDesc: string;
                    skuPrice: number;
                    skuStockNum: number;
                    /**
                     * skuId
                     */
                    skuId?: number;
                    /**
                     * 专享零售价
                     */
                    exclusiveRetailPrice?: number;
                    /**
                     * 是否上架 true - 存在 false - 不存在
                     */
                    listedFlag?: boolean;
                    purchaseLimit?: number;
                    /**
                     * 单用户限购数量，true - 限购 false - 不限购
                     */
                    purchaseLimitFlag?: boolean;
                    // 100-已失效，110-待生效，200-已生效
                    skuExclusivePriceStatus?: number;
                    rewardPrice?: number;
                }[];
                goodsType: number;
                id: number;
                liveId: number;
                mainUrl: string;
                sayPinned: number;
                isEpigraphTextOpening: number;
                serialNo: number;
                sort: number;
                spuCode: string;
                spuId: number;
                spuName: string;
                stockNum: number;
                twoCategoryName: string;
                /**
                 * isTop=1置顶，0非置顶
                 */
                isTop: number;
                /**
                 * 自定义类目
                 */
                customerCategoryId?: number;
                customSpuName?: string;
                /**
                 * 自定义类目
                 */
                customerCategoryName: string;
                /**
                 * 三级类目
                 */
                threeFrontCategoryName: string;
                /**
                 * 卖点
                 */
                sellingPoint: string;
                /**
                 * 上下架状态 1 待开售 2 开售中
                 */
                goodsUpStatus: number;
                rewardPrice?: number;
                isShopExclusive?: number;
            }[];
            total: number;
        };

        /**
         * ActiveData
         */
        export interface ActiveData {
            /**
             * 活动id
             */
            activeId?: number;
            /**
             * 活动名称
             */
            activeName?: string;
            /**
             * 活动库存
             */
            activeNum?: number;
            /**
             * 最小活动价
             */
            activePrice?: number;
            /**
             * 活动说明
             */
            activeRemark?: string;
            /**
             * 状态 1.待提交 2.待发布 3.未开始 4.进行中 5.已结束 6.已取消
             */
            activeStatus?: number;
            /**
             * 活动类型 10.群友购 11.秒杀 13.限量预售 14限量秒杀
             */
            activeType: number;
            boolMaxDiffPrice?: boolean;
            /**
             * 活动佣金
             */
            commission?: number;
            /**
             * 营销文案
             */
            copyWriting?: string;
            /**
             * 活动已优惠价格
             */
            diffPrice?: number;
            /**
             * 结束剩余时间
             */
            endRemainingTime?: number;
            /**
             * 结束时间
             */
            endTime: string;
            fullDecrementConditionModelVOS?: FullDecrementRuleModelVO1;
            /**
             * 满减活动规则
             */
            fullDecrementDetails: string[];
            ladderGroupBuyingMinPriceRuleVO?: LadderGroupBuyingMinPriceRuleVO;
            /**
             * 最大活动价
             */
            maxActivePrice?: number;
            /**
             * 活动最大优惠价格
             */
            maxDiffPrice?: number;
            /**
             * 剩余数量
             */
            residueNum?: number;
            /**
             * 已抢数量
             */
            seckillBuyNum?: number;
            /**
             * 服务器时间
             */
            serverTime: string;
            /**
             * 开始剩余时间
             */
            startRemainingTime?: number;
            /**
             * 开始时间
             */
            startTime: string;
            /**
             * 活动省
             */
            subPrice?: number;
            /**
             * 订阅人数
             */
            subscribeNum?: number;
            /**
             * 活动的时间段id
             */
            subTimeId?: number;
            /**
             * 活动限量总库存数
             */
            totalStock?: number;
            [property: string]: unknown;
        }
        /**
         * LadderGroupBuyingMinPriceRuleVO
         */
        export interface LadderGroupBuyingMinPriceRuleVO {
            /**
             * 最低活动价
             */
            activePrice?: number;
            boolMaxDiffPrice?: boolean;
            /**
             * 已优惠价格
             */
            diffPrice?: number;
            /**
             * 最大优惠价格
             */
            maxDiffPrice?: number;
            /**
             * 最低价的 skuId
             */
            skuId?: number;
            [property: string]: unknown;
        }
        /**
         * FullDecrementRuleModelVO_1
         */
        export interface FullDecrementRuleModelVO1 {
            /**
             * 优惠阶梯
             */
            conditionList?: FullDecrementConditionModelVO1[];
            /**
             * 类型(1:满额减 2:满件减)
             */
            fullSubtractType?: number;
            /**
             * 优惠类型(1:阶梯优惠 2:循环优惠)
             */
            preferenceType?: number;
            [property: string]: unknown;
        }
        /**
         * FullDecrementConditionModelVO_1
         */
        export interface FullDecrementConditionModelVO1 {
            /**
             * 分摊类型(1:按金额 2:按比例)
             */
            apportionmentType?: number;
            /**
             * 优惠金额
             */
            discount?: number;
            /**
             * 最多优惠金额
             */
            maxDiscount?: number;
            /**
             * 平台分摊金额
             */
            platformCost?: number;
            /**
             * 平台分摊比例
             */
            platformRatio?: number;
            /**
             * 供应商分摊金额
             */
            supplierCost?: number;
            /**
             * 供应商分摊比例
             */
            supplierRatio?: number;
            /**
             * 门槛金额
             */
            threshold?: number;
            [property: string]: unknown;
        }
        /**
         * GetActiveCouponResDTO
         */
        export interface GetActiveCouponResDTO {
            /**
             * 店长专属券活动id
             */
            activeId?: number;
            /**
             * 券
             */
            coupons?: CouponModel1[];
            /**
             * 活动有效结束时间
             */
            expiryEnd: string;
            /**
             * 活动有效开始时间
             */
            expiryStart: string;
            /**
             * 是否删除 0.正常 1.删除
             */
            isDelete?: number;
            /**
             * 0是未领取完1是领取完
             */
            isReceived?: number;
            /**
             * 是否限领0代表不限制
             */
            limitNum?: number;
            /**
             * 1.不限 2.新用户 3.商品首单券--
             */
            personProperty?: number;
            /**
             * 1.不限  2.店长 3.C端用户
             */
            personScope?: number;
            /**
             * 活动预热时间
             */
            preTimeStart?: Date;
            /**
             * 服务器时间
             */
            serverTime: string;
            /**
             * 活动状态 状态 1.待提交 2.待发布 3.未开始 4.进行中 5.已结束 6.已取消
             */
            status?: number;
            [property: string]: unknown;
        }
        /**
         * CouponModel_1
         */
        export interface CouponModel1 {
            activeNum?: number;
            count?: number;
            couponTab?: string;
            couponType: number;
            discountRule?: string;
            /**
             * 优惠规则 满100-20,满100打2折,最多少20,30元无门槛券
             */
            discountRuleModels: DiscountRuleModel5[];
            activeTime: {
                expiryStart: string;
                expiryEnd: string;
                serverTime: string;
            };
            discountType?: number;
            exchangeNum?: number;
            expiryEnd: string;
            expiryObject?: string;
            expiryStart: string;
            explain?: string;
            fullSubtractType?: number;
            goodsScope?: string;
            goodsScopeModel?: GoodsScopeModel2;
            /**
             * 1.全部 2.指定商品 3.前台类目 4.品牌 5.专题
             */
            goodsScopeType?: number;
            id?: number;
            /**
             * 是否叠加使用：0否，1是
             */
            isAdd?: number;
            isFilterSpu?: number;
            isLimitNewcomer?: number;
            isReceive?: number;
            isSupportShare?: number;
            /**
             * 限制范围名称：只有一个类目和一个品牌的时候才有该值
             */
            limitName?: string;
            /**
             * 是否限领0代表不限制
             */
            limitNum?: number;
            /**
             * 优惠券领取的位置
             */
            listPosition?: number[];
            name: string;
            sort?: number;
            status?: number;
            timeModel: {
                /**
                 * 有效结束时间
                 */
                expiryEnd?: string;
                /**
                 * 有效开始时间
                 */
                expiryStart?: string;
                /**
                 * 领取次天起几日内有效
                 */
                nextDay?: number;
                /**
                 * 领取当天起几日内有效
                 */
                sameDay?: number;
                [property: string]: unknown;
            };
            transferNum?: number;
            usableNum?: number;
            visibility?: number;
            serverTime: string;
            [property: string]: unknown;
        }
        /**
         * DiscountRuleModel_5
         */
        export interface DiscountRuleModel5 {
            /**
             * 无门槛
             */
            cash?: number;
            /**
             * 折扣
             */
            discount?: number;
            /**
             * 最多减
             */
            maxSub?: number;
            /**
             * 减
             */
            sub?: number;
            /**
             * 满
             */
            to?: number;
            [property: string]: unknown;
        }

        /**
         * GoodsScopeModel_2
         */
        export interface GoodsScopeModel2 {
            /**
             * 品牌集合
             */
            brands?: number[];
            /**
             * 类目集合
             */
            categories?: number[];
            /**
             * 是否赔付券 0 否 1 是
             */
            isCompensation?: number;
            isShopExclusive?: number;
            /**
             * 商品id集合
             */
            spuIds?: number[];
            /**
             * 商品列表
             */
            spus?: GoodsScopeSpuModel2[];
            /**
             * 1.全部 2.指定商品 3.前台类目 4.品牌 5.专题
             */
            type?: number;
            [property: string]: unknown;
        }

        /**
         * GoodsScopeSpuModel_2
         */
        export interface GoodsScopeSpuModel2 {
            /**
             * sku列表
             */
            skus?: GoodsScopeSkuModel2[];
            /**
             * 商品spuId
             */
            spuId?: number;
            [property: string]: unknown;
        }

        /**
         * GoodsScopeSkuModel_2
         */
        export interface GoodsScopeSkuModel2 {
            /**
             * sku规格名称
             */
            skuDesc?: string;
            /**
             * skuId
             */
            skuId?: number;
            [property: string]: unknown;
        }

        export interface GetLiveBlockKeywords {
            keywords: string[];
        }

        /**
         * com.meiji.live.assistant.dto.response.LiveShieldKeywordRespDTO.KeywordsItemDTO
         *
         * KeywordsItemDTO
         */
        export interface KeywordsItemDTO {
            keywords: string;
            type: number;
        }

        /**
         * com.meiji.live.assistant.dto.response.LiveShieldKeywordRespDTO.RegexKeywordsItemDTO
         *
         * RegexKeywordsItemDTO
         */
        export interface RegexKeywordsItemDTO {
            name: string;
            regex: string;
        }

        /**
         * PageResDTOLiveGroupOnlineUsersRespVO
         */
        export type PageResDTOLiveGroupOnlineUsersRespVO = {
            /**
             * 集合
             */
            dataList: LiveGroupOnlineUsersRespVO[];
            /**
             * 总条数
             */
            total: number;
            // [property: string]: unknown;
        };

        /**
         * 群组用户列表响应实体
         *
         * LiveGroupOnlineUsersRespVO
         */
        export type LiveGroupOnlineUsersRespVO = {
            /**
             * 头像
             */
            avatar?: string;
            /**
             * 用户状态（0: 正常，1: 拉黑）
             */
            blockStatus?: number;
            /**
             * 群组id
             */
            groupId?: number;
            /**
             * 用户id
             */
            id?: number;
            /**
             * 等级
             */
            level?: number;
            /**
             * 等级图标
             */
            levelIcon?: string;
            /**
             * 用户禁言状态（0: 未禁言，1: 禁言）
             */
            muteStatus?: number;
            /**
             * 昵称
             */
            nickname?: string;
            /**
             * 角色
             */
            role?: number;
            /**
             * 在线状态 0离线，1在线
             */
            onlineStatus?: number;
            [property: string]: unknown;
        };

        export type GetCommentList = {
            cloudCustomData: string;
            gmtCreate: string;
            gmtModified: string;
            groupId: number;
            id: number;
            msgContent: string;
            msgId: string;
            msgSenderId: number;
            msgSeq: number;
            /** 1-正常、2-撤回、3-删除、4-隐藏 */
            msgStatus: number;
            msgTime: string;
            msgType: string;
            msgSenderNickname: string;
            msgSenderAvatar: string;
        }[];

        /**
         * ResultVOLiveRuntimeBigStatisticsDTO
         */
        export type Response = {
            /**
             * 状态码
             */
            code?: string;
            /**
             * 返回数据
             */
            data?: LiveRuntimeBigStatisticsDTO;
            /**
             * 提示信息
             */
            msg?: string;
            /**
             * 时间戳
             */
            timestamp?: number;
            /**
             * 定位信息（不显示）
             */
            trace?: string;
            [property: string]: unknown;
        };

        /**
         * 返回数据
         *
         * LiveRuntimeBigStatisticsDTO
         */
        export type LiveRuntimeBigStatisticsDTO = {
            /**
             * 人均观看时长=累积观看时长/累积观看人数
             */
            avgViewTimes?: number;
            /**
             * 实时在线人数
             */
            currentViewsOnlineUsers?: number;
            /**
             * 全单访问商品用户数
             */
            fullAccessGoodsUsers?: number;
            /**
             * 全单加购商品用户数
             */
            fullAddcarGoodsUsers?: number;
            /**
             * 全单点击商品用户数
             */
            fullClickGoodsUsers?: number;
            /**
             * 全单会员单价
             */
            fullDerUnitPrice?: number;
            /**
             * 全单首单会员数
             */
            fullFirstOrderDealers?: number;
            /**
             * 全单首单用户数
             */
            fullFirstOrderUsers?: number;
            /**
             * 全单-商品曝光下单率
             */
            fullGoodsExposureOrderRate?: number;
            /**
             * 会员全单销售额
             */
            fullOrderDerTotalGmv?: number;
            /**
             * 全量成交会员数
             */
            fullOrderDerUsers?: number;
            /**
             * 全单会员转化率=成交会员数/累积观看会员数
             */
            fullOrderDerUsersRate?: number;
            /**
             * 全单-出单商品数
             */
            fullOrderGoods?: number;
            /**
             * 全量销售件数
             */
            fullOrderSkuNumbers?: number;
            /**
             * 全单销售额
             */
            fullOrderTotalGmv?: number;
            /**
             * 全单订单数
             */
            fullOrderTotalNumbers?: number;
            /**
             * 全量成交人数
             */
            fullOrderUsers?: number;
            /**
             * 全单用户转化率=成交人数/累积观看人数
             */
            fullOrderUsersRate?: number;
            /**
             * 全单复购会员数
             */
            fullRepayOrderDealers?: number;
            /**
             * 全单复购用户数
             */
            fullRepayOrderUsers?: number;
            /**
             * 全单用户单价
             */
            fullUnitPrice?: number;
            /**
             * 纯单uv价值
             */
            fullUvValue?: number;
            /**
             * 全单-观看商品曝光率
             */
            fullViewGoodsExposureRate?: number;
            /**
             * 全单浏览商品用户数
             */
            fullViewGoodsUsers?: number;
            fullViewOrderRate?: number;
            pureViewOrderRate?: number;
            /**
             * 直播ID
             */
            liveId?: number;
            /**
             * 直播开始时间
             */
            liveStartTime?: string;
            /**
             * 直播间类型 公开直播= 1 ，私密直播 = 2
             */
            liveType?: number;
            /**
             * 实时最高在线人数
             */
            maxViewsOnlineUsers?: number;
            /**
             * 纯单访问商品用户数
             */
            pureAccessGoodsUsers?: number;
            /**
             * 纯单加购商品用户数
             */
            pureAddcarGoodsUsers?: number;
            /**
             * 纯单点击商品用户数
             */
            pureClickGoodsUsers?: number;
            /**
             * 纯单会员单价
             */
            pureDerUnitPrice?: number;
            /**
             * 纯单首单会员数
             */
            pureFirstOrderDealers?: number;
            /**
             * 纯单首单用户数
             */
            pureFirstOrderUsers?: number;
            /**
             * 纯单-商品曝光下单率
             */
            pureGoodsExposureOrderRate?: number;
            /**
             * 会员纯量销售额
             */
            pureOrderDerTotalGmv?: number;
            /**
             * 纯量成交会员数
             */
            pureOrderDerUsers?: number;
            /**
             * 纯单会员转化率
             */
            pureOrderDerUsersRate?: number;
            /**
             * 纯单-出单商品数
             */
            pureOrderGoods?: number;
            /**
             * 纯量销售件数
             */
            pureOrderSkuNumbers?: number;
            /**
             * 纯量销售额
             */
            pureOrderTotalGmv?: number;
            /**
             * 纯单订单数
             */
            pureOrderTotalNumbers?: number;
            /**
             * 纯量成交人数
             */
            pureOrderUsers?: number;
            /**
             * 纯单用户转化率
             */
            pureOrderUsersRate?: number;
            /**
             * 纯单复购会员数
             */
            pureRepayOrderDealers?: number;
            /**
             * 纯单复购用户数
             */
            pureRepayOrderUsers?: number;
            /**
             * 纯单用户单价
             */
            pureUnitPrice?: number;
            /**
             * 全单uv价值
             */
            pureUvValue?: number;
            /**
             * 纯单-观看商品曝光率
             */
            pureViewGoodsExposureRate?: number;
            /**
             * 纯单浏览商品用户数
             */
            pureViewGoodsUsers?: number;
            /**
             * 新注册会员数
             */
            totalIncreDerUsers?: number;
            /**
             * 拉新总人数
             */
            totalIncreUsers?: number;
            /**
             * 直播时长
             */
            totalLiveTimes?: number;
            /**
             * 累积观看会员数
             */
            totalViewsDerUsers?: number;
            /**
             * 累积观看人数
             */
            totalViewsUsers?: number;

            /**
             * 累积观看人次
             */
            totalViewsUserTimes?: number;
            /**
             * 累积观看时长
             */
            totalViewTimes?: number;
            /**
             * 观看-下单率
             */
            viewOrderRate?: number;

            /**
             * 直播奖励
             */
            pureRewardAmt?: number;

            showReward?: 0 | 1;
        };
        export interface IStatisticsGoodsRes {
            liveRuntimeBigStatisticsDTO: LiveRuntimeBigStatisticsDTO;
            // liveGoodsDataVOPageResDTO: IPageResult<LiveGoodsDataVO>;
        }

        /**
         * com.meiji.big.data.api.dto.response.live.LiveTrendsDataDTO
         *
         * LiveTrendsDataDTO
         */
        export type LiveTrendsDataDTO = {
            /**
             * 在线人数
             */
            currentViewsOnlineUsers: number;
            /**
             * 趋势图时间段
             */
            dataTime: string;
            /**
             * 成交金额
             */
            fullOrderTotalGmv: number;
            /**
             * 成交用户数
             */
            fullOrderUsers: number;
            liveId: number;
            // [property: string]: unknown;
        };

        /**
         * LiveImUserNumRespDTO
         */
        export type LiveImUserNumRespDTO = {
            /**
             * 直播间ID
             */
            groupId?: number;
            /**
             * 禁言人数
             */
            muteNum?: number;
            /**
             * 在线人数
             */
            onlineNum?: number;
            /**
             * 当前直播间拉黑数量
             */
            currentBlockNum?: number;
            /**
             * 永久拉黑数量字段
             */
            blockNum?: number;
            // [property: string]: unknown;
        };
        export type GetUserMutedList = {
            imUserId: number;
            nickname: string;
            sdkAppId: number;
            userSign: string;
        }[];

        /**
         * com.meiji.big.data.api.dto.response.live.LiveUserInviteTopDataDTO
         *
         * LiveUserInviteTopDataDTO
         */
        export type LiveUserInviteTopDataDTO = {
            /**
             * 用户头像
             */
            avatar?: string;
            /**
             * 邀请人数
             */
            inviteUsers?: number;
            /**
             * 等级
             */
            level?: number;
            /**
             * 图标
             */
            levelIcon?: string;
            liveId?: number;
            /**
             * 用户昵称
             */
            nickname?: string;
            /**
             * 角色
             */
            role?: number;
            userId?: number;
            [property: string]: unknown;
        };

        /**
         * com.meiji.big.data.api.dto.response.live.LiveUserCommentTopDataDTO
         *
         * LiveUserCommentTopDataDTO
         */
        export type LiveUserCommentTopDataDTO = {
            /**
             * 用户头像
             */
            avatar?: string;
            /**
             * 评论数
             */
            commentValue?: number;
            /**
             * 关注数
             */
            followValue?: number;
            /**
             * 互动数
             */
            interactValue?: number;
            /**
             * 等级
             */
            level?: number;
            /**
             * 图标
             */
            levelIcon?: string;
            /**
             * 点赞数
             */
            likeValue?: number;
            liveId?: number;
            /**
             * 用户昵称
             */
            nickname?: string;
            /**
             * 角色
             */
            role?: number;
            /**
             * 分享数
             */
            shareValue?: number;
            userId?: number;
            [property: string]: unknown;
        };

        /**
         * com.meiji.big.data.api.dto.response.live.LiveUserTimeDataDTO
         *
         * LiveUserTimeDataDTO
         */
        export type LiveUserTimeDataDTO = {
            /**
             * 用户头像
             */
            avatar?: string;
            /**
             * 等级
             */
            level?: number;
            /**
             * 图标
             */
            levelIcon?: string;
            liveId?: number;
            /**
             * 用户昵称
             */
            nickname?: string;
            /**
             * 角色
             */
            role?: number;
            userId?: number;
            /**
             * 观看时长
             */
            watchTimes?: number;
            [property: string]: unknown;
        };

        /**
         * com.meiji.big.data.api.dto.response.live.LiveUserBuyTopDataDTO
         *
         * LiveUserBuyTopDataDTO
         */
        export type LiveUserBuyTopDataDTO = {
            /**
             * 用户头像
             */
            avatar?: string;
            /**
             * 购买次数
             */
            buyNums?: number;
            /**
             * 等级
             */
            level?: number;
            /**
             * 图标
             */
            levelIcon?: string;
            liveId?: number;
            /**
             * 直播间类型 公开直播= 1 ，私密直播 = 2
             */
            liveType?: number;
            /**
             * 用户昵称
             */
            nickname?: string;
            /**
             * 角色
             */
            role?: number;
            /**
             * 成交金额
             */
            totalGmv?: number;
            /**
             * 订单数
             */
            totalOrders?: number;
            userId?: number;
            [property: string]: unknown;
        };

        /**
         * 返回数据
         *
         * LiveUserPortraitsRespVO
         */
        export type LiveUserPortraitsRespVO = {
            /**
             * 成交用户类型数据
             */
            livePersonaUserOrderTypeDataVO?: LivePersonaUserOrderTypeDataVO;
            /**
             * 用户城市分布数据
             */
            livePersonaUserProvinceDataVOList?: LivePersonaUserProvinceDataVO[];
            /**
             * 用户类型数据
             */
            livePersonaUserTypeDataVO?: LivePersonaUserTypeDataVO;
            // [property: string]: unknown;
        };

        /**
         * 成交用户类型数据
         *
         * LivePersonaUserOrderTypeDataVO
         */
        export type LivePersonaUserOrderTypeDataVO = {
            /**
             * 所有(全单)用户
             */
            allFullUsers?: number;
            /**
             * 所有(纯单)用户
             */
            allPureUsers?: number;
            /**
             * 新会员(全单)
             */
            fullNewDealers?: number;
            /**
             * 新会员(全单)所占比例
             */
            fullNewDerUsersRatio?: number;
            /**
             * 新用户(全单)
             */
            fullNewUsers?: number;
            /**
             * 新用户(全单)所占比例
             */
            fullNewUsersRatio?: number;
            /**
             * 老会员(全单)
             */
            fullOldDealers?: number;
            /**
             * 老会员(全单)所占比例
             */
            fullOldDerUsersRatio?: number;
            /**
             * 老用户(全单)
             */
            fullOldUsers?: number;
            /**
             * 老用户(全单)所占比例
             */
            fullOldUsersRatio?: number;
            liveId?: number;
            /**
             * 直播间类型 公开直播= 1 ，私密直播 = 2
             */
            liveType?: number;
            /**
             * 新会员(纯单)
             */
            pureNewDealers?: number;
            /**
             * 新会员(纯单)所占比例
             */
            pureNewDerUsersRatio?: number;
            /**
             * 新用户(纯单)
             */
            pureNewUsers?: number;
            /**
             * 新用户(纯单)所占比例
             */
            pureNewUsersRatio?: number;
            /**
             * 老会员(纯单)
             */
            pureOldDealers?: number;
            /**
             * 老会员(纯单)所占比例
             */
            pureOldDerUsersRatio?: number;
            /**
             * 老用户(纯单)
             */
            pureOldUsers?: number;
            /**
             * 老用户(纯单)所占比例
             */
            pureOldUsersRatio?: number;
            // [property: string]: unknown;
        };

        /**
         *
         * com.meiji.proxy.live.vo.response.live.LiveUserPortraitsRespVO.LivePersonaUserProvinceDataVO
         *
         * LivePersonaUserProvinceDataVO
         */
        export type LivePersonaUserProvinceDataVO = {
            liveId?: number;
            /**
             * 省份
             */
            province?: string;
            /**
             * 用户数所占比例
             */
            userNumsRatio?: number;
            /**
             * 用户数
             */
            users?: number;
            // [property: string]: unknown;
        };

        /**
         * 用户类型数据
         *
         * LivePersonaUserTypeDataVO
         */
        export type LivePersonaUserTypeDataVO = {
            /**
             * 所有用户
             */
            allUsers?: number;
            liveId?: number;
            /**
             * 新会员
             */
            newDealers?: number;
            /**
             * 新会员所占比例
             */
            newDerUsersRatio?: number;
            /**
             * 新用户
             */
            newUsers?: number;
            /**
             * 新用户所占比例
             */
            newUsersRatio?: number;
            /**
             * 老会员
             */
            oldDealers?: number;
            /**
             * 老会员所占比例
             */
            oldDerUsersRatio?: number;
            /**
             * 老用户
             */
            oldUsers?: number;
            /**
             * 老用户所占比例
             */
            oldUsersRatio?: number;
            /**
             * 游客数量
             */
            visitors?: number;
            /**
             * 游客所占比例
             */
            visitorsRatio?: number;
            // [property: string]: unknown;
        };

        /**
         * LiveEpigraphTextRespDTO
         */
        export type LiveEpigraphTextRespDTO = {
            /**
             * 文本
             */
            epigraphText?: string;
            /**
             * 直播间ID
             */
            liveId?: number;
            /**
             * 商品ID
             */
            spuId?: number;
            /**
             * 类型：1=商品话术，2=案例分析
             */
            type: number;
            // [property: string]: unknown;
        };

        /**
         * LiveEpigraphTextRespDTO
         */
        export type LiveGoodsPromotionWordsAiStreamLiveEpigraphTextRespDTO = {
            /**
             * 返回内容，最后一次会返回完整的内容用于修正
             */
            answer: string;
            /**
             * 未完成：null, 已完成：all-down
             */
            action: string;
            // [property: string]: unknown;
        };

        /**
         * LiveAnalysisRespDTO
         */
        export type LiveAnalysisRespDTO = {
            /**
             * 总结分析
             */
            analysis?: LiveAnalysisRespDTOAnalysis[];
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 改进建议
             */
            suggestions?: LiveAnalysisRespDTOSuggestion[];
            /**
             * 词云列表
             */
            words?: string[];
            [property: string]: unknown;
        };

        /**
         * getLiveReservationPushMsgRespDTO
         */
        export type getLiveReservationPushMsgRespDTO = {
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 直播间名称
             */
            liveName?: string;
            /**
             * 开播时间
             */
            preStartTime?: string;
            /**
             * 是否推送预约 0.关闭 1.开启
             */
            pushSwitch?: number;
        };

        /**
         * com.meiji.live.assistant.dto.response.LiveAnalysisRespDTO.Analysis
         *
         * Analysis
         */
        export type LiveAnalysisRespDTOAnalysis = {
            /**
             * 相关评论
             */
            commentList?: string[];
            /**
             * 内容
             */
            content?: string;
            /**
             * 等级 0=无，1=低，2=中，3=高
             */
            level: number;
            /**
             * 原因
             */
            reason: string;
            /**
             * 解决方案
             */
            solution: string;
            /**
             * 标题
             */
            title?: string;
            // [property: string]: unknown;
        };

        /**
         * com.meiji.live.assistant.dto.response.LiveAnalysisRespDTO.Suggestion
         *
         * Suggestion
         */
        export type LiveAnalysisRespDTOSuggestion = {
            /**
             * 内容
             */
            content?: string;
            /**
             * 标签
             */
            tag?: string;
            [property: string]: unknown;
        };
        export type GetLiveGoodsCount = {
            count: number;
            unUpCount: number;
        };

        export type AudienceRecord = {
            /**
             * 用户头像
             */
            avatar: string;
            /**
             * 邀请时间
             */
            createdTime: string;
            /**
             * 是否粉丝（0: 否, 1: 是）
             */
            isFollow: boolean;
            /**
             * 邀请状态（0: 未邀请, 1: 已邀请）
             */
            isInvites: number;
            /**
             * 最近被忽略或拒绝时间（前端需要根据这个字段判断邀请按钮状态，5分钟后按钮转换为‘邀请’状态）
             */
            lastRejectTime: string;
            /**
             * 用户等级
             */
            level: number;
            /**
             * 等级图标
             */
            levelIcon: string;
            /**
             * 直播间ID
             */
            liveId: number;
            /**
             * 连麦记录类型（1: 刚邀请过, 2: 刚拒绝过, 3: 今天连线过）
             */
            liveMicHistoryType: number;
            /**
             * 用户昵称
             */
            nickname: string;
            /**
             * 用户角色（会员、非会员等）
             */
            role: number;
            /**
             * 用户ID
             */
            userId: number;
            /**
             * 观看时长（秒）
             */
            viewTime: number;
        };

        export type GetAudienceList = {
            total: number;
            dataList: AudienceRecord[];
        };

        export type GetInteractiveConfig = {
            /**
             * 是否允许开启摄像头（0: 不允许, 1: 允许）
             */
            allowCamera: number;
            /**
             * 用户连麦是否需要申请（0: 无需申请, 1: 需要申请）
             */
            auditStrategy: number;
            /**
             * 加入时是否自动静音（0: 非静音, 1: 静音）
             */
            autoMute: number;
            /**
             * 默认连麦类型（1: 音频, 2: 视频）
             */
            defaultMedia: number;
            /**
             * 直播间连麦总开关（0: 关闭, 1: 开启）
             */
            enableMic: number;
            /**
             * 主键ID
             */
            id: number;
            /**
             * 直播间ID
             */
            liveId: number;
            /**
             * 直播间最大同时连麦人数
             */
            maxUsers: number;
        };

        export type ApplyCoRecord = {
            /**
             * 申请时间
             */
            applyTime: string;
            /**
             * 用户头像
             */
            avatar: string;
            /**
             * id
             */
            id: number;
            /**
             * 是否粉丝，0=否，1=是
             */
            isFollow: boolean;
            /**
             * 是否接受连麦，0=否，1=是(前端模拟的)
             */
            isAccept?: number;
            /**
             * 用户等级
             */
            level: number;
            /**
             * 用户等级图片
             */
            levelIcon: string;
            /**
             * 直播间ID
             */
            liveId: number;
            /**
             * 连线状态，1=刚邀请过，2=刚拒绝过，3=今日连线过
             */
            liveMicHistoryType: number;
            /**
             * 用户昵称
             */
            nickname: string;
            /**
             * 用户角色
             */
            role: number;
            /**
             * 申请用户ID
             */
            userId: number;
            /**
             * 观看时长（秒）
             */
            viewTime: number;
        };

        export type GetApplyCoList = {
            total: number;
            dataList: ApplyCoRecord[];
        };

        export type CoStreamRecord = {
            /**
             * 头像
             */
            avatar: string;
            /**
             * 摄像头状态
             * 摄像头状态（0: 关闭, 1: 开启, 2: 被主播强制关闭）
             */
            cameraStatus: number;
            /**
             * 是否粉丝
             */
            isFollow: boolean;
            /**
             * 是否静音
             * 是否静音（0: 非静音, 1: 静音）
             */
            isMuted: number;
            /**
             * 用户等级
             */
            level: number;
            /**
             * 用户等级图片
             */
            levelIcon: string;
            /**
             * 用户昵称
             */
            nickname: string;
            /**
             * 用户角色
             */
            role: number;
            /**
             * Trtc拉流地址
             */
            trtcPlayUrl: string;
            /**
             * 用户ID
             */
            userId: number;
        };

        export type GetCoStreamList = {
            /** 连麦申请排队人数 */
            queueSize: number;
            seatsList: CoStreamRecord[];
        };
        export type UpdateActiveRedPacketRain = {};

        export type UpdateActiveRedPacketRainOpenStatus = {};

        export type ExportActiveRedPacketRainUseLotteryPrize = {};

        export type GetCourierCompanyList = {
            firstLetter: string;
            list: {
                courierCompanyName: string;
                ewbAuthFieldJson: string;
                localCode: string;
            }[];
        }[];

        export type LiveManualCommentRecord = {
            id: string;
            content: string;
            /** 是否全局 1=全局 0=非全局 */
            isGlobal: number;
        };

        export type GetLiveManualComment = {
            liveId: number;
            /** 直播间人工预设评论 */
            preCommentItems: LiveManualCommentRecord[];
            /** 全局预设评论 */
            globalPreComment: LiveManualCommentRecord[];
        };

        /**
         * LiveGoodsCategoryVO
         */
        export type LiveGoodsCategoryVO = {
            /**
             * 类别名称
             */
            categoryName: string;
            /**
             * id
             */
            id?: number;
            /**
             * 排序
             */
            sort?: number;
            // [property: string]: unknown;
        };

        export type GetRecentlyPrePushPreLive = {
            /**
             * 直播id
             */
            liveId?: number;
            /**
             * 直播预推流方式 1-手动推流 2-自动推流, 可能返回为空
             */
            prePushType?: number;
            /**
             * 直播预开始时间
             */
            preStartTime?: string;
            /**
             * 直播预开始时间（秒）
             */
            preStartTimeSec?: number;
        };

        export type GetLiveConfig = {
            /**
             * 是否推流预览 1-是 0-否
             */
            isPrePush?: number;
            /**
             * 直播间id
             */
            liveId?: number;
            /**
             * 直播预推流方式 1-手动推流 2-自动推流
             */
            prePushType?: number | null;
        };

        export type GetPlayUrl = {
            /**
             * 自适应流播放地址
             */
            webRtcAuto?: string;
        };

        /** 获取拉黑用户列表 */
        export type GetBlockUserList = {
            /**
             * 集合
             */
            dataList: {
                avatar: string;
                /** 昵称 */
                nickname: string;
                /** 用户id */
                id: number;
                levelIcon: string;
            }[];
            /*
             * 总条数
             */
            total: number;
        };
        export interface LiveOrderVO {
            /**
             * 买家头像
             */
            avatar?: string;
            /**
             * 下单时间
             */
            gmtCreate?: string;
            /**
             * 商品图片
             */
            goodsImgUrl?: string;
            /**
             * 商品名称
             */
            goodsName?: string;
            /**
             * 订单总价
             */
            goodsTotalAmount?: number;
            /**
             * 订单编号
             */
            orderCode: string;
            /**
             * 订单状态
             */
            orderStatus?: number;
            /**
             * 买家昵称
             */
            receiverNick?: string;
            /**
             * 商品规格
             */
            skuDesc?: string;
            /**
             * 商品数量
             */
            skuNum?: number;
            /**
             * 商品价格
             */
            skuPrice?: number;
            /**
             * 商品id
             */
            spuId?: number;
        }
    }

    /** 聊天消息 */
    export type ChatMessage = Pick<
        Message,
        'cloudCustomData' | 'conversationType' | 'from' | 'ID' | 'nick' | 'payload' | 'to' | 'type'
    > & {
        /** 消息状态 */
        msgStatus?: IMMessageStatus;
        /** 云端自定义数据 */
        cloudCustomData?: Record<string, unknown>;
    };

    export type AtUser = {
        /** 用户id */
        id: string;
        /** 用户昵称 */
        nickname: string;
        /** 开始位置 */
        startIndex: number;
    };
}
