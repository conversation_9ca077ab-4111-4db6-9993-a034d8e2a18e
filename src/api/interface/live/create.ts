/**
 * @Owners xj
 * @Title 直播中控台
 */

export namespace CreateLive {
    export namespace Params {
        export type AddLive = {
            /** 直播封面 */
            coverUrl: string;
            /** 直播商品 */
            goodsList: {
                /** 排序 */
                sort: number;
                spuId: number;
            }[];
            /** 是否开启录播 0.否 1.是 */
            isRecord: number;
            /** 直播间类型 1.即时开播 2.直播预告 */
            liveCategory: number;
            /** 直播间方式 1.横屏直播 2.竖屏直播 */
            liveMode: number;
            /** 直播间名称 */
            liveName: string;
            /** 直播主题 */
            liveTheme: string;
            /** 直播间类别 1.公开直播 2.私密直播 */
            liveType: number;
            /** 直播时间 */
            preStartTime: string;
            /** 推流类型 1.外设推流 2.手机推流 */
            pushType: number;
        };
    }
    export namespace Response {
        export type UserPermission = {
            liveTools: string[];
            permissions: number[];
            isGoodsManage: number;
            /** 直播奖励开关：1-开启，0-关闭 */

            rewardEnabled: number;
        };
    }
}
