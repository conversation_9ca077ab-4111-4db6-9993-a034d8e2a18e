/**
 * @Owners xj
 * @Title 首页
 */

export namespace Home {
    export namespace Params {
        export interface PastLivePage<T = {}> {
            /** 条件 */
            condition?: T;
            /** 页数 */
            page: number;
            /** 每页条数 */
            rows: number;
        }
    }
    export namespace Response {
        export type UserLivingInfo = {
            id: number;
            coverUrl: string; // 直播封面
            liveName: string; // 直播间名称
            liveTheme: string; // 直播主题
            roomCode: number; // 房间号
            pushStatus: number; // 推流状态
            liveSummary: {
                fullTotalGmv: number; // 成交金额
                orderUsers: number; // 下单用户数
                uvValue: number; // UV 价值
                fullSkuNumbers: number; // 销售件数
                totalOnlineUsers: number; // 在线人数
                watchTotalCount: number; // 观看人次
                orderRate: number; // 下单转化
                liveFirstOrderUsers: number; // 首单用户数
                avgOnlineTime: number; // 平均在线时长
                avgOnlineTimeStr: string;
                increFans: number; // 新增关注
                maxOnlineCount: number; // 最高在线
                shareUsers: number; // 分享人数
                shareCount: number; // 分享次数

                liveStartTime: string; // 直播间开播时间
                totalLiveTimeStr: string; // 直播时长
            };
        };

        export type LiveOverview = {
            /**
             * 直播间id
             */
            id: number;
            /**
             * 直播间名称
             */
            liveName: string;
            /**
             * 数据分析
             */
            liveRuntimeBigStatistics: {
                /**
                 * 人均观看时长=累积观看时长/累积观看人数
                 */
                avgViewTimes: number;
                /**
                 * 实时在线人数
                 */
                currentViewsOnlineUsers: number;
                /**
                 * 全单会员单价
                 */
                fullDerUnitPrice: number;
                /**
                 * 会员全单销售额
                 */
                fullOrderDerTotalGmv: number;
                /**
                 * 全量成交会员数
                 */
                fullOrderDerUsers: number;
                /**
                 * 全单会员转化率=成交会员数/累积观看会员数
                 */
                fullOrderDerUsersRate: number;
                /**
                 * 全量销售件数
                 */
                fullOrderSkuNumbers: number;
                /**
                 * 全单销售额
                 */
                fullOrderTotalGmv: number;
                /**
                 * 全量成交人数
                 */
                fullOrderUsers: number;
                /**
                 * 全单用户转化率=成交人数/累积观看人数
                 */
                fullOrderUsersRate: number;
                /**
                 * 全单用户单价
                 */
                fullUnitPrice: number;
                /**
                 * 直播ID
                 */
                liveId: number;
                /**
                 * 直播开始时间
                 */
                liveStartTime: string;
                /**
                 * 直播间类型 公开直播= 1 ，私密直播 = 2
                 */
                liveType: number;
                /**
                 * 纯单会员单价
                 */
                pureDerUnitPrice: number;
                /**
                 * 会员纯量销售额
                 */
                pureOrderDerTotalGmv: number;
                /**
                 * 纯量成交会员数
                 */
                pureOrderDerUsers: number;
                /**
                 * 纯单会员转化率
                 */
                pureOrderDerUsersRate: number;
                /**
                 * 纯量销售件数
                 */
                pureOrderSkuNumbers: number;
                /**
                 * 纯量销售额
                 */
                pureOrderTotalGmv: number;
                /**
                 * 纯量成交人数
                 */
                pureOrderUsers: number;
                /**
                 * 纯单用户转化率
                 */
                pureOrderUsersRate: number;
                /**
                 * 纯单用户单价
                 */
                pureUnitPrice: number;
                /**
                 * 新注册会员数
                 */
                totalIncreDerUsers: number;
                /**
                 * 拉新总人数
                 */
                totalIncreUsers: number;
                /**
                 * 直播时长
                 */
                totalLiveTimes: number;
                /**
                 * 累积观看会员数
                 */
                totalViewsDerUsers: number;
                /**
                 * 累积观看人数
                 */
                totalViewsUsers: number;
                /**
                 * 累积观看时长
                 */
                totalViewTimes: number;
            };
            /**
             * 推流状态 1.未推流 2.推流中 3.暂停推流 4.禁止推流 5.停止推流 6.未开始 7.未开播 8.已过期
             */
            pushStatus: number;
        };

        export type PreLiveList = {
            liveId: number;
            coverUrl: string; // 直播分享封面
            liveCoverUrl: string; // 直播间封面
            liveName: string; // 直播间名称
            liveTheme: string; // 直播主题
            goodNumber: string; // 橱窗商品
            liveStatus: number; // 直播状态
            preStartTime: string; // 开播时间
            roomCode: number; // 房间号
            subscribeNumber: number; // 预约人数
            pushStatus: number; // 推流状态 1.未推流 2.推流中 3.暂停推流 4.禁止推流 5.停止推流 6.未开始 7.未开播 8.已过期
        }[];

        export type PastLivePage = {
            dataList: {
                liveId: number;
                coverUrl: string; // 直播封面
                liveName: string; // 直播间名称
                liveTheme: string; // 直播主题
                liveType: number; // 直播类型
                liveStartTime: string; // 开播时间
                fullSkuNumbers: number; // 全平台销售件数
                fullTotalGmv: number; // 全平台成交金额
                liveEndTime: string; // 直播间结束时间
                liveSkuNumbers: number; // 直播间销售件数
                liveTotalGmv: number; // 直播间成交金额
                orderRate: number; // 下单转化率
                roomCode: number; // 房间号
                totalLiveTimeStr: string; // 直播时长
                totalOnlineCount: number; // 观看人次
                totalOnlineUsers: number; // 观看人数
                uvValue: number; // UV价值
                /**
                 * 直播录制url
                 */
                videoUrl: string;
                /**
                 * 直播录制下载url
                 */
                videoDownloadUrl: string;
            }[];
            total: number;
        };
    }
}
