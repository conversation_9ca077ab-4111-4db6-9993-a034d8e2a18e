/**
 * @Owners wyy
 * @Title 福袋
 */

export namespace LuckyBag {
    /**
     * EditLuckyBagReqDTO
     */
    export interface EditLuckyBagReqDTO {
        /**
         * 福袋id
         */
        bagId?: number;
        /**
         * 优惠券列表
         */
        couponList?: LuckyBagCouponInfoDTO[];
        distributeRule?: LuckyBagDistributeRuleDTO;
        /**
         * 门槛 0 不限 1 未关注主播 2 全部关注主播 3 分享直播间可领 4 邀请进入直播间
         */
        doorsill?: number;
        /**
         * 图标
         */
        icon?: string;
        /**
         * 图标类型 0 自定义
         */
        iconType?: number;
        /**
         * 邀请类型(1:全部用户都为有效邀请,2:有效邀请仅为本场直播间新用户)
         */
        inviteType?: number;
        /**
         * 限领次数 0 不限制
         */
        limitNum?: number;
        /**
         * 活动名称/福袋名称
         */
        name?: string;
        /**
         * 目标数量  邀请XXX人
         */
        target?: number;
        /**
         * 福袋类型 1 优惠券
         */
        type?: number;
    }

    /**
     * LuckyCouponRespVO
     */
    export interface LuckyCouponRespVO {
        spuInfoList: never[];
        /**
         * 活动库存
         */
        activeNum?: number;
        /**
         * 申请数量
         */
        applyNum?: number;
        /**
         * 券基础id
         */
        couponId?: number;
        couponType?: number;
        createBy?: string;
        discountRule?: string;
        /**
         * 优惠规则 满100-20,满100打2折,最多少20,30元无门槛券
         */
        discountRuleModels: DiscountRuleModel5[];
        discountType?: number;
        expiryEnd?: Date;
        expiryObject?: string;
        expiryStart?: Date;
        explain?: string;
        fullSubtractType?: number;
        gmtCreate?: Date;
        gmtModified?: Date;
        /**
         * 只有当是指定商品的时候才会返回
         */
        goods?: SpuResponseVO[];
        goodsScope?: string;
        goodsScopeModel: GoodsScopeModel;
        goodsScopeType?: number;
        id?: number;
        isExchangeFraction?: number;
        name?: string;
        occupyNum?: number;
        shopId?: number;
        shopPhone?: string;
        /**
         * 排序
         */
        sort?: number;
        status?: number;
        timeModel: TimeModel3;
        total?: number;
        updateBy?: string;
        usableNum?: number;
    }

    /**
     * DiscountRuleModel_5
     */
    export interface DiscountRuleModel5 {
        /**
         * 无门槛
         */
        cash?: number;
        /**
         * 折扣
         */
        discount?: number;
        /**
         * 最多减
         */
        maxSub?: number;
        /**
         * 减
         */
        sub?: number;
        /**
         * 满
         */
        to?: number;
    }

    /**
     * SpuResponseVO
     */
    export interface SpuResponseVO {
        /**
         * 展示活动样式
         */
        activityShowStyle?: number;
        /**
         * 展示活动文案
         */
        activityShowText?: string;
        /**
         * 后台类目id
         */
        backgroundCategoryId?: number;
        /**
         * 后台类目名字
         */
        backgroundCategoryName?: string;
        /**
         * 基础销量
         */
        baseSales?: number;
        /**
         * 品牌id
         */
        brandId?: number;
        /**
         * 品牌名字
         */
        brandName?: string;
        /**
         * 晒单文案
         */
        buyerShowLabelList?: string[];
        /**
         * 轮播图片
         */
        carouselPic?: string;
        /**
         * 商品品名
         */
        categoryName?: string;
        /**
         * 渠道二级编码（默认：yfdmx-2nd-default）
         */
        channel2ndCode?: string;
        /**
         * 栏目商品id
         */
        columnGoodsId?: number;
        /**
         * 栏目商品排序字段
         */
        columnSort?: number;
        /**
         * cps类目id
         */
        cpsCategoryId?: string;
        /**
         * cps类目名称
         */
        cpsCategoryName?: string;
        /**
         * 发货信息
         */
        deliveryInfoDTO?: DeliveryInfoDTO[];
        /**
         * 派送方式（默认 0 快递发货）
         */
        deliveryType?: number;
        /**
         * 商品详情
         */
        detail?: string;
        /**
         * 下架原因
         */
        downReason?: string;
        /**
         * 下架备注
         */
        downRemark?: string;
        /**
         * 下架时间
         */
        downTime?: Date;
        /**
         * 下架类型
         */
        downType?: number;
        /**
         * 外包装图片列表
         */
        extenalPackingPics?: string[];
        /**
         * 常见问题
         */
        freAskedQuestions?: string;
        /**
         * 运费模板id
         */
        freightTemplateId?: number;
        /**
         * 前台类目id
         */
        frontDeskCategoryId?: number;
        /**
         * 前台类目名字
         */
        frontDeskCategoryName?: string;
        /**
         * 创建时间
         */
        gmtCreate?: Date;
        /**
         * 更新时间
         */
        gmtModified?: Date;
        /**
         * 商品卡片图
         */
        goodsCardPic?: string;
        goodsCommentCouponDetailDTO?: GoodsCommentCouponDetailDTO;
        /**
         * 最迟发货时间
         */
        goodsDeliverTime?: number;
        goodsMarkDTO?: GoodsMarkDTO;
        /**
         * 退改政策规则
         */
        goodsRefundPolicyRates?: SpuRefundRate[];
        goodsStyleDTO?: GoodsStyleDTO;
        /**
         * 商品质检报告图列表
         */
        goodsTestingImgs?: PicResponseDTO[];
        /**
         * 商品类型 1-普通商品 2-跨境商品
         */
        goodsType?: number;
        /**
         * 商品上下架状态 0 未上架 1 待开售 2 开售中
         */
        goodsUpStatus?: number;
        goodsUpStyleDTO?: GoodsStyleDTO;
        /**
         * 商品视频
         */
        goodsVideo?: string;
        /**
         * 商品版本：1 商城版本 2 素食版本
         */
        goodVersion?: number;
        /**
         * 保险开关 1.开 2.关
         */
        insurance?: number;
        /**
         * 积分抵扣比例
         */
        integralDeductRate?: number;
        /**
         * 积分返还比例
         */
        integralReturnRate?: number;
        /**
         * 是否买贵必陪 0.否 1.是
         */
        isCompensation?: number;
        /**
         * 是否支持售后换货（默认 0 否，1是）
         */
        isExchangeReturn?: number;
        /**
         * 是否全额退款 0 否 1 是
         */
        isFullRefund?: number;
        /**
         * 积分补差：0禁用，1启用
         */
        isIntegralSubsidy?: number;
        /**
         * 是否包邮（ 默认 0 否 ，1 是 ）
         */
        isParcel?: number;
        /**
         * 商品有效时间 1 无 2有
         */
        isPublish?: number;
        /**
         * 是否限购（默认 0 否 1是）
         */
        isPurchaseLimits?: number;
        /**
         * 是否二次确认 0 否 1 是
         */
        isSecondConfirmation?: number;
        /**
         * 是否已选 1是 0否
         */
        isSelected?: number;
        /**
         * 是否支持七天无理由退货（默认 0 否，1是）
         */
        isSevenDayReturn?: number;
        isShopExclusive?: number;
        /**
         * 供应商介绍是否展示
         */
        isShowSupSuggest?: number;
        /**
         * 0.正常商品 1.测试商品
         */
        isTestGoods?: number;
        /**
         * 是否上新提报 1 是 0 否
         */
        isUpNew?: number;
        /**
         * 标签id集合（一个商品可能对应多个标签，多个的情况下，按id逗号隔开）
         */
        labelIds?: string;
        /**
         * 评级
         */
        level?: string;
        /**
         * 限购周期 1.每天 2.每周 3.每月
         */
        limitCycle?: number;
        /**
         * 限购数量
         */
        limitNum?: number;
        /**
         * 配送类型 1：普通快递 2：顺丰冷链
         */
        logisticType?: number;
        /**
         * 是否多属性
         */
        manyProperty?: boolean;
        /**
         * 新店长商品排序
         */
        newShopSort?: number;
        /**
         * 1级类目id（前台）
         */
        oneCategory?: number;
        /**
         * 1级类目名字（前台）
         */
        oneCategoryName?: string;
        /**
         * 产地
         */
        originAddress?: string;
        /**
         * 履约方式 1-物流配送 2-线下服务
         */
        performance?: number;
        /**
         * 图片列表
         */
        pics?: PicResponseVO[];
        /**
         * 图片素材
         */
        picSourceList?: SourceMaterialResVO[];
        /**
         * 产品介绍详情
         */
        productDetail?: string;
        /**
         * 下架时间
         */
        publishDown?: Date;
        /**
         * 发布时间
         */
        publishTime?: Date;
        /**
         * 上架时间
         */
        publishUp?: Date;
        /**
         * 平台销量
         */
        realSales?: number;
        /**
         * 退改政策说明
         */
        refundPolicyContent?: string;
        /**
         * 备注
         */
        remark?: string;
        /**
         * 搜索词集合
         */
        searchWordsLabelList?: string[];
        /**
         * 卖点
         */
        sellingPoint?: string;
        /**
         * 店主：1-打分评价；2-打分评价+图文评价
         */
        shopCommentMethod?: number;
        shopExclusiveRule?: number;
        /**
         * 短标题
         */
        shortTitle?: string;
        /**
         * sku列表
         */
        skus?: SkuResponseVO[];
        /**
         * 商品来源 1 中台 2 OMS
         */
        source?: number;
        /**
         * 1.素材上架 2.素材下架
         */
        sourceMaterialStatus?: number;
        /**
         * spu编码
         */
        spuCode?: string;
        /**
         * spu-id
         */
        spuId?: number;
        /**
         * 商品名字
         */
        spuName?: string;
        /**
         * 群友内购排序
         */
        spuSort?: number;
        /**
         * 商品状态 0.待提交，1.待审核，2.审核不通过，3.待发布，4.已发布
         */
        status?: number;
        /**
         * 采购责任人
         */
        supervisorId?: number;
        /**
         * 采购责任人
         */
        supervisorName?: string;
        /**
         * 供应商code
         */
        supplierCode?: string;
        /**
         * 供应商id
         */
        supplierId?: number;
        /**
         * 供应商介绍
         */
        supplierIntroduce?: string;
        /**
         * 供应商级别 1:品牌商 2:经销商 3:总代理 4:生产商 5:其他
         */
        supplierLevel?: number;
        /**
         * 供应商名字
         */
        supplierName?: string;
        /**
         * 供应商退货地址id
         */
        supplierReturnAddressId?: number;
        /**
         * 文案素材
         */
        textSourceList?: SourceMaterialResVO[];
        /**
         * 总销量
         */
        totalSales?: number;
        /**
         * 出行时间
         */
        travelTime?: Date;
        /**
         * 2级类目id（前台）
         */
        twoCategory?: number;
        /**
         * 2级类目名字（前台）
         */
        twoCategoryName?: string;
        /**
         * 普通用户：1-打分评价；2-打分评价+图文评价
         */
        userCommentMethod?: number;
        /**
         * 视频素材
         */
        videoSourceList?: SourceMaterialResVO[];
        /**
         * 工单状态 1 审核中 2 审核通过 0 不通过
         */
        workStatus?: number;
        /**
         * 云仓标识 1 顺丰云仓 0 普通
         */
        yunStorageFlag?: number;
    }

    /**
     * DeliveryInfoDTO
     */
    export interface DeliveryInfoDTO {
        /**
         * 内容 周六不发货=7，周日不发货=1，积分赔付=500
         */
        content?: string;
        /**
         * 自定不发货时间： 结束时间
         */
        endTime?: Date;
        /**
         * 标识： 1 周末不发货 2 自定不发货时间 3 积分赔付
         */
        flag?: number;
        /**
         * spuId
         */
        spuId?: number;
        /**
         * 自定不发货时间： 开始时间
         */
        startTime?: Date;
    }

    /**
     * GoodsCommentCouponDetailDTO
     */
    export interface GoodsCommentCouponDetailDTO {
        /**
         * 活动id
         */
        activeId?: number;
        coupon?: CommentCouponDTO;
        /**
         * 是否开启
         */
        enabled?: boolean;
        /**
         * 前 N 份
         */
        topN?: number;
    }

    /**
     * CommentCouponDTO
     */
    export interface CommentCouponDTO {
        /**
         * 券id
         */
        couponId?: number;
        /**
         * 优惠券名称
         */
        couponName?: string;
        /**
         * 优惠规则 满100-20,满100打2折,最多少20,30元无门槛券
         */
        discountRuleModels: GoodsCouponDiscountRuleModelDTO[];
        /**
         * 优惠类型 1.满减券 2.折扣券 3.无门槛
         */
        discountType?: number;
        /**
         * 满减类型 1 满额减  2 满件减
         */
        fullSubtractType?: number;
        timeRuleModel: TimeModel1;
    }

    /**
     * GoodsCouponDiscountRuleModelDTO
     */
    export interface GoodsCouponDiscountRuleModelDTO {
        /**
         * 无门槛
         */
        cash?: number;
        /**
         * 折扣
         */
        discount?: number;
        /**
         * 最多减
         */
        maxSub?: number;
        /**
         * 减
         */
        sub?: number;
        /**
         * 满
         */
        to?: number;
    }

    /**
     * TimeModel_1
     */
    export interface TimeModel1 {
        /**
         * 有效结束时间
         */
        expiryEnd?: Date;
        /**
         * 有效开始时间
         */
        expiryStart?: Date;
        /**
         * 领取次天起几日内有效
         */
        nextDay?: number;
        /**
         * 领取当天起几日内有效
         */
        sameDay?: number;
    }

    /**
     * GoodsMarkDTO
     */
    export interface GoodsMarkDTO {
        /**
         * 角标主键id
         */
        id?: number;
        /**
         * 角标展示状态：0.不展示，1.展示
         */
        markStatus?: number;
        /**
         * 角标样式
         */
        markStyle?: string;
        /**
         * 展示区域：1商品主图右上角，2商品主图左上角
         */
        showArea?: number;
        /**
         * 展示位置：0.全局展示，1.仅在组件列表展示，2.仅在商品详情展示
         */
        showPlace?: number;
        /**
         * 商品id
         */
        spuId?: number;
    }

    /**
     * SpuRefundRate
     */
    export interface SpuRefundRate {
        /**
         * 比例 1-100 整数
         */
        rate?: number;
        /**
         * 时间
         */
        time?: Date;
    }

    /**
     * GoodsStyleDTO
     */
    export interface GoodsStyleDTO {
        /**
         * 横幅背景
         */
        backPic?: string;
        /**
         * 文案类型：1.文字，2.图片
         */
        descType?: number;
        /**
         * 样式主键id
         */
        id?: number;
        /**
         * 图片描述
         */
        picDesc?: string;
        /**
         * 排序：1选中，2不使用
         */
        sort?: number;
        /**
         * 商品id
         */
        spuId?: number;
        /**
         * 样式展示状态：0.不展示，1.展示
         */
        styleStatus?: number;
        /**
         * 文字描述
         */
        textDesc?: string;
        /**
         * 商详样式类型：1.样式一，2.样式二
         */
        type?: number;
        /**
         * 上下架样式类型：1.样式一, 2.自定义
         */
        upType?: number;
    }

    /**
     * PicResponseDTO
     */
    export interface PicResponseDTO {
        picId?: number;
        picSort?: number;
        picUrl?: string;
        spuId?: number;
    }

    /**
     * SourceMaterialResVO
     */
    export interface SourceMaterialResVO {
        /**
         * 创建时间
         */
        gmtCreate?: Date;
        /**
         * 更新时间
         */
        gmtModified?: Date;
        /**
         * 素材id
         */
        id?: number;
        /**
         * 图片和视频是url,文案就是文案类容
         */
        refObj?: string;
        /**
         * 排序字段
         */
        sort?: number;
        /**
         * 商品id
         */
        spuId?: number;
        /**
         * 素材类型 1.图片 2.视频 3.文案
         */
        type?: number;
    }

    /**
     * PicResponseVO
     */
    export interface PicResponseVO {
        /**
         * 图片id
         */
        picId?: number;
        /**
         * 图片排序
         */
        picSort?: number;
        /**
         * 图片地址
         */
        picUrl?: string;
        /**
         * spu-id
         */
        spuId?: number;
    }

    /**
     * SkuResponseVO
     */
    export interface SkuResponseVO {
        /**
         * 佣金金额
         */
        commissionMoney?: number;
        /**
         * 一级分销扣点
         */
        commissionRate?: number;
        /**
         * 佣金类型
         */
        commissionType?: number;
        /**
         * 成本价
         */
        costPrice?: number;
        /**
         * 发货标签
         */
        deliveryLabel?: string;
        /**
         * 最迟发货时间
         */
        endDeliveryTime?: Date;
        /**
         * 商品修改时间
         */
        gmtModified?: Date;
        /**
         * 发货时效 默认是0不限
         */
        goodsDeliveryAgeing?: number;
        /**
         * 商品会员利润比例配置编码
         */
        goodsGradeProfitCode?: string;
        /**
         * 店铺等级利润比（服务商比例 = 100% - 店铺等级利润比例）
         */
        gradeProfit?: number;
        /**
         * 毛重
         */
        grossWeight?: number;
        /**
         * 毛重单位
         */
        grossWeightUnit?: string;
        /**
         * 是否删除 0.需要维护的sku 1.不维护的sku
         */
        isDelete?: number;
        /**
         * 是否隐藏 0 否 1 是
         */
        isHide?: number;
        /**
         * 京东价
         */
        jdPrice?: number;
        /**
         * 京东比价比例
         */
        jdPriceRate?: number;
        /**
         * 京东比价金额
         */
        jdPriceSub?: number;
        /**
         * 京东价更新时间
         */
        jdPriceUpdate?: Date;
        /**
         * 京东地址
         */
        jdUrl?: string;
        /**
         * 净重
         */
        netWeight?: number;
        /**
         * 净重单位
         */
        netWeightUnit?: string;
        /**
         * 剩余数量
         */
        newShopNum?: number;
        /**
         * 新店长价格
         */
        newShopPrice?: number;
        /**
         * 新人价
         */
        newUserPrice?: number;
        /**
         * sku库存数量
         */
        num?: number;
        /**
         * 下单数量设置
         */
        orderQuantity?: number;
        /**
         * 其它价格
         */
        otherPrices?: OtherPriceReqVO[];
        /**
         * 一级分销扣点
         */
        primaryDistributionPercentage?: number;
        /**
         * 规格明细
         */
        properties?: PropertiesResponseVO[];
        /**
         * 规格描述
         */
        propertyValuesStr?: string;
        /**
         * 规格图片
         */
        propPicUrl?: string;
        /**
         * sku零售价
         */
        retailPrice?: number;
        /**
         * 销售状态 0 现售 1 预售
         */
        salesStatus?: number;
        /**
         * 安全库存
         */
        securityStockNum?: number;
        /**
         * 供应商结算价
         */
        settlementPrice?: number;
        /**
         * 活动数量
         */
        skuApplyCount?: number;
        /**
         * sku编码
         */
        skuCode?: string;
        /**
         * sku-id
         */
        skuId?: number;
        /**
         * 群友内购活动价格
         */
        skuPrice?: number;
        /**
         * 排序 默认是50
         */
        skuSort?: number;
        /**
         * spu-id
         */
        spuId?: number;
        /**
         * 开始发货时间
         */
        startDeliveryTime?: Date;
        /**
         * 供应商编码
         */
        supplierCode?: string;
        /**
         * 供应商id
         */
        supplierId?: number;
        /**
         * 供应商sku编码
         */
        supplierSkuCode?: string;
        /**
         * 税率
         */
        taxation?: number;
        /**
         * 第三方编码
         */
        thirdPartySkuCode?: string;
        /**
         * 天猫价格
         */
        tmPrice?: number;
        /**
         * 天猫比价比例
         */
        tmPriceRate?: number;
        /**
         * 天猫比价金额
         */
        tmPriceSub?: number;
        /**
         * 天猫价更新时间
         */
        tmPriceUpdate?: Date;
        /**
         * 天猫地址
         */
        tmUrl?: string;
        /**
         * sku单价
         */
        unitPrice?: number;
        /**
         * 云仓标识 1 顺丰云仓 0 普通
         */
        yunStorageFlag?: number;
    }

    /**
     * OtherPriceReqVO
     */
    export interface OtherPriceReqVO {
        /**
         * 其它平台
         */
        otherPlatform?: string;
        /**
         * 其它价
         */
        otherPrice?: number;
    }

    /**
     * PropertiesResponseVO
     */
    export interface PropertiesResponseVO {
        /**
         * 属性id
         */
        propertyId?: number;
        /**
         * 属性名字
         */
        propertyName?: string;
        /**
         * 属性值名字
         */
        propertyValue?: string;
        /**
         * 属性值id
         */
        propertyValueId?: number;
        /**
         * 属性排序
         */
        sort?: number;
    }

    /**
     * GoodsScopeModel
     */
    export interface GoodsScopeModel {
        /**
         * 品牌集合
         */
        brands?: number[];
        /**
         * 类目集合
         */
        categories?: number[];
        /**
         * 是否赔付券 0 否 1 是
         */
        isCompensation?: number;
        /**
         * 商品范围（0-普通商品;1-店长专享;3-团批商品;4-企业采购商品库；5-外部商品库）
         */
        isShopExclusive?: number;
        /**
         * 商品id集合
         */
        spuIds?: number[];
        /**
         * 商品集合
         */
        spus?: GoodsScopeSpuModel[];
        /**
         * 1.全部 2.指定商品 3.前台类目 4.品牌
         */
        type?: number;
    }

    /**
     * GoodsScopeSpuModel
     */
    export interface GoodsScopeSpuModel {
        /**
         * 商品sku
         */
        skus?: GoodsScopeSkuModel[];
        /**
         * 商品id
         */
        spuId?: number;
    }

    /**
     * GoodsScopeSkuModel
     */
    export interface GoodsScopeSkuModel {
        /**
         * 商品sku规格名称
         */
        skuDesc?: string;
        /**
         * 商品skuId
         */
        skuId?: number;
    }

    /**
     * TimeModel_3
     */
    export interface TimeModel3 {
        /**
         * 有效结束时间
         */
        expiryEnd?: Date;
        /**
         * 有效开始时间
         */
        expiryStart?: Date;
        /**
         * 领取次天起几日内有效
         */
        nextDay?: number;
        /**
         * 领取当天起几日内有效
         */
        sameDay?: number;
    }

    /**
     * 福袋信息
     *
     * LuckyBagInfoRespVO
     */
    export interface LuckyBagInfoRespVO {
        /**
         * 预开始时间
         */
        autoStartDate?: Date;
        /**
         * 福袋id
         */
        bagId?: number;
        /**
         * 倒计时 单位:秒  ,0 不倒计时
         */
        countDownNum?: number;
        /**
         * 优惠券列表
         */
        couponList?: LuckyCouponRespVO[];
        /**
         * 创建人
         */
        createBy?: string;
        /**
         * 门槛 0 不限 1 未关注主播 2 全部关注主播 3 分享直播间可领 4 邀请进入直播间
         */
        doorsill?: number;
        /**
         * 创建时间
         */
        gmtCreate?: Date;
        /**
         * 结束时间
         */
        gmtEnd?: Date;
        /**
         * 更新时间
         */
        gmtModified?: Date;
        /**
         * 开始时间
         */
        gmtStart?: Date;
        /**
         * 图标
         */
        icon?: string;
        /**
         * 图标类型 0 自定义
         */
        iconType?: number;
        /**
         * 邀请类型(1:全部用户都为有效邀请,2:有效邀请仅为本场直播间新用户)
         */
        inviteType?: number;
        /**
         * 限领次数 0 不限制
         */
        limitNum?: number;
        /**
         * 活动名称/福袋名称
         */
        name?: string;
        /**
         * 来源：1:中台 2:主播
         */
        source?: string;
        /**
         * 0 移除 1 待开始  2 进行中  3 已结束   更新直播间时传
         */
        status?: number;
        /**
         * 系统时间
         */
        systemDate?: Date;
        /**
         * 目标数量  邀请XXX人
         */
        target?: number;
        /**
         * 1优惠券
         */
        type?: number;
    }

    /**
     * LuckyBagCouponInfoDTO
     */
    export interface LuckyBagCouponInfoDTO {
        /**
         * 优惠券id
         */
        couponId?: number;
        /**
         * 排序
         */
        sort?: number;
        /**
         * 直播间可发放数量
         */
        transferNum?: number;
    }

    /**
     * LuckyBagDistributeRuleDTO
     */
    export interface LuckyBagDistributeRuleDTO {
        /**
         * 倒计时时间
         */
        countDownNum?: number;
        /**
         * 发放类型 1:手动开始 2:立即开始 3:倒计时开始
         */
        distributeType?: number;
    }
    /**
     * LuckyBagCouponInfoDTO
     */
    export interface LuckyBagCouponInfoDTO {
        /**
         * 优惠券id
         */
        couponId?: number;
        /**
         * 排序
         */
        sort?: number;
        /**
         * 直播间可发放数量
         */
        transferNum?: number;
    }

    export interface LuckyBagCountRespVO {
        /**
         * 福袋数量数据
         */
        luckyBagCountList?: LuckyBagCount[];
        /**
         * 福袋总数
         */
        total?: number;
        [property: string]: unknown;
    }

    /**
     * com.meiji.proxy.live.vo.response.luckyBag.LuckyBagCountRespVO.LuckyBagCount
     *
     * LuckyBagCount
     */
    export interface LuckyBagCount {
        /**
         * 数量
         */
        countNum?: number;
        /**
         * 状态
         */
        status?: number;
        /**
         * 状态描述
         */
        statusDesc?: string;
    }

    /**
     * LuckyBagDistributeRuleDTO
     */
    export interface LuckyBagDistributeRuleDTO {
        /**
         * 倒计时时间
         */
        countDownNum?: number;
        /**
         * 发放类型 1:手动开始 2:立即开始 3:倒计时开始
         */
        distributeType?: number;
    }
    export namespace Params {
        export type getLiveLuckyBags = {
            page?: number; // 页码, 默认1
            rows?: number; // 条数, 默认10
            liveId: number; // 直播间id
            luckyBagId?: number; // 福袋id
            status?: number; // 1 未开始 2 进行中 3 已结束
        };
        export type getLiveLuckyBagsCount = {
            liveId: number; // 直播间id
        };
        export type addLuckyBag = {
            liveId: number; // 直播间id
            luckyBagInfo: {
                /**
                 * 优惠券列表
                 */
                couponList?: LuckyBagCouponInfoDTO[];
                distributeRule?: LuckyBagDistributeRuleDTO;
                /**
                 * 门槛 0 不限 1 未关注主播 2 全部关注主播 3 分享直播间可领 4 邀请进入直播间
                 */
                doorsill?: number;
                /**
                 * 图标
                 */
                icon?: string;
                /**
                 * 图标类型 0 自定义
                 */
                iconType?: number;
                /**
                 * 邀请类型(1:全部用户都为有效邀请,2:有效邀请仅为本场直播间新用户)
                 */
                inviteType?: number;
                /**
                 * 限领次数 0 不限制
                 */
                limitNum?: number;
                /**
                 * 活动名称/福袋名称
                 */
                name?: string;
                /**
                 * 目标数量  邀请XXX人
                 */
                target?: number;
                /**
                 * 福袋类型 1 优惠券
                 */
                type?: number;
            };
        };
        export type updateLuckyBag = {
            /**
             * 0立即结束 1立即发放 2 移除
             */
            action?: number;
            /**
             * 福袋id
             */
            bagId?: number;
            /**
             * 直播间id
             */
            liveId?: number;
        };
        export type editLuckyBag = {
            /**
             * 直播间id
             */
            liveId?: number;
            luckyBagInfo?: EditLuckyBagReqDTO;
        };
        export type getLiveLuckyBagDetail = {
            /**
             * 福袋id
             */
            bagId: number;
            /**
             * 直播间id
             */
            liveId: number;
        };
        /**
         * LuckyBagDistributeRuleDTO
         */
        export interface LuckyBagDistributeRuleDTO {
            /**
             * 倒计时时间
             */
            countDownNum?: number;
            /**
             * 发放类型 1:手动开始 2:立即开始 3:倒计时开始
             */
            distributeType?: number;
            [property: string]: unknown;
        }
    }
    export namespace Response {
        export type getLiveLuckyBags = {
            luckyBagCountList: {
                count?: 0 | undefined;
                endedCount?: 0 | undefined;
                inProgressCount?: 0 | undefined;
                toBeginCount?: 0 | undefined;
            };
            dataList: {
                autoStartDate: string; // 预开始时间
                bagId: number; // 福袋id
                countDownNum: number; // 倒计时 单位:秒 ,0 不倒计时
                couponList: LuckyCouponRespVO[];
                distributeRule?: LuckyBagDistributeRuleDTO;
                /**
                 * 门槛 0 不限 1 未关注主播 2 全部关注主播 3 分享直播间可领 4 邀请进入直播间
                 */
                distributeType: number;
                createBy: string; // 创建人
                doorsill: number; // 门槛 0 不限 1 未关注主播 2 全部关注主播 3 分享直播间可领 4 邀请进入直播间
                gmtCreate: string; // 创建时间
                gmtEnd: string; // 结束时间
                gmtModified: string; // 更新时间
                gmtStart: string; // 开始时间
                icon: string; // 图标
                iconType: number; // 图标类型 0 自定义
                inviteType: number; // 邀请类型(1:全部用户都为有效邀请,2:有效邀请仅为本场直播间新用户)
                limitNum: number; // 限领次数 0 不限制
                name: string; // 福袋名称
                source: string; // 来源：1:中台 2:主播
                status: number; // 0 移除 1 待开始 2 进行中 3 已结束 更新直播间时传
                systemDate: string; // 系统时间
                type: number; // 1 优惠券
                target: number; // 目标数量 邀请XXX人
            };
            total: number; // 总条数
        };
        export type getLiveLuckyBagsCount = {
            code?: string;
            data?: LuckyBagCountRespVO;
            msg?: string;
            timestamp?: number;
            trace?: string;
        };
        export type addLuckyBag = {
            code?: string;
            data?: number;
            msg?: string;
            timestamp?: number;
            trace?: string;
        };
        export type updateLuckyBag = {
            code?: string;
            data?: { [key: string]: unknown };
            msg?: string;
            timestamp?: number;
            trace?: string;
        };
        export type editLuckyBag = {
            code?: string;
            data?: { [key: string]: unknown };
            msg?: string;
            timestamp?: number;
            trace?: string;
        };
        export type getLiveLuckyBagDetail = {
            /**
             * 福袋信息
             */
            luckyBagInfo?: LuckyBagInfoRespVO;
            /**
             * 未使用数量
             */
            notUsedNum?: number;
            /**
             * 领取数量
             */
            receiveNum?: number;
            /**
             * 总数
             */
            totalNum?: number;
            /**
             * 已使用数量
             */
            usedNum?: number;
        };
    }
}
