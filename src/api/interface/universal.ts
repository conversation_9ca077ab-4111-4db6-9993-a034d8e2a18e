/**
 * @Owners xj
 * @Title 通用接口
 */

export namespace Universal {
    export interface FilesUploadType {
        data: string;
    }

    interface AreaItemType {
        id: number;
        name: string;
        arealevel: 1 | 2 | 3; // 级别 1：省 2：市 3：区
        parentId?: number; // 父级id
        children?: AreaItemType[];
    }

    export interface AreaInfoType {
        data: AreaItemType[];
    }
}
