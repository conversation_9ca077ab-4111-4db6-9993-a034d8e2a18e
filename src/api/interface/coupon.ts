/**
 * @Owners fleix.cai
 * @Title 优惠券
 */

export namespace Coupon {
    export interface TimeModel1 {
        /**
         * 有效结束时间
         */
        expiryEnd?: Date;
        /**
         * 有效开始时间
         */
        expiryStart?: Date;
        /**
         * 领取次天起几日内有效
         */
        nextDay?: number;
        /**
         * 领取当天起几日内有效
         */
        sameDay?: number;
    }

    /**
     * GoodsInfo
     */
    export interface GoodsInfo {
        /**
         * 品类
         */
        categoryName?: string;
        /**
         * 商品id
         */
        spuId?: number;
        /**
         * 商品名称
         */
        spuName?: string;
        /**
         * 商品图片
         */
        spuPic?: string;
    }

    /**
     * DiscountRuleModel_2
     */
    export interface DiscountRuleModel2 {
        /**
         * 无门槛
         */
        cash?: number;
        /**
         * 折扣
         */
        discount?: number;
        /**
         * 最多减
         */
        maxSub?: number;
        /**
         * 减
         */
        sub?: number;
        /**
         * 满
         */
        to?: number;
    }

    export type CouponListType = {
        couponMoney: number;
        // 总的优惠金额
        discountMoney: number | undefined;
        // 优惠规则 满100-20,满100打2折,最多少20,30元无门槛券
        discountRuleModels: {
            // 无门槛
            cash: number | undefined;
            // 折扣
            discount: number | undefined;
            // 最多减
            maxSub: number | undefined;
            // 减
            sub: number | undefined;
            // 满
            to: number | undefined;
        }[];
        usableCount?: number;
        couponType?: number;
        usedCount?: number;
        occupyNum: number;
        // 优惠类型 1.满减券 2.折扣券 3.无门槛
        discountType: number;
        // 有效使用量
        effectiveUsedNum: number | undefined;
        // 有效使用比例
        effectiveUsedRate: number | undefined;
        // 有效结束时间
        expiryEnd: string;
        // 有效开始时间
        expiryStart: string;
        // 更新时间
        gmtModified: string;
        id: number;
        // 优惠券名字
        name: string;
        desc: string;
        // 总的真实优惠金额
        realDiscountMoney: number | undefined;
        // 领取数量
        receiveNum: number | undefined;
        // 领取比例
        receiveRate: number | undefined;
        // 优惠券状态 1.未生效 2.已生效 3.已过期 4.已作废
        status: number;
        isProfitPenetrate: number;
        // 有效期
        timeModel: {
            // 有效结束时间
            expiryEnd: string;
            // 有效开始时间
            expiryStart: string;
            // 领取次天起几日内有效
            nextDay: number | undefined;
            // 领取当天起几日内有效
            sameDay: number | undefined;
        };
        // 总发行量
        total: number | undefined;
        // 可用库存
        usableNum: number;
        // 已经发送的数量
        usedNum: number | undefined;
        // 商品范围
        goodsScopeType: number;
        fullSubtractType: number; // 1满额减2满件减
        goodsScopeModel: {
            brands: number[];
            categories: number[];
            isCompensation: number; // 是否赔付券 0 否 1 是
            isShopExclusive: number;
            spuIds: number[];
            type: number;
        };
    };

    export type supplierLiveCouponType = {
        /**
         * 1.小程序商城券 2.直播间专属券 3.复购券 4：单品券
         */
        couponType?: number;
        couponTab?: string;
        /**
         * 描述
         */
        desc?: string;
        /**
         * 优惠规则 满100-20,满100打2折,最多少20,30元无门槛券
         */
        discountRuleModels: DiscountRuleModel2[];
        /**
         * 优惠类型 1.满减券 2.折扣券 3.无门槛
         */
        discountType: number;
        /**
         * 有效结束时间
         */
        expiryEnd?: string;
        /**
         * 有效开始时间
         */
        expiryStart?: string;
        /**
         * 满减券类型 1 满额减  2 满件减
         */
        fullSubtractType?: number;
        /**
         * 更新时间
         */
        gmtModified?: string;
        /**
         * 商品信息（只取第一个商品）
         */
        spuInfoList: GoodsInfo[];
        goodsInfoList: GoodsInfo[];
        /**
         * 1.全部 2.指定商品 3.前台类目 4.品牌 5.专题
         */
        goodsScopeType?: number;
        spuScopeType?: number;
        id?: number;
        /**
         * 是否限定新人下单（即是只使用一次）：0-否，1-是
         */
        isLimitNewcomer?: number;
        /**
         * 是否平台补差：0否，1是
         */
        isPlatformSubsidy?: number;
        /**
         * 是否利润穿透：0-计算中，1-是 2-否
         */
        isProfitPenetrate?: number;
        /**
         * 是否支持分享：0-否，1-是
         */
        isSupportShare?: number;
        /**
         * 优惠券名字
         */
        name: string;
        /**
         * 占用库存
         */
        occupyNum?: number;
        /**
         * 指定店长手机号码,直播券时必传
         */
        shopPhone?: string;
        /**
         * 优惠券状态 1.未生效 2.已生效 3.已过期 4.已作废
         */
        status: number;
        timeModel: TimeModel1;
        /**
         * 总发行量
         */
        total?: number;
        /**
         * 可用库存
         */
        usableNum?: number;
    };

    export namespace Params {
        export type getCouponListByPage = {
            activeType?: number;
            discountType?: number;
            expiryEnd?: string;
            expiryStart?: string;
            id?: string;
            name?: string;
            page?: number;
            rows?: number;
            status?: string;
            isExchangeFraction?: number;
            couponType?: number | undefined; // 1 小程序商城券 ; 2直播间营销券
            couponTypes?: number[];
            shopPhone?: string;
            nameOrId?: string; // 优惠券名字或券id
            desc?: string;
            isProfitPenetrate?: number;
            isFilterBuybackCoupon?: number;
            getValidDate?: number;
            goodsScopeIsShopExclusive?: number;
            isLimitNewcomer?: number;
            visibility?: number;
        };

        export type getCouponDetailById = {
            id: number | string | undefined;
        };

        export type supplierLiveCouponPage = {
            /**
             * 优惠券名字
             */
            name?: string;
            /**
             * 页码, 默认1
             */
            page?: number;
            /**
             * 条数, 默认10
             */
            rows?: number;
            /**
             * 优惠券状态 1.未生效 2.已生效 3.已过期 4.已作废
             */
            status?: number;
        };
    }
    export namespace Response {
        export type getCouponListByPage = {
            dataList: CouponListType[];
            total: number;
        };
        export type getCouponDetailById = {
            data: CouponListType;
        };

        export type supplierLiveCouponPage = {
            dataList: supplierLiveCouponType[];
            total: number;
        };
    }
}
