/**
 * @Owners mzh
 * @Title 近往期直播预告
 */

export namespace LivePreview {
    export namespace Params {
        export type Condition = {};

        export type getPastPreLivePage = {
            condition?: {};
            page: number;
            rows: number;
        };

        export type getPastLivePage = {
            condition?: {};
            page: number;
            rows: number;
        };
    }

    export namespace Response {
        // 近期往期直播列表项综合
        export type LiveItem = {
            coverUrl: string;
            goodNumber: number;
            liveId: number;
            liveName: string;
            liveStatus: number;
            liveTheme: number;
            preStartTime: string;
            pushStatus: number;
            roomCode: number;
            subscribeNumber: number;
            liveType: number;
            fullSkuNumbers: number;
            fullTotalGmv: number;
            liveStartTime: string;
            liveEndTime: string;
            liveSkuNumbers: number;
            liveTotalGmv: number;
            orderRate: number;
            totalLiveTime: number;
            totalLiveTimeStr: string;
            totalOnlineCount: number;
            totalOnlineUsers: number;
            uvValue: number;
            /**
             * 直播录制url
             */
            videoUrl: string;
            /**
             * 直播录制下载url
             */
            videoDownloadUrl: string;
        };

        export type getLiveList = {
            dataList: LiveItem[];
            total: number;
        };

        export type getPastLiveStatistics = {
            avgLiveTimeStr: string;
            avgLiveTime: number;
            liveNumbers: number;
            monthAvgLives: number;
            monthLiveCounts: number;
            weekAvgLives: number;
            weekLiveCounts: number;
            userId: number;
        };
    }
}
