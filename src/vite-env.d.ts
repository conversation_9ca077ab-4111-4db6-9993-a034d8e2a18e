/**
 * @Owners init
 * @Title vite 相关配置
 */

/// <reference types="vite/client" />
declare module 'tcplayer.js';

declare type Recordable<T = unknown> = Record<string, T>;

declare type ImportMetaEnv = Partial<{
    /** 接口域名 */
    VITE_API_HOST: string;
    VITE_PORT: number;
    VITE_OPEN: boolean;
    VITE_GLOB_APP_TITLE: string;
    VITE_DROP_CONSOLE: boolean;
    VITE_PROXY_URL: string;
    VITE_BUILD_GZIP: boolean;
}>;

interface ImportMeta {
    readonly env: ImportMetaEnv;
}
