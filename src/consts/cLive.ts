/**
 * @Owners lvbenlong
 * @Title 直播模块常用常量
 */
/** 是否首次进入 中控台 key */
export const FIRST_ENTER_CONSOLE = 'FIRST_ENTER_CONSOLE';

/** 中控台交互tabs */
export enum ConsoleInteractiveTabs {
    /** 直播tab */
    Live = '1',
    /** 用户列表 */
    UserList = '2',
    /** 连麦 */
    InteractiveLive = '3',
}

/** 中控台Top交互tabs */
export enum ConsoleTopTabs {
    /** 直播tab */
    Live = '1',
    /** 商品分析 */
    Shop = '2',
    /** 用户分析 */
    User = '3',
    /** AI诊断 */
    Ai = '4',
}

// namespace _cLive {
// 推流状态--映射直播间状态
export enum PushStatusMap {
    /** 未推流-直播中 */
    NoPush = 1,
    /** 推流中-直播中 */
    Pushing = 2,
    /** 暂停推流-直播中 */
    PausePush = 3,
    /** 禁止推流-直播中 */
    ForbidPush = 4,
    /** 停止推流-直播结束 */
    StopPush = 5,
    /** 未开始-此时没有推流地址-预约类型直播间-指定预约时间之前-未开始 */
    NoStart = 6,
    /** 未开播-此时没有推流地址-预约类型直播间-指定预约时间之后48小时内一直没有流-未开播 */
    NoLive = 7,
    /** 已失效-此时没有推流地址-预约类型直播间-指定预约时间之后48小时后一直没有流-未开播 */
    Expired = 8,
}

/** IM消息状态 */
export enum IMMessageStatus {
    /** 正常 */
    Normal = 1,
    /** 隐藏 */
    Hide = 2,
}

/** IM消息类型 */
export enum IMMessageCustomEvent {
    /** 直播间在线人数更改 */
    REFRESH_LIVE_USER_NUM = 'REFRESH_LIVE_USER_NUM',
    /** 直播间状态 */
    RoomStatus = 'ROOM_STATUS',
    /** 用户禁言 */
    Mute = 'MUTE',
    /** 主播开启连麦 */
    LiveMicOpen = 'LIVE_MIC_OPEN',
    /** 主播关闭连麦 */
    LiveMicClose = 'LIVE_MIC_CLOSE',
    /** 主播关闭/开启摄像头 */
    ManageCameraOff = 'MANAGE_CAMERA_OFF',
    /** 主播静音/取消静音 */
    ManageMicMute = 'MANAGE_MIC_MUTE',
    /** 用户操作静音与解除静音 */
    ManageMicMuteUser = 'MANAGE_MIC_MUTE_USER',
    /** 主播踢出连麦用户 */
    ManageMicKick = 'MANAGE_MIC_KICK',
    /** 观众取消连麦 */
    ApplyMicCancel = 'APPLY_MIC_CANCEL',
    /** 观众申请连麦(无需主播审核) */
    ApplyMicRequest = 'APPLY_MIC_REQUEST',
    /** 观众申请连麦(需主播审核)   */
    ApplyMicRequestWait = 'APPLY_MIC_REQUEST_WAIT',
    /** 观众切换媒体类型 */
    ManageMicUserSwitchMediaType = 'MANAGE_MIC_USER_SWITCH_MEDIA_TYPE',
    /** 观众同意/拒绝连麦邀请 */
    InviteMicResponse = 'INVITE_MIC_RESPONSE',
    /** 主播接受用户申请连麦 */
    ApplyMicAccept = 'APPLY_MIC_ACCEPT',
    /** 用户主动断开连麦 */
    ManageMicUserDisconnect = 'MANAGE_MIC_USER_DISCONNECT',
    /** 连麦观众接受/拒绝主播邀请打开摄像头 */
    LiveMicInviteUserOpenCamera = 'LIVE_MIC_INVITE_USER_OPEN_CAMERA',
}

// 推流状态
export const PushStatusTextMap: {
    [key: number]: {
        text: string;
        value: number;
        color: string;
    };
} = {
    [PushStatusMap.NoPush]: { text: '未推流', value: PushStatusMap.NoPush, color: '#999999' },
    [PushStatusMap.Pushing]: {
        text: '直播中',
        value: PushStatusMap.Pushing,
        color: '#FD395A',
    },
    [PushStatusMap.PausePush]: { text: '已暂停', value: PushStatusMap.PausePush, color: '#FF8F16' },
    [PushStatusMap.ForbidPush]: { text: '已禁用', value: PushStatusMap.ForbidPush, color: '#999999' },
    [PushStatusMap.StopPush]: {
        text: '已结束',
        value: PushStatusMap.StopPush,
        color: '#00B68F',
    },
    [PushStatusMap.NoStart]: { text: '未开始', value: PushStatusMap.NoStart, color: '#999999' },
    [PushStatusMap.NoLive]: { text: '未开播', value: PushStatusMap.NoLive, color: '#999999' },
    [PushStatusMap.Expired]: { text: '已过期', value: PushStatusMap.Expired, color: '#999999' },
};

// 推流类型
export enum PushTypeMap {
    OBS = 1, // 外设推流
    PHONE = 2, // 手机推流
}

// 推流类型
export const PushTypeTextMap: {
    [key: number]: {
        text: string;
        value: number;
        color: string;
    };
} = {
    [PushTypeMap.OBS]: { text: '外设推流', value: PushTypeMap.OBS, color: '#1890FF' },
    [PushTypeMap.PHONE]: { text: '手机推流', value: PushTypeMap.PHONE, color: '#13C2C2' },
};

// 直播间推流类型
export enum LiveTypeMap {
    PUBLIC = 1,
    PRIVATE = 2,
}

// 直播间推流类型
export const LiveTypeTextMap: {
    [key: number]: {
        text: string;
        value: number;
        color: string;
    };
} = {
    [LiveTypeMap.PUBLIC]: { text: '公开直播', value: LiveTypeMap.PUBLIC, color: '#52C41A' },
    [LiveTypeMap.PRIVATE]: { text: '私密直播', value: LiveTypeMap.PRIVATE, color: '#722ED1' },
};

// 直播间类型
export enum LiveCategoryMap {
    NOW = 1, // 即时开播
    PRE = 2, // 直播预告
}

// 直播间类型
export const LiveCategoryTextMap: {
    [key: number]: {
        text: string;
        value: number;
        color: string;
    };
} = {
    [LiveCategoryMap.NOW]: { text: '即时开播', value: LiveCategoryMap.NOW, color: 'green' },
    [LiveCategoryMap.PRE]: { text: '直播预告', value: LiveCategoryMap.PRE, color: 'purple' },
};

/** 连麦状态 */
export enum LiveEnableMicStatus {
    /** 关闭 */
    Close = 0,
    /** 开启 */
    Open = 1,
}

/** 连麦摄像头状态 */
export enum CoStreamCameraStatus {
    /** 摄像头已关闭 */
    Close = 0,
    /** 摄像头已开 */
    Open = 1,
    /** 强制关闭 */
    ForceClose = 2,
}

/** 连麦麦克风状态 */
export enum CoStreamMicStatus {
    /** 麦克风已开 */
    Open = 0,
    /** 麦克风已关闭 */
    Close = 1,
}

/** 连麦历史类型 */
export enum LiveMicHistoryType {
    /** 刚邀请过 */
    Invited = 1,
    /** 刚拒绝过 */
    Rejected = 2,
    /** 今天连线过 */
    ConnectedToday = 3,
}
// }

/** 快捷评论类型 */
export enum LiveQuickCommentType {
    /** 全局 */
    Global = 1,
    /** 非全局 */
    NonGlobal = 0,
}

// export const cLive = {
//     ..._cLive,
// };
