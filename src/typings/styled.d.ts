/**
 * @Owners zp
 * @Title styled-components类型声明
 */
import 'styled-components';

// and extend them!
declare module 'styled-components' {
    export interface DefaultTheme {
        colors: {
            primaryColor: string;
            primaryColorRgba: string;
            colorError: string;
            menuBgColor: string;
            tabBgColor: string;
            lightBgColor: string;
            lightMainBgColor: string;
            lightBorderColor: string;
            lightBorderHeaderColor: string;
            lightTextColor: string;
            lightShadowColor: string;
            lightScrollbarBgColor: string;
            scrollbarColor: string;
            scrollbarThumbColor: string;
        };
        borderRadius: number;
    }
}
