/**
 * @Owners haoyang
 * @Title cookie store 类型
 */

declare namespace ICookieStore {
    interface CookieEvent extends Event {
        changed: <PERSON>ieList;
        deleted: <PERSON>ieList;
    }

    interface CookieCahnged {
        changed?: CookieListItem;
        deleted?: CookieListItem;
    }

    interface CookieStore extends EventTarget {
        get(options?: CookieStoreGetOptions | string): Promise<CookieListItem | null>;

        getAll(name?: CookieStoreGetOptions | string): Promise<CookieList>;

        set(name: string, value: string): Promise<void>;
        set(options: CookieInit): Promise<void>;

        delete(name: CookieStoreDeleteOptions | string): Promise<void>;

        addEventListener(type: string, callback: (evt: CookieEvent) => void, options?: AddEventListenerOptions | boolean): void;
    }
    interface CookieStoreGetOptions {
        name?: string;
        url?: string;
    }

    type CookieSameSite = 'lax' | 'none' | 'strict';

    interface CookieInit {
        name: string;
        value: string;
        expires?: Date | number | null;
        domain?: string | null;
        path?: string;
        sameSite?: CookieSameSite;
        httpOnly?: boolean;
    }
}
interface CookieStoreDeleteOptions {
    name: string;
    domain?: string | null;
    path?: string;
}

interface CookieListItem {
    name: string;
    value: string;
    domain?: string | null;
    path?: string;
    expires?: Date | null;
    secure?: boolean;
    sameSite?: CookieSameSite;
}

type CookieList = CookieListItem[];
