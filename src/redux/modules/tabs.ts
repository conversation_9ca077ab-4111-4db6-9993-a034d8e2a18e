/**
 * @Owners zp
 * @Title tabs - redux
 */
import { HOME_URL } from '@/config/config';
import { type TabsState } from '@/redux/interface';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

const tabsState: TabsState = {
    tabsList: [{ name: '首页', path: HOME_URL }],
};

const tabsSlice = createSlice({
    name: 'tabs',
    initialState: tabsState,
    reducers: {
        setTabsList(state: TabsState, { payload }: PayloadAction<TabsState['tabsList']>) {
            state.tabsList = !payload.length ? tabsState.tabsList : payload;
        },
    },
});

export const { setTabsList } = tabsSlice.actions;
export default tabsSlice.reducer;
