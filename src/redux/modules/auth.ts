/**
 * @Owners haoyang
 * @Title 鉴权-redux
 */

import { type AuthState } from '@/redux/interface';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

const authState: AuthState = {
    authButtons: {},
    authRouter: [],
};

const authSlice = createSlice({
    name: 'auth',
    initialState: authState,
    reducers: {
        setAuthButtons(state: AuthState, { payload }: PayloadAction<{ [propName: string]: { [key: string]: boolean } }>) {
            state.authButtons = payload;
        },
        setAuthRouter(state: AuthState, { payload }: PayloadAction<string[]>) {
            state.authRouter = payload;
        },
    },
});

export const { setAuthButtons, setAuthRouter } = authSlice.actions;
export default authSlice.reducer;
