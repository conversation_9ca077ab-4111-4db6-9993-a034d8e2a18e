/**
 * @Owners haoyang
 * @Title 菜单-redux
 */

import { type SysMenu } from '@/api/interface/system/SysMenu';
import { type MenuState } from '@/redux/interface';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

const menuState: MenuState = {
    isCollapse: false,
    menuList: [],
};

const menuSlice = createSlice({
    name: 'menu',
    initialState: menuState,
    reducers: {
        updateCollapse(state: MenuState, { payload }: PayloadAction<boolean>) {
            state.isCollapse = payload;
        },
        setMenuList(state: MenuState, { payload }: PayloadAction<SysMenu.MenuItem[]>) {
            state.menuList = payload;
        },
    },
});

export default menuSlice.reducer;
export const { updateCollapse, setMenuList } = menuSlice.actions;
