/**
 * @Owners haoyang
 * @Title 面包屑-redux
 */

import { type BreadcrumbState } from '@/redux/interface';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

const breadcrumbState: BreadcrumbState = {
    breadcrumbList: {},
};

const breadcrumbSlice = createSlice({
    name: 'breadcrumb',
    initialState: breadcrumbState,
    reducers: {
        setBreadcrumbList(state: BreadcrumbState, { payload }: PayloadAction<{ [propName: string]: string[] }>) {
            state.breadcrumbList = payload;
        },
    },
});

export const { setBreadcrumbList } = breadcrumbSlice.actions;
export default breadcrumbSlice.reducer;
