/**
 * @Owners haoyang
 * @Title 全局配置-redux
 */

import { type AvatarTheme, type GlobalState, type UserInfoProp } from '@/redux/interface';
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { SizeType } from 'antd/lib/config-provider/SizeContext';

const globalState: GlobalState = {
    token: '',
    userInfo: {},
    assemblySize: 'middle',
    themeConfig: {
        primary: '#1890ff',
        breadcrumb: true,
        tabs: true,
        footer: true,
    },
    avatarTheme: {},
};

const globalSlice = createSlice({
    name: 'global',
    initialState: globalState,
    reducers: {
        setToken(state: GlobalState, { payload }: PayloadAction<string>) {
            state.token = payload;
        },
        setAssemblySize(state: GlobalState, { payload }: PayloadAction<SizeType>) {
            state.assemblySize = payload;
        },
        /** 设置用户信息 */
        setUserInfo(state: GlobalState, { payload }: PayloadAction<UserInfoProp>) {
            state.userInfo = payload;
        },
        /** 设置头像主题 */
        setAvatarTheme(state: GlobalState, { payload }: PayloadAction<AvatarTheme>) {
            state.avatarTheme = payload;
        },
    },
});

export const { setToken, setAssemblySize, setUserInfo, setAvatarTheme } = globalSlice.actions;
export default globalSlice.reducer;
