/**
 * @Owners haoyang
 * @Title store
 */

import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { type TypedUseSelectorHook, useDispatch as useReduxDispatch, useSelector as useReduxSelector } from 'react-redux';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import reduxPromise from 'redux-promise';
import reduxThunk from 'redux-thunk';

import auth from './modules/auth';
import breadcrumb from './modules/breadcrumb';
import global from './modules/global';
import menu from './modules/menu';
import tabs from './modules/tabs';

// create reducer
const reducer = combineReducers({
    global,
    menu,
    tabs,
    auth,
    breadcrumb,
});

// redux persist
const persistConfig = {
    key: 'redux-state',
    storage,
};
const persistReducerConfig = persistReducer(persistConfig, reducer) as unknown as typeof reducer;

// redux middleWares
const middleWares = [reduxThunk, reduxPromise];

// store
export const store = configureStore({
    reducer: persistReducerConfig,
    middleware: middleWares,
    devTools: true,
});

// create persist store
export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useSelector: TypedUseSelectorHook<RootState> = useReduxSelector;
export const useDispatch = useReduxDispatch;
