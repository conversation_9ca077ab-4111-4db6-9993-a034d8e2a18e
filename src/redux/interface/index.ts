/**
 * @Owners haoyang
 * @Title redux-类型接口
 */

import type { SysMenu, User } from '@/api/interface/system';
import type { SizeType } from 'antd/lib/config-provider/SizeContext';

/* themeConfigProp */
export interface ThemeConfigProp {
    /** 主题颜色 */
    primary: string;
    /** 面包屑导航是否展示 */
    breadcrumb: boolean;
    /** 标签页是否展示 */
    tabs: boolean;
    /** 页脚是否展示 */
    footer: boolean;
}

/** 用户信息类型 */
export interface UserInfoType extends User.UserInfo {
    loginId: number;
    liveUserId: number;
}

export type UserInfoProp = Partial<UserInfoType>;

/** 用户头像主题 */
export interface AvatarTheme {
    /** 颜色色相 */
    colorHue?: number;
}

/* GlobalState */
export interface GlobalState {
    /** token */
    token: string;
    /** 用户信息 */
    userInfo: UserInfoProp;
    assemblySize: SizeType;
    /** 主题配置 */
    themeConfig: ThemeConfigProp;
    /** 用户头像主题 */
    avatarTheme: AvatarTheme;
}

/* MenuState */
export interface MenuState {
    isCollapse: boolean;
    menuList: SysMenu.MenuItem[];
}

/* TabsState */
export type TabListItem = Omit<SysMenu.MenuItem, 'sort'>;
export interface TabsState {
    tabsList: TabListItem[];
}

/* BreadcrumbState */
export interface BreadcrumbState {
    breadcrumbList: {
        [propName: string]: string[];
    };
}

/* AuthState */
export interface AuthState {
    authButtons: {
        [propName: string]: { [key: string]: boolean };
    };
    authRouter: string[];
}
